# Hướng dẫn Deploy Next.js lên VPS (Ubuntu)

## 1. <PERSON><PERSON><PERSON> bị VPS
- <PERSON><PERSON><PERSON> bảo VPS đã cài đặt Ubuntu (khuyến nghị 20.04 trở lên).
- <PERSON><PERSON><PERSON> nhập VPS qua SSH:
  ```bash
  ssh user@your_vps_ip
  ```

## 2. Cài đặt Node.js & npm
- Cài Node.js (khuyến nghị >= 16):
  ```bash
  curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
  sudo apt-get install -y nodejs
  node -v
  npm -v
  ```

## 3. <PERSON><PERSON><PERSON> đặt Git (nếu cần)
  ```bash
  sudo apt-get install git -y
  ```

## 4. Upload mã nguồn lên VPS
- **Cách 1:** <PERSON><PERSON> từ GitHub:
  ```bash
  git clone https://github.com/HoiAnHub/crypto-bubble-map.git
  cd crypto-bubble-map
  ```
- **Cách 2:** Upload file .zip qua SFTP/WinSCP, rồi g<PERSON><PERSON>i nén:
  ```bash
  unzip your_project.zip
  cd your_project_folder
  ```

## 5. Cài đặt các biến môi trường
- Tạo file `.env.local` (hoặc `.env.production` nếu dùng production):
  ```env
  NEXT_PUBLIC_API_URL=https://your-backend-api.com/api
  NEXT_PUBLIC_NEO4J_URI=neo4j://your-neo4j-instance:7687
  NEXT_PUBLIC_NEO4J_USER=neo4j
  NEXT_PUBLIC_NEO4J_PASSWORD=your_password
  ```

## 6. Cài đặt dependencies & build project
  ```bash
  npm install
  npm run build
  ```

## 7. Chạy ứng dụng Next.js
- Chạy production:
  ```bash
  npm start
  # hoặc
  npx next start
  ```
- Mặc định sẽ chạy ở port 3000. Có thể đổi port bằng biến môi trường `PORT`.

## 8. Cài đặt PM2 để auto start (khuyến nghị)
  ```bash
  sudo npm install -g pm2
  pm2 start npm --name "crypto-bubble-map" -- start / pm2 start npm --name "crypto-bubble-map-dev" -- run dev
  pm2 save
  pm2 startup
  # Làm theo hướng dẫn để enable auto start khi reboot VPS
  ```

## 9. Cấu hình domain với Nginx (Reverse Proxy)
- Cài đặt Nginx:
  ```bash
  sudo apt-get install nginx -y
  ```
- Tạo file cấu hình cho domain:
  ```bash
  sudo nano /etc/nginx/sites-available/projectcryptoland.com
  ```
- Thêm nội dung sau:
  ```nginx
  server {
      listen 80;
      server_name projectcryptoland.com www.projectcryptoland.com;

      location / {
          proxy_pass http://localhost:3000;
          proxy_http_version 1.1;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection 'upgrade';
          proxy_set_header Host $host;
          proxy_cache_bypass $http_upgrade;
      }
  }
  ```
- Kích hoạt cấu hình và reload Nginx:
  ```bash
  sudo ln -s /etc/nginx/sites-available/projectcryptoland.com /etc/nginx/sites-enabled/
  sudo nginx -t
  sudo systemctl reload nginx
  ```

## 10. Cài đặt SSL miễn phí với Let's Encrypt (khuyến nghị)
  ```bash
  sudo apt-get install certbot python3-certbot-nginx -y
  sudo certbot --nginx -d projectcryptoland.com -d www.projectcryptoland.com
  ```

## 11. Kiểm tra
- Truy cập `http://projectcryptoland.com` để kiểm tra website.
- Kiểm tra log nếu có lỗi:
  ```bash
  pm2 logs
  sudo journalctl -u nginx
  ```

## 12. Kết nối domain từ GoDaddy về VPS

### Bước 1: Lấy địa chỉ IP của VPS
- Đăng nhập VPS và chạy:
  ```bash
  curl ifconfig.me
  ```
- Hoặc xem trong trang quản lý VPS để lấy địa chỉ IPv4.

### Bước 2: Cấu hình DNS trên GoDaddy
1. Đăng nhập vào tài khoản GoDaddy.
2. Vào phần quản lý domain, chọn domain `projectcryptoland.com`.
3. Vào mục **DNS** hoặc **Quản lý DNS**.
4. Thêm hoặc sửa bản ghi A:
   - **Host**: @
   - **Type**: A
   - **Value**: (địa chỉ IP VPS của bạn)
   - **TTL**: 600 hoặc mặc định
5. Thêm bản ghi cho www:
   - **Host**: www
   - **Type**: A hoặc CNAME
   - **Value**: (nếu A thì là IP VPS, nếu CNAME thì là projectcryptoland.com)

### Bước 3: Cấu hình Nginx trên VPS
- Đảm bảo file cấu hình Nginx đã có:
  ```nginx
  server {
      listen 80;
      server_name projectcryptoland.com www.projectcryptoland.com;

      location / {
          proxy_pass http://localhost:3000;
          proxy_http_version 1.1;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection 'upgrade';
          proxy_set_header Host $host;
          proxy_cache_bypass $http_upgrade;
      }
  }
  ```
- Reload lại Nginx:
  ```bash
  sudo nginx -t
  sudo systemctl reload nginx
  ```

### Bước 4: Cài SSL miễn phí với Let's Encrypt (khuyến nghị)
  ```bash
  sudo apt-get install certbot python3-certbot-nginx -y
  sudo certbot --nginx -d projectcryptoland.com -d www.projectcryptoland.com
  ```

### Bước 5: Kiểm tra
- Đợi 5-15 phút để DNS cập nhật.
- Truy cập http://projectcryptoland.com hoặc https://projectcryptoland.com để kiểm tra website.

---

**Nếu gặp lỗi, kiểm tra lại cấu hình DNS, Nginx và log hệ thống.**

**Chúc bạn deploy thành công! Nếu cần hỗ trợ, hãy liên hệ admin hoặc support.**