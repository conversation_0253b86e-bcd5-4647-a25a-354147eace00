/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx}",
    "./src/components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    screens: {
      'xs': '475px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      colors: {
        // BubbleMaps-inspired color scheme
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },
        secondary: {
          50: '#ecfdf5',
          100: '#d1fae5',
          200: '#a7f3d0',
          300: '#6ee7b7',
          400: '#34d399',
          500: '#10b981',
          600: '#059669',
          700: '#047857',
          800: '#065f46',
          900: '#064e3b',
        },
        accent: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7',
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
        },
        // Dark theme backgrounds
        background: {
          DEFAULT: '#0a0e1a',
          secondary: '#0f1419',
          tertiary: '#1a1f2e',
          card: '#141b2d',
          glass: 'rgba(20, 27, 45, 0.8)',
        },
        foreground: {
          DEFAULT: '#e2e8f0',
          secondary: '#cbd5e1',
          muted: '#94a3b8',
          accent: '#f1f5f9',
        },
        // Modern 2024 color scheme with enhanced vibrancy and accessibility
        bubble: {
          // Primary bubble colors - Modern and vibrant
          blue: '#2563EB',      // Modern blue with better contrast
          green: '#10B981',     // Emerald green for growth/positive
          purple: '#8B5CF6',    // Vibrant purple for contracts
          pink: '#EC4899',      // Hot pink for DeFi protocols
          orange: '#F59E0B',    // Amber orange for bridges
          red: '#EF4444',       // Modern red for flagged/risk
          cyan: '#06B6D4',      // Cyan for whales/large holders
          indigo: '#6366F1',    // Indigo for special addresses

          // Specialized node types with modern colors
          whale: '#0EA5E9',     // Sky blue for large holders
          miner: '#F97316',     // Orange for mining operations
          exchange: '#22C55E',  // Green for exchanges
          defi: '#A855F7',      // Purple for DeFi protocols
          bridge: '#F59E0B',    // Amber for cross-chain bridges
          contract: '#8B5CF6',  // Violet for smart contracts

          // Enhanced glow variants for modern effects
          glowBlue: '#60A5FA',
          glowGreen: '#4ADE80',
          glowPurple: '#A78BFA',
          glowPink: '#F472B6',
          glowOrange: '#FBBF24',
          glowRed: '#F87171',
          glowCyan: '#22D3EE',
          glowIndigo: '#818CF8',

          // Modern gradient stops
          gradientStart: '#3B82F6',
          gradientMid: '#8B5CF6',
          gradientEnd: '#EC4899',
        },
        // Border and accent colors
        border: {
          DEFAULT: 'rgba(148, 163, 184, 0.1)',
          secondary: 'rgba(148, 163, 184, 0.2)',
          accent: 'rgba(139, 92, 246, 0.3)',
        },
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',

        // Modern 2024 brand gradients with enhanced vibrancy
        'gradient-brand': 'linear-gradient(135deg, #2563EB 0%, #8B5CF6 25%, #EC4899 50%, #F59E0B 75%, #10B981 100%)',
        'gradient-brand-subtle': 'linear-gradient(135deg, rgba(37, 99, 235, 0.08) 0%, rgba(139, 92, 246, 0.08) 25%, rgba(236, 72, 153, 0.08) 50%, rgba(245, 158, 11, 0.08) 75%, rgba(16, 185, 129, 0.08) 100%)',

        // Enhanced glass morphism cards
        'gradient-card': 'linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(15, 23, 42, 0.95) 100%)',
        'gradient-card-hover': 'linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(15, 23, 42, 0.95) 30%, rgba(30, 41, 59, 0.9) 100%)',

        // Modern bubble gradients with depth
        'gradient-bubble-blue': 'radial-gradient(circle at 30% 30%, #60A5FA 0%, #2563EB 50%, #1E40AF 100%)',
        'gradient-bubble-green': 'radial-gradient(circle at 30% 30%, #4ADE80 0%, #10B981 50%, #047857 100%)',
        'gradient-bubble-purple': 'radial-gradient(circle at 30% 30%, #A78BFA 0%, #8B5CF6 50%, #7C3AED 100%)',
        'gradient-bubble-pink': 'radial-gradient(circle at 30% 30%, #F472B6 0%, #EC4899 50%, #BE185D 100%)',
        'gradient-bubble-orange': 'radial-gradient(circle at 30% 30%, #FBBF24 0%, #F59E0B 50%, #D97706 100%)',
        'gradient-bubble-cyan': 'radial-gradient(circle at 30% 30%, #22D3EE 0%, #06B6D4 50%, #0891B2 100%)',

        // Dynamic background mesh with modern colors
        'gradient-mesh': `
          radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.12) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.12) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 60% 60%, rgba(236, 72, 153, 0.06) 0%, transparent 50%),
          radial-gradient(circle at 90% 10%, rgba(245, 158, 11, 0.04) 0%, transparent 50%)
        `,

        // Animated gradient for special effects
        'gradient-animated': 'linear-gradient(-45deg, #2563EB, #8B5CF6, #EC4899, #F59E0B, #10B981)',
      },
      backdropBlur: {
        xs: '2px',
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-slow': 'bounce 2s infinite',
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      boxShadow: {
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
        'glow': '0 0 20px rgba(139, 92, 246, 0.3)',
        'bubble': '0 4px 20px rgba(0, 0, 0, 0.3)',
      },
    },
  },
  plugins: [],
}