import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import performance components with no SSR
const PerformanceMonitor = dynamic(() => import('./PerformanceMonitor'), {
  ssr: false,
  loading: () => null
});

const PerformanceConfigPanel = dynamic(() => import('./PerformanceConfigPanel'), {
  ssr: false,
  loading: () => null
});

interface ClientOnlyPerformanceProps {
  showPerformanceMonitor: boolean;
  showPerformanceConfig: boolean;
  onOpenSettings: () => void;
  onCloseSettings: () => void;
  onConfigChange: (config: any) => void;
}

const ClientOnlyPerformance: React.FC<ClientOnlyPerformanceProps> = ({
  showPerformanceMonitor,
  showPerformanceConfig,
  onOpenSettings,
  onCloseSettings,
  onConfigChange
}) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render anything during SSR or before hydration
  if (!isMounted) {
    return null;
  }

  return (
    <>
      {/* Performance Monitor */}
      {showPerformanceMonitor && (
        <PerformanceMonitor
          onOpenSettings={onOpenSettings}
        />
      )}

      {/* Performance Configuration Panel */}
      <PerformanceConfigPanel
        isVisible={showPerformanceConfig}
        onClose={onCloseSettings}
        onConfigChange={onConfigChange}
      />
    </>
  );
};

export default ClientOnlyPerformance;
