import React, { useState, useEffect } from 'react';
import {
  FaCog,
  FaTimes,
  FaDesktop,
  FaMobile,
  FaTabletAlt,
  FaChartLine,
  FaExclamationTriangle,
  FaInfoCircle,
  FaRedo,
  FaSave
} from 'react-icons/fa';
// Performance manager will be imported dynamically
type PerformanceConfig = any;
type DeviceCapabilities = any;
type PerformanceMetrics = any;
import Tooltip from './Tooltip';

interface PerformanceConfigPanelProps {
  isVisible: boolean;
  onClose: () => void;
  onConfigChange?: (config: PerformanceConfig) => void;
}

const PerformanceConfigPanel: React.FC<PerformanceConfigPanelProps> = ({
  isVisible,
  onClose,
  onConfigChange
}) => {
  const [isMounted, setIsMounted] = useState(false);
  const [config, setConfig] = useState<PerformanceConfig | null>(null);
  const [capabilities, setCapabilities] = useState<DeviceCapabilities | null>(null);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Client-side initialization
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsMounted(true);

      // Dynamically import performance manager
      import('@/utils/performanceManager').then(({ performanceManager }) => {
        setConfig(performanceManager.getConfig());
        setCapabilities(performanceManager.getCapabilities());
        setMetrics(performanceManager.getMetrics());
        setRecommendations(performanceManager.getRecommendations());
      }).catch(error => {
        console.warn('Failed to load performance manager:', error);
      });
    }
  }, []);

  useEffect(() => {
    if (isVisible && isMounted) {
      // Update data when panel opens
      import('@/utils/performanceManager').then(({ performanceManager }) => {
        setConfig(performanceManager.getConfig());
        setCapabilities(performanceManager.getCapabilities());
        setMetrics(performanceManager.getMetrics());
        setRecommendations(performanceManager.getRecommendations());
        setHasUnsavedChanges(false);

        // Set up metrics update interval
        const interval = setInterval(async () => {
          try {
            const { performanceManager: pm } = await import('@/utils/performanceManager');
            setMetrics(pm.getMetrics());
            setRecommendations(pm.getRecommendations());
          } catch (error) {
            console.warn('Failed to update metrics:', error);
          }
        }, 1000);

        return () => clearInterval(interval);
      }).catch(error => {
        console.warn('Failed to load performance manager:', error);
      });
    }
  }, [isVisible, isMounted]);

  const handleConfigChange = (key: keyof PerformanceConfig, value: any) => {
    if (!config) return;
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    setHasUnsavedChanges(true);
  };

  const handleSave = async () => {
    if (!config) return;
    try {
      const { performanceManager } = await import('@/utils/performanceManager');
      performanceManager.updateConfig(config);
      onConfigChange?.(config);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.warn('Failed to save config:', error);
    }
  };

  const handleReset = async () => {
    try {
      const { performanceManager } = await import('@/utils/performanceManager');
      performanceManager.resetToOptimal();
      const optimalConfig = performanceManager.getConfig();
      setConfig(optimalConfig);
      onConfigChange?.(optimalConfig);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.warn('Failed to reset config:', error);
    }
  };

  const getDeviceIcon = () => {
    if (!capabilities) return <FaDesktop />;
    if (capabilities.isMobile) return <FaMobile />;
    if (capabilities.isTablet) return <FaTabletAlt />;
    return <FaDesktop />;
  };

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return 'text-green-400';
    if (value >= thresholds.warning) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getQualityPresets = () => ({
    low: {
      renderingQuality: 'low' as const,
      maxNodes: 50,
      frameRateLimit: 30 as const,
      particleDensity: 'off' as const,
      enableShadows: false,
      enableGlow: false,
      enableParticles: false
    },
    medium: {
      renderingQuality: 'medium' as const,
      maxNodes: 150,
      frameRateLimit: 60 as const,
      particleDensity: 'medium' as const,
      enableShadows: true,
      enableGlow: true,
      enableParticles: true
    },
    high: {
      renderingQuality: 'high' as const,
      maxNodes: 300,
      frameRateLimit: 60 as const,
      particleDensity: 'high' as const,
      enableShadows: true,
      enableGlow: true,
      enableParticles: true
    },
    ultra: {
      renderingQuality: 'ultra' as const,
      maxNodes: 500,
      frameRateLimit: 120 as const,
      particleDensity: 'high' as const,
      enableShadows: true,
      enableGlow: true,
      enableParticles: true
    }
  });

  const applyPreset = (preset: keyof ReturnType<typeof getQualityPresets>) => {
    const presets = getQualityPresets();
    const presetConfig = presets[preset];
    const newConfig = { ...config, ...presetConfig };
    setConfig(newConfig);
    setHasUnsavedChanges(true);
  };

  if (!isVisible || !isMounted || !config || !capabilities || !metrics) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9000] flex items-center justify-center p-2 sm:p-4">
      <div className="glass-card rounded-xl border border-border-accent shadow-bubble w-full max-w-xs sm:max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border-accent bg-gradient-to-r from-background-secondary/40 to-background-tertiary/30">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
              <FaCog className="text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-foreground">Performance Settings</h2>
              <p className="text-sm text-foreground-muted">Optimize application performance for your device</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-background-tertiary rounded-lg transition-colors"
          >
            <FaTimes className="text-foreground-muted" />
          </button>
        </div>

        <div className="flex flex-col lg:flex-row h-[calc(95vh-120px)] sm:h-[calc(90vh-120px)]">
          {/* Left Panel - Settings */}
          <div className="flex-1 p-3 sm:p-6 overflow-y-auto">
            {/* Quick Presets */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-foreground mb-3">Quick Presets</h3>
              <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-4 gap-3">
                {Object.entries(getQualityPresets()).map(([preset, _]) => (
                  <button
                    key={preset}
                    onClick={() => applyPreset(preset as any)}
                    className={`p-3 rounded-lg border transition-all duration-200 ${
                      config.renderingQuality === preset
                        ? 'border-accent-500 bg-accent-500/20 text-accent-400'
                        : 'border-border-accent bg-background-secondary/50 text-foreground-muted hover:border-accent-500/50'
                    }`}
                  >
                    <div className="text-sm font-medium capitalize">{preset}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Rendering Settings */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-foreground mb-3">Rendering Settings</h3>
              <div className="space-y-4">
                {/* Rendering Quality */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Rendering Quality
                  </label>
                  <select
                    value={config.renderingQuality}
                    onChange={(e) => handleConfigChange('renderingQuality', e.target.value)}
                    className="w-full p-2 bg-background-secondary border border-border-accent rounded-lg text-foreground"
                  >
                    <option value="low">Low - Best Performance</option>
                    <option value="medium">Medium - Balanced</option>
                    <option value="high">High - Best Quality</option>
                    <option value="ultra">Ultra - Maximum Quality</option>
                  </select>
                </div>

                {/* Max Nodes */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Maximum Nodes: {config.maxNodes}
                  </label>
                  <input
                    type="range"
                    min="25"
                    max="500"
                    step="25"
                    value={config.maxNodes}
                    onChange={(e) => handleConfigChange('maxNodes', parseInt(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-foreground-muted mt-1">
                    <span>25</span>
                    <span>500</span>
                  </div>
                </div>

                {/* Frame Rate Limit */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Frame Rate Limit
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {[30, 60, 120].map((fps) => (
                      <button
                        key={fps}
                        onClick={() => handleConfigChange('frameRateLimit', fps)}
                        className={`px-2 sm:px-3 py-2 rounded-lg border transition-colors text-sm ${
                          config.frameRateLimit === fps
                            ? 'border-accent-500 bg-accent-500/20 text-accent-400'
                            : 'border-border-accent bg-background-secondary text-foreground-muted hover:border-accent-500/50'
                        }`}
                      >
                        {fps} FPS
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Visual Effects */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-foreground mb-3">Visual Effects</h3>
              <div className="space-y-3">
                {/* Particle Density */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Particle System
                  </label>
                  <select
                    value={config.particleDensity}
                    onChange={(e) => handleConfigChange('particleDensity', e.target.value)}
                    className="w-full p-2 bg-background-secondary border border-border-accent rounded-lg text-foreground"
                  >
                    <option value="off">Disabled</option>
                    <option value="low">Low Density</option>
                    <option value="medium">Medium Density</option>
                    <option value="high">High Density</option>
                  </select>
                </div>

                {/* Effect Toggles */}
                <div className="grid grid-cols-1 xs:grid-cols-2 gap-3">
                  {[
                    { key: 'enableShadows', label: 'Shadows' },
                    { key: 'enableGlow', label: 'Glow Effects' },
                    { key: 'enableParticles', label: 'Particles' },
                    { key: 'enableAnimations', label: 'Animations' }
                  ].map(({ key, label }) => (
                    <label key={key} className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={config[key as keyof PerformanceConfig] as boolean}
                        onChange={(e) => handleConfigChange(key as keyof PerformanceConfig, e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm text-foreground">{label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Memory & Data Settings */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-foreground mb-3">Memory & Data</h3>
              <div className="space-y-4">
                {/* Data Cache Limit */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Data Cache Limit: {config.dataCacheLimit} MB
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="200"
                    step="10"
                    value={config.dataCacheLimit}
                    onChange={(e) => handleConfigChange('dataCacheLimit', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>

                {/* Real-time Update Interval */}
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Update Interval: {config.realTimeUpdateInterval / 1000}s
                  </label>
                  <input
                    type="range"
                    min="1000"
                    max="30000"
                    step="1000"
                    value={config.realTimeUpdateInterval}
                    onChange={(e) => handleConfigChange('realTimeUpdateInterval', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            {/* Advanced Options */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-foreground mb-3">Advanced Options</h3>
              <div className="space-y-3">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={config.lodEnabled}
                    onChange={(e) => handleConfigChange('lodEnabled', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm text-foreground">Level of Detail (LOD)</span>
                  <Tooltip content="Reduces detail of distant objects to improve performance">
                    <FaInfoCircle className="text-foreground-muted text-xs" />
                  </Tooltip>
                </label>

                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={config.cullingEnabled}
                    onChange={(e) => handleConfigChange('cullingEnabled', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm text-foreground">Frustum Culling</span>
                  <Tooltip content="Hides objects outside the visible area">
                    <FaInfoCircle className="text-foreground-muted text-xs" />
                  </Tooltip>
                </label>

                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={config.autoOptimize}
                    onChange={(e) => handleConfigChange('autoOptimize', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm text-foreground">Auto-Optimization</span>
                  <Tooltip content="Automatically adjusts settings based on performance">
                    <FaInfoCircle className="text-foreground-muted text-xs" />
                  </Tooltip>
                </label>
              </div>
            </div>
          </div>

          {/* Right Panel - Metrics & Device Info */}
          <div className="w-full lg:w-80 border-t lg:border-t-0 lg:border-l border-border-accent p-3 sm:p-6 bg-background-secondary/20 max-h-64 lg:max-h-none overflow-y-auto lg:overflow-y-visible">
            {/* Device Information */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-foreground mb-3 flex items-center gap-2">
                {getDeviceIcon()}
                Device Information
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-foreground-muted">GPU:</span>
                  <span className={`font-medium ${
                    capabilities.gpu === 'ultra' ? 'text-green-400' :
                    capabilities.gpu === 'high' ? 'text-blue-400' :
                    capabilities.gpu === 'medium' ? 'text-yellow-400' : 'text-red-400'
                  }`}>
                    {capabilities.gpu.toUpperCase()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Memory:</span>
                  <span className="text-foreground">{capabilities.memory} GB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Resolution:</span>
                  <span className="text-foreground">
                    {capabilities.screenResolution.width}×{capabilities.screenResolution.height}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-foreground-muted">Browser Score:</span>
                  <span className={getPerformanceColor(capabilities.browserScore, { good: 80, warning: 60 })}>
                    {capabilities.browserScore}/100
                  </span>
                </div>
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-foreground mb-3 flex items-center gap-2">
                <FaChartLine />
                Performance Metrics
              </h3>
              <div className="space-y-3">
                <div className="glass-card p-3 rounded-lg">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-foreground-muted">FPS</span>
                    <span className={`font-bold ${getPerformanceColor(metrics.fps, { good: 50, warning: 30 })}`}>
                      {metrics.fps.toFixed(1)}
                    </span>
                  </div>
                  <div className="w-full bg-background-tertiary rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        metrics.fps >= 50 ? 'bg-green-400' :
                        metrics.fps >= 30 ? 'bg-yellow-400' : 'bg-red-400'
                      }`}
                      style={{ width: `${Math.min((metrics.fps / config.frameRateLimit) * 100, 100)}%` }}
                    />
                  </div>
                </div>

                <div className="glass-card p-3 rounded-lg">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-foreground-muted">Memory</span>
                    <span className={`font-bold ${getPerformanceColor(100 - metrics.memoryUsage, { good: 50, warning: 25 })}`}>
                      {metrics.memoryUsage.toFixed(1)} MB
                    </span>
                  </div>
                </div>

                <div className="glass-card p-3 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-foreground-muted">Nodes</span>
                    <span className="font-bold text-foreground">{metrics.nodeCount}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Recommendations */}
            {recommendations.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-foreground mb-3 flex items-center gap-2">
                  <FaExclamationTriangle className="text-yellow-400" />
                  Recommendations
                </h3>
                <div className="space-y-2">
                  {recommendations.map((rec, index) => (
                    <div key={index} className="glass-card p-3 rounded-lg border-l-4 border-yellow-400">
                      <p className="text-xs text-foreground-muted">{rec}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-border-accent bg-background-secondary/20">
          <div className="flex items-center gap-2">
            {hasUnsavedChanges && (
              <div className="flex items-center gap-2 text-yellow-400">
                <FaExclamationTriangle size={14} />
                <span className="text-sm">Unsaved changes</span>
              </div>
            )}
          </div>
          <div className="flex gap-3">
            <button
              onClick={handleReset}
              className="px-4 py-2 bg-background-tertiary text-foreground-muted rounded-lg hover:bg-background-secondary transition-colors flex items-center gap-2"
            >
              <FaRedo size={14} />
              Reset to Optimal
            </button>
            <button
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
              className="px-4 py-2 bg-accent-500 text-white rounded-lg hover:bg-accent-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              <FaSave size={14} />
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceConfigPanel;
