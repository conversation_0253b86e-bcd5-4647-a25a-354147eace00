import React from 'react';
import Tooltip from './Tooltip';
import { Fa<PERSON>og, FaInfo, FaUser, FaHome } from 'react-icons/fa';

const TooltipTest: React.FC = () => {
  return (
    <div className="fixed top-4 left-4 z-50 bg-background-secondary p-4 rounded-lg border border-border-accent">
      <h3 className="text-sm font-bold text-foreground mb-4">Tooltip Test</h3>
      <div className="grid grid-cols-2 gap-4">
        {/* Test basic tooltip */}
        <Tooltip content="This is a basic tooltip" position="top">
          <button className="p-2 bg-blue-500 text-white rounded">
            <FaInfo />
          </button>
        </Tooltip>

        {/* Test auto positioning */}
        <Tooltip content="Auto positioning tooltip" position="auto">
          <button className="p-2 bg-green-500 text-white rounded">
            <FaCog />
          </button>
        </Tooltip>

        {/* Test with shortcut */}
        <Tooltip 
          content="Tooltip with shortcut" 
          showShortcut="Ctrl+S"
          position="auto"
        >
          <button className="p-2 bg-purple-500 text-white rounded">
            <FaUser />
          </button>
        </Tooltip>

        {/* Test Vietnamese content */}
        <Tooltip 
          content="Home button" 
          contentVi="Nút trang chủ"
          position="auto"
        >
          <button className="p-2 bg-orange-500 text-white rounded">
            <FaHome />
          </button>
        </Tooltip>
      </div>
    </div>
  );
};

export default TooltipTest;
