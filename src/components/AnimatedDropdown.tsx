import React, { useState, useRef, useEffect } from 'react';
import { FaChevronDown, FaCheck, FaSearch } from 'react-icons/fa';

interface DropdownOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
  description?: string;
}

interface AnimatedDropdownProps {
  options: DropdownOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  maxHeight?: string;
  searchable?: boolean;
  compact?: boolean;
  showDescriptions?: boolean;
}

const AnimatedDropdown: React.FC<AnimatedDropdownProps> = ({
  options,
  value,
  onChange,
  placeholder = "Select option",
  className = "",
  disabled = false,
  maxHeight = "200px",
  searchable = false,
  compact = false,
  showDescriptions = true
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Find selected option
  const selectedOption = options.find(option => option.value === value);

  // Filter options based on search
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (option.description && option.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle option selection
  const handleOptionSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
    setSearchTerm('');
    setFocusedIndex(-1);
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!isOpen) {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        setIsOpen(true);
      }
      return;
    }

    switch (event.key) {
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        setFocusedIndex(-1);
        break;
      case 'ArrowDown':
        event.preventDefault();
        setFocusedIndex(prev =>
          prev < filteredOptions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        setFocusedIndex(prev =>
          prev > 0 ? prev - 1 : filteredOptions.length - 1
        );
        break;
      case 'Enter':
        event.preventDefault();
        if (focusedIndex >= 0 && filteredOptions[focusedIndex]) {
          handleOptionSelect(filteredOptions[focusedIndex].value);
        }
        break;
    }
  };

  // Auto-focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen, searchable]);

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      {/* Dropdown Trigger */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        className={`
          w-full bg-slate-700 border border-slate-600 rounded-lg text-white
          focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20
          transition-all duration-300 hover:border-purple-400 hover:shadow-lg hover:shadow-purple-500/10
          hover:bg-slate-600/80 hover:transform hover:scale-[1.01]
          flex items-center justify-between group relative overflow-hidden
          ${compact ? 'px-2 py-1.5 text-sm' : 'px-3 py-2'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${isOpen ? 'border-purple-500 ring-2 ring-purple-500/20 shadow-lg shadow-purple-500/20 bg-slate-600/50' : ''}
        `}
      >
        {/* Subtle gradient overlay on hover */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        <div className="flex items-center gap-2 flex-1 text-left min-w-0">
          {selectedOption?.icon && (
            <span className={`text-purple-400 transition-colors duration-200 flex-shrink-0 ${compact ? 'text-sm' : ''}`}>
              {selectedOption.icon}
            </span>
          )}
          <div className="flex flex-col min-w-0 flex-1">
            <span className={`truncate ${selectedOption ? 'text-white' : 'text-slate-400'} ${compact ? 'text-sm' : ''}`}>
              {selectedOption?.label || placeholder}
            </span>
            {selectedOption?.description && showDescriptions && !compact && (
              <span className="text-xs text-slate-500 truncate">
                {selectedOption.description.length > 40
                  ? `${selectedOption.description.substring(0, 40)}...`
                  : selectedOption.description
                }
              </span>
            )}
          </div>
        </div>

        <FaChevronDown
          className={`text-slate-400 transition-all duration-300 group-hover:text-purple-400 ${
            isOpen ? 'rotate-180 text-purple-400' : ''
          }`}
          size={12}
        />
      </button>

      {/* Dropdown Menu with Animation */}
      {isOpen && (
        <div
          className={`
            absolute z-50 w-full mt-1 bg-slate-800 border border-slate-600 rounded-lg
            shadow-2xl shadow-black/50 overflow-hidden backdrop-blur-sm
            transform transition-all duration-300 ease-out
            animate-in slide-in-from-top-2 fade-in
            ${compact ? 'text-sm' : ''}
          `}
          style={{
            background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.98) 100%)',
            backdropFilter: 'blur(16px)',
            border: '1px solid rgba(139, 92, 246, 0.2)',
            animation: `dropdownSlide ${compact ? '0.2s' : '0.3s'} cubic-bezier(0.16, 1, 0.3, 1) forwards`,
            minWidth: compact ? '120px' : '200px'
          }}
        >
          {/* Search Input */}
          {searchable && (
            <div className={`border-b border-slate-600/50 ${compact ? 'p-2' : 'p-3'}`}>
              <div className="relative">
                <FaSearch className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-slate-400" size={10} />
                <input
                  ref={inputRef}
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className={`w-full bg-slate-700/50 border border-slate-600 rounded text-white placeholder-slate-400 focus:outline-none focus:border-purple-500 focus:ring-1 focus:ring-purple-500/30 transition-all duration-200 ${
                    compact ? 'pl-7 pr-2 py-1 text-xs' : 'pl-9 pr-3 py-2 text-sm'
                  }`}
                />
              </div>
            </div>
          )}

          {/* Options List */}
          <div
            className="overflow-y-auto scrollable-container"
            style={{ maxHeight }}
          >
            {filteredOptions.length === 0 ? (
              <div className={`text-center animate-in fade-in duration-300 ${compact ? 'px-3 py-4' : 'px-4 py-6'}`}>
                <div className={`text-slate-400 mb-1 ${compact ? 'text-xs' : 'text-sm'}`}>No options found</div>
                {!compact && (
                  <div className="text-slate-500 text-xs">Try adjusting your search</div>
                )}
              </div>
            ) : (
              filteredOptions.map((option, index) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => handleOptionSelect(option.value)}
                  onMouseEnter={() => setFocusedIndex(index)}
                  className={`
                    w-full text-left transition-all duration-300 ease-out
                    flex items-center justify-between group hover:bg-slate-700/50
                    transform hover:scale-[1.01] hover:translate-x-0.5
                    ${compact ? 'px-3 py-2' : 'px-4 py-3'}
                    ${option.value === value ? 'bg-purple-500/20 text-purple-400 border-r-2 border-purple-400 shadow-lg shadow-purple-500/10' : 'text-white'}
                    ${focusedIndex === index ? 'bg-slate-700/30 scale-[1.005] translate-x-0.5' : ''}
                  `}
                  style={{
                    animationDelay: `${index * 30}ms`,
                    animation: 'optionSlideIn 0.2s ease-out forwards'
                  }}
                >
                  <div className={`flex items-center flex-1 min-w-0 ${compact ? 'gap-2' : 'gap-3'}`}>
                    {option.icon && (
                      <span className={`transition-all duration-300 transform group-hover:scale-110 flex-shrink-0 ${
                        option.value === value ? 'text-purple-400' : 'text-slate-400 group-hover:text-purple-400'
                      } ${compact ? 'text-sm' : ''}`}>
                        {option.icon}
                      </span>
                    )}
                    <div className="flex flex-col min-w-0 flex-1">
                      <span className={`transition-colors duration-200 truncate ${compact ? 'text-sm' : 'font-medium'}`}>
                        {option.label}
                      </span>
                      {option.description && showDescriptions && !compact && (
                        <span className="text-xs text-slate-500 mt-0.5 transition-colors duration-200 group-hover:text-slate-400 truncate">
                          {option.description.length > 50
                            ? `${option.description.substring(0, 50)}...`
                            : option.description
                          }
                        </span>
                      )}
                      {option.description && compact && showDescriptions && (
                        <span
                          className="text-xs text-slate-500 truncate cursor-help"
                          title={option.description}
                        >
                          {option.description.length > 20
                            ? `${option.description.substring(0, 20)}...`
                            : option.description
                          }
                        </span>
                      )}
                    </div>
                  </div>

                  {option.value === value && (
                    <div className="relative flex-shrink-0">
                      <FaCheck className={`text-purple-400 transition-all duration-300 transform group-hover:scale-110 ${compact ? 'text-xs' : ''}`} size={compact ? 10 : 12} />
                      {!compact && (
                        <div className="absolute inset-0 bg-purple-400 rounded-full opacity-20 animate-ping"></div>
                      )}
                    </div>
                  )}
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AnimatedDropdown;
