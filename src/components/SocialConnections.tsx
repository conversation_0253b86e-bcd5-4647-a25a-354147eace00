import React, { useState } from 'react';
import {
  FaD<PERSON>rd,
  FaTelegram,
  FaGithub,
  FaGlobe,
  FaLinkedin,
  FaMedium,
  FaReddit,
  FaExternalLinkAlt,
  FaShieldAlt,
  FaCheckCircle,
  FaInfoCircle
} from 'react-icons/fa';
import { SocialProfiles } from '@/services/neo4jService';

// Custom X (Twitter) Logo Component
const XLogo: React.FC<{ size?: number; className?: string }> = ({ size = 16, className = '' }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className}
  >
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

interface SocialConnectionsProps {
  socialProfiles?: SocialProfiles;
  hasVerifiedSocials?: boolean;
  socialScore?: number;
  className?: string;
}

const SocialConnections: React.FC<SocialConnectionsProps> = ({
  socialProfiles,
  hasVerifiedSocials = false,
  socialScore = 0,
  className = ''
}) => {
  const [copiedPlatform, setCopiedPlatform] = useState<string | null>(null);

  // Social platform configurations - Minimal design
  const socialPlatforms = [
    {
      key: 'twitter',
      name: 'X',
      icon: XLogo,
      getUrl: (handle: string) => `https://x.com/${handle.replace('@', '')}`
    },
    {
      key: 'discord',
      name: 'Discord',
      icon: FaDiscord,
      getUrl: (handle: string) => null, // Discord doesn't have direct URLs
      copyable: true
    },
    {
      key: 'telegram',
      name: 'Telegram',
      icon: FaTelegram,
      getUrl: (handle: string) => `https://t.me/${handle.replace('@', '')}`
    },
    {
      key: 'github',
      name: 'GitHub',
      icon: FaGithub,
      getUrl: (handle: string) => handle.startsWith('http') ? handle : `https://github.com/${handle}`
    },
    {
      key: 'website',
      name: 'Website',
      icon: FaGlobe,
      getUrl: (handle: string) => handle.startsWith('http') ? handle : `https://${handle}`
    },
    {
      key: 'linkedin',
      name: 'LinkedIn',
      icon: FaLinkedin,
      getUrl: (handle: string) => handle.startsWith('http') ? handle : `https://linkedin.com/in/${handle}`
    },
    {
      key: 'medium',
      name: 'Medium',
      icon: FaMedium,
      getUrl: (handle: string) => `https://medium.com/${handle.replace('@', '')}`
    },
    {
      key: 'reddit',
      name: 'Reddit',
      icon: FaReddit,
      getUrl: (handle: string) => `https://reddit.com/${handle.startsWith('u/') ? handle : `u/${handle}`}`
    }
  ];

  const handleCopy = async (text: string, platform: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedPlatform(platform);
      setTimeout(() => setCopiedPlatform(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleSocialClick = (platform: any, handle: string) => {
    if (platform.copyable) {
      handleCopy(handle, platform.key);
    } else {
      const url = platform.getUrl(handle);
      if (url) {
        window.open(url, '_blank', 'noopener,noreferrer');
      }
    }
  };



  if (!socialProfiles || Object.keys(socialProfiles).length === 0) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-foreground">Social Connections</span>
        </div>
        <div className="p-3 rounded-lg bg-slate-700/30 border border-slate-600/50">
          <div className="text-xs text-foreground-muted text-center">
            No social media profiles found
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Header - Minimal */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-foreground">Social Connections</span>
          {hasVerifiedSocials && (
            <FaCheckCircle className="text-green-400" size={12} title="Verified Social Presence" />
          )}
        </div>

        {/* Connection Count */}
        <div className="px-2 py-1 rounded-lg bg-slate-700/50 border border-slate-600/50 text-xs font-medium text-slate-300">
          {Object.keys(socialProfiles).length} platform{Object.keys(socialProfiles).length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Social Links Grid - Minimal Design */}
      <div className="flex flex-wrap gap-2">
        {socialPlatforms
          .filter(platform => socialProfiles[platform.key as keyof SocialProfiles])
          .map(platform => {
            const handle = socialProfiles[platform.key as keyof SocialProfiles]!;
            const Icon = platform.icon;
            const isCopied = copiedPlatform === platform.key;

            return (
              <button
                key={platform.key}
                onClick={() => handleSocialClick(platform, handle)}
                className="flex items-center gap-2 px-3 py-2 rounded-lg bg-slate-700/50 border border-slate-600/50 text-slate-300 hover:text-white hover:bg-slate-600/50 hover:border-slate-500/50 text-xs font-medium transition-all duration-200"
                title={platform.copyable ? `Copy ${platform.name}: ${handle}` : `Open ${platform.name}: ${handle}`}
              >
                <Icon size={16} />
                <span className="font-medium">{platform.name}</span>
                {platform.copyable ? (
                  <span className="text-xs opacity-75">
                    {isCopied ? '✓' : 'Copy'}
                  </span>
                ) : (
                  <FaExternalLinkAlt size={10} className="opacity-75" />
                )}
              </button>
            );
          })}
      </div>
    </div>
  );
};

export default SocialConnections;
