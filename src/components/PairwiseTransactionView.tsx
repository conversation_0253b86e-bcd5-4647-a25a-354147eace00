import React, { useState, useEffect } from 'react';
import { FaLink, FaExchangeAlt, FaEthereum, FaClock, FaGasPump, FaShieldAlt, FaExternalLinkAlt, FaFilter, FaDownload, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { PairwiseTransaction, PairwiseTransactionSummary } from '@/services/neo4jService';

interface PairwiseTransactionViewProps {
  sourceWallet: {
    id: string;
    address: string;
    label?: string;
  };
  targetWallet: {
    id: string;
    address: string;
    label?: string;
  };
}

const PairwiseTransactionView: React.FC<PairwiseTransactionViewProps> = ({
  sourceWallet,
  targetWallet
}) => {
  const [transactions, setTransactions] = useState<PairwiseTransaction[]>([]);
  const [summary, setSummary] = useState<PairwiseTransactionSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'incoming' | 'outgoing'>('all');
  const [tokenFilter, setTokenFilter] = useState<string>('all');
  const [riskFilter, setRiskFilter] = useState<string>('all');
  const [expandedTx, setExpandedTx] = useState<string | null>(null);

  useEffect(() => {
    generateMockData();
  }, [sourceWallet, targetWallet]);

  const generateMockData = () => {
    setLoading(true);

    // Generate mock transactions
    const mockTransactions: PairwiseTransaction[] = Array.from({ length: 12 }, (_, i) => {
      const isIncoming = i % 2 === 0;
      const tokens = ['ETH', 'USDC', 'USDT', 'DAI', 'WETH', 'UNI'];
      const token = tokens[i % tokens.length];
      const transactionTypes = ['transfer', 'swap', 'deposit', 'withdraw', 'contract_call'] as const;
      const riskLevels = ['low', 'medium', 'high', 'critical'] as const;

      return {
        id: `tx_${i}`,
        hash: `0x${Math.random().toString(16).substr(2, 64)}`,
        from: isIncoming ? targetWallet.address : sourceWallet.address,
        to: isIncoming ? sourceWallet.address : targetWallet.address,
        value: Math.random() * 100,
        token: token,
        tokenSymbol: token,
        tokenDecimals: token === 'USDC' || token === 'USDT' ? 6 : 18,
        usdValue: Math.random() * 50000,
        timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        blockNumber: 18000000 + Math.floor(Math.random() * 100000),
        gasUsed: 21000 + Math.floor(Math.random() * 200000),
        gasPrice: Math.random() * 50,
        gasFee: Math.random() * 0.01,
        transactionType: transactionTypes[Math.floor(Math.random() * transactionTypes.length)],
        method: ['Transfer', 'Swap', 'Deposit', 'Withdraw', 'Execute'][Math.floor(Math.random() * 5)],
        riskLevel: riskLevels[Math.floor(Math.random() * riskLevels.length)],
        riskFactors: Math.random() > 0.7 ? ['High Volume', 'Unusual Pattern'] : [],
        status: Math.random() > 0.05 ? 'success' : 'failed',
        direction: isIncoming ? 'incoming' : 'outgoing',
        isInternal: Math.random() > 0.8,
        contractAddress: Math.random() > 0.6 ? `0x${Math.random().toString(16).substr(2, 40)}` : undefined
      } as PairwiseTransaction;
    });

    // Generate summary
    const mockSummary: PairwiseTransactionSummary = {
      walletA: sourceWallet.address,
      walletB: targetWallet.address,
      totalTransactions: mockTransactions.length,
      totalVolume: mockTransactions.reduce((sum, tx) => sum + tx.value, 0),
      totalVolumeUSD: mockTransactions.reduce((sum, tx) => sum + (tx.usdValue || 0), 0),
      firstTransaction: mockTransactions[mockTransactions.length - 1]?.timestamp || '',
      lastTransaction: mockTransactions[0]?.timestamp || '',
      topTokens: [
        { symbol: 'ETH', volume: 45.2, volumeUSD: 125000, transactionCount: 5 },
        { symbol: 'USDC', volume: 25000, volumeUSD: 25000, transactionCount: 3 },
        { symbol: 'USDT', volume: 15000, volumeUSD: 15000, transactionCount: 2 }
      ],
      riskDistribution: {
        low: mockTransactions.filter(tx => tx.riskLevel === 'low').length,
        medium: mockTransactions.filter(tx => tx.riskLevel === 'medium').length,
        high: mockTransactions.filter(tx => tx.riskLevel === 'high').length,
        critical: mockTransactions.filter(tx => tx.riskLevel === 'critical').length
      },
      transactionTypes: {
        transfer: mockTransactions.filter(tx => tx.transactionType === 'transfer').length,
        swap: mockTransactions.filter(tx => tx.transactionType === 'swap').length,
        deposit: mockTransactions.filter(tx => tx.transactionType === 'deposit').length,
        withdraw: mockTransactions.filter(tx => tx.transactionType === 'withdraw').length,
        contract_call: mockTransactions.filter(tx => tx.transactionType === 'contract_call').length
      }
    };

    setTransactions(mockTransactions);
    setSummary(mockSummary);
    setLoading(false);
  };

  const filteredTransactions = transactions.filter(tx => {
    if (filter !== 'all' && tx.direction !== filter) return false;
    if (tokenFilter !== 'all' && tx.tokenSymbol !== tokenFilter) return false;
    if (riskFilter !== 'all' && tx.riskLevel !== riskFilter) return false;
    return true;
  });

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'medium': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'high': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'critical': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'transfer': return <FaExchangeAlt className="text-blue-400" />;
      case 'swap': return <FaEthereum className="text-purple-400" />;
      case 'deposit': return <FaChevronDown className="text-green-400" />;
      case 'withdraw': return <FaChevronUp className="text-red-400" />;
      default: return <FaLink className="text-gray-400" />;
    }
  };

  const formatValue = (value: number, decimals: number = 4) => {
    return value.toFixed(decimals);
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="w-6 h-6 border-2 rounded-full animate-spin border-accent-400 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with wallet pair info */}
      <div className="text-center">
        <FaLink className="mx-auto mb-3 text-4xl text-accent-400" />
        <p className="text-foreground-muted">Transactions Between Wallets</p>
        <div className="mt-2 text-xs text-foreground-muted">
          <span className="font-mono">{formatAddress(sourceWallet.address)}</span>
          <span className="mx-2">↔</span>
          <span className="font-mono">{formatAddress(targetWallet.address)}</span>
        </div>
      </div>

      {/* Summary Stats */}
      {summary && (
        <div className="grid grid-cols-2 gap-3 mb-6">
          <div className="p-4 bg-gradient-to-br rounded-lg border from-primary-500/10 to-primary-600/5 border-primary-500/20">
            <div className="flex gap-2 items-center mb-2">
              <FaExchangeAlt className="text-primary-400" size={14} />
              <label className="text-xs font-medium text-foreground-muted">Total Transactions</label>
            </div>
            <p className="text-lg font-bold text-foreground">{summary.totalTransactions}</p>
            <p className="text-xs text-foreground-muted">Between wallets</p>
          </div>

          <div className="p-4 bg-gradient-to-br rounded-lg border from-secondary-500/10 to-secondary-600/5 border-secondary-500/20">
            <div className="flex gap-2 items-center mb-2">
              <FaEthereum className="text-secondary-400" size={14} />
              <label className="text-xs font-medium text-foreground-muted">Total Volume</label>
            </div>
            <p className="text-lg font-bold text-foreground">${summary.totalVolumeUSD.toLocaleString()}</p>
            <p className="text-xs text-foreground-muted">USD equivalent</p>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="mb-6">
        <div className="flex gap-2 items-center mb-3">
          <FaFilter className="text-accent-400" size={14} />
          <span className="text-sm font-medium text-foreground">Filter Transactions</span>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
          {/* Direction Filter */}
          <div className="space-y-1">
            <label className="block text-xs font-medium text-foreground-muted">Direction</label>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="w-full px-3 py-2 text-sm rounded-lg bg-slate-700/50 border border-slate-600/50 text-foreground focus:border-accent-400 focus:ring-1 focus:ring-accent-400 transition-colors"
            >
              <option value="all">All Directions</option>
              <option value="incoming">Incoming Only</option>
              <option value="outgoing">Outgoing Only</option>
            </select>
          </div>

          {/* Token Filter */}
          <div className="space-y-1">
            <label className="block text-xs font-medium text-foreground-muted">Token</label>
            <select
              value={tokenFilter}
              onChange={(e) => setTokenFilter(e.target.value)}
              className="w-full px-3 py-2 text-sm rounded-lg bg-slate-700/50 border border-slate-600/50 text-foreground focus:border-accent-400 focus:ring-1 focus:ring-accent-400 transition-colors"
            >
              <option value="all">All Tokens</option>
              <option value="ETH">ETH</option>
              <option value="USDC">USDC</option>
              <option value="USDT">USDT</option>
              <option value="DAI">DAI</option>
              <option value="WETH">WETH</option>
              <option value="UNI">UNI</option>
            </select>
          </div>

          {/* Risk Filter */}
          <div className="space-y-1">
            <label className="block text-xs font-medium text-foreground-muted">Risk Level</label>
            <select
              value={riskFilter}
              onChange={(e) => setRiskFilter(e.target.value)}
              className="w-full px-3 py-2 text-sm rounded-lg bg-slate-700/50 border border-slate-600/50 text-foreground focus:border-accent-400 focus:ring-1 focus:ring-accent-400 transition-colors"
            >
              <option value="all">All Risk Levels</option>
              <option value="low">Low Risk</option>
              <option value="medium">Medium Risk</option>
              <option value="high">High Risk</option>
              <option value="critical">Critical Risk</option>
            </select>
          </div>
        </div>
      </div>

      {/* Transaction List */}
      <div className="space-y-3">
        {filteredTransactions.map(tx => (
          <div key={tx.id} className="glass-card p-4 rounded-xl border transition-all duration-300 bg-background-secondary border-border hover:bg-background-tertiary hover:border-accent-400/30 hover:shadow-lg hover:shadow-accent-400/10">
            <div className="flex justify-between items-center mb-3">
              <div className="flex gap-3 items-center">
                <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-slate-600/50 to-slate-700/50 border border-slate-500/30">
                  {getTransactionIcon(tx.transactionType)}
                </div>
                <div className="flex flex-col">
                  <span className="text-sm font-semibold text-foreground">
                    {tx.method || 'Transfer'}
                  </span>
                  <div className="flex gap-2 items-center mt-1">
                    <span className={`px-2 py-0.5 rounded-full text-xs font-medium border ${getRiskColor(tx.riskLevel)}`}>
                      {tx.riskLevel.toUpperCase()}
                    </span>
                    <span className="px-2 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-blue-300 border border-blue-500/30">
                      {tx.tokenSymbol}
                    </span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <span className="text-xs text-foreground-muted">
                  {formatDate(tx.timestamp)}
                </span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2 text-xs">
                <div className="px-2 py-1 rounded-md bg-slate-700/50 border border-slate-600/50">
                  <span className="font-mono text-foreground-muted">{formatAddress(tx.from)}</span>
                </div>
                <div className="flex items-center justify-center w-6 h-6 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30">
                  <span className="text-blue-300 text-xs">→</span>
                </div>
                <div className="px-2 py-1 rounded-md bg-slate-700/50 border border-slate-600/50">
                  <span className="font-mono text-foreground-muted">{formatAddress(tx.to)}</span>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-lg font-bold ${tx.direction === 'incoming' ? 'text-green-400' : 'text-blue-400'}`}>
                  {tx.direction === 'incoming' ? '+' : ''}{formatValue(tx.value)} {tx.tokenSymbol}
                </div>
                {tx.usdValue && (
                  <div className="text-xs text-foreground-muted">
                    ≈ ${formatValue(tx.usdValue, 2)}
                  </div>
                )}
              </div>
            </div>

            {/* Expandable details */}
            {expandedTx === tx.id && (
              <div className="mt-4 pt-4 border-t border-slate-600/50 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FaGasPump className="text-orange-400" size={12} />
                      <span className="text-xs font-medium text-foreground-muted">Gas Fee</span>
                    </div>
                    <div className="px-3 py-2 rounded-lg bg-slate-700/30 border border-slate-600/30">
                      <span className="text-sm font-mono text-foreground">{formatValue(tx.gasFee, 6)} ETH</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FaClock className="text-blue-400" size={12} />
                      <span className="text-xs font-medium text-foreground-muted">Block Number</span>
                    </div>
                    <div className="px-3 py-2 rounded-lg bg-slate-700/30 border border-slate-600/30">
                      <span className="text-sm font-mono text-foreground">{tx.blockNumber.toLocaleString()}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FaShieldAlt className="text-green-400" size={12} />
                      <span className="text-xs font-medium text-foreground-muted">Status</span>
                    </div>
                    <div className="px-3 py-2 rounded-lg bg-slate-700/30 border border-slate-600/30">
                      <span className={`text-sm font-semibold ${tx.status === 'success' ? 'text-green-400' : 'text-red-400'}`}>
                        {tx.status.toUpperCase()}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FaExchangeAlt className="text-purple-400" size={12} />
                      <span className="text-xs font-medium text-foreground-muted">Type</span>
                    </div>
                    <div className="px-3 py-2 rounded-lg bg-slate-700/30 border border-slate-600/30">
                      <span className="text-sm font-medium text-foreground">{tx.transactionType}</span>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <button className="flex-1 flex items-center justify-center gap-2 py-2 px-4 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 shadow-lg shadow-blue-500/25">
                    <FaExternalLinkAlt size={12} />
                    View on Explorer
                  </button>
                </div>
              </div>
            )}

            <button
              onClick={() => setExpandedTx(expandedTx === tx.id ? null : tx.id)}
              className="w-full mt-3 py-2 px-3 text-xs font-medium text-foreground-muted hover:text-foreground bg-slate-700/30 hover:bg-slate-600/50 border border-slate-600/30 hover:border-slate-500/50 rounded-lg transition-all duration-300 flex items-center justify-center gap-2"
            >
              {expandedTx === tx.id ? (
                <>
                  <FaChevronUp size={10} />
                  Show Less
                </>
              ) : (
                <>
                  <FaChevronDown size={10} />
                  Show More
                </>
              )}
            </button>
          </div>
        ))}
      </div>

      {filteredTransactions.length === 0 && (
        <div className="text-center py-12">
          <div className="relative mb-6">
            <div className="absolute inset-0 rounded-full opacity-20 blur-lg bg-gradient-to-r from-blue-500 to-purple-500"></div>
            <div className="flex relative justify-center items-center w-16 h-16 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 mx-auto">
              <FaFilter className="text-2xl text-blue-400" />
            </div>
          </div>
          <h3 className="mb-2 text-lg font-semibold text-foreground">No Transactions Found</h3>
          <p className="text-foreground-muted max-w-sm mx-auto">
            No transactions match your current filter criteria. Try adjusting the filters above to see more results.
          </p>
        </div>
      )}
    </div>
  );
};

export default PairwiseTransactionView;
