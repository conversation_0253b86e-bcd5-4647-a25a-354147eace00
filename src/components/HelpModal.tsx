import React from 'react';
import { FaTimes, FaKeyboard, FaQuestionCircle } from 'react-icons/fa';

interface HelpModalProps {
  isVisible: boolean;
  onClose: () => void;
  shortcuts: Array<{
    formatted: string;
    description: string;
    descriptionVi?: string;
  }>;
}

const HelpModal: React.FC<HelpModalProps> = ({ isVisible, onClose, shortcuts }) => {
  const [isVietnamese, setIsVietnamese] = React.useState(false);

  React.useEffect(() => {
    const lang = localStorage.getItem('language') || 'en';
    setIsVietnamese(lang === 'vi');
  }, []);

  if (!isVisible) return null;

  const toggleLanguage = () => {
    const newLang = isVietnamese ? 'en' : 'vi';
    localStorage.setItem('language', newLang);
    setIsVietnamese(!isVietnamese);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999]">
      <div className="glass-card rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto border border-border-accent shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-brand rounded-full flex items-center justify-center">
              <FaKeyboard className="text-white text-lg" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-foreground">
                {isVietnamese ? 'Phím tắt & Trợ giúp' : 'Keyboard Shortcuts & Help'}
              </h2>
              <p className="text-sm text-foreground-muted">
                {isVietnamese 
                  ? 'Sử dụng các phím tắt để làm việc hiệu quả hơn'
                  : 'Use keyboard shortcuts for efficient workflow'
                }
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={toggleLanguage}
              className="px-3 py-1 text-xs bg-background-tertiary hover:bg-background-secondary rounded-lg transition-colors"
            >
              {isVietnamese ? 'EN' : 'VI'}
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-background-tertiary rounded-lg transition-colors"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Shortcuts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {shortcuts.map((shortcut, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-3 bg-background-secondary/50 rounded-lg border border-border-accent"
            >
              <span className="text-sm text-foreground">
                {isVietnamese && shortcut.descriptionVi 
                  ? shortcut.descriptionVi 
                  : shortcut.description
                }
              </span>
              <kbd className="px-2 py-1 bg-background-tertiary border border-border-accent rounded text-xs font-mono text-foreground-muted">
                {shortcut.formatted}
              </kbd>
            </div>
          ))}
        </div>

        {/* Feature Guide */}
        <div className="border-t border-border-accent pt-6">
          <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
            <FaQuestionCircle className="text-accent-400" />
            {isVietnamese ? 'Hướng dẫn Tính năng' : 'Feature Guide'}
          </h3>
          
          <div className="space-y-4">
            <div className="glass-card p-4 rounded-lg">
              <h4 className="font-semibold text-foreground mb-2">
                {isVietnamese ? '🛡️ Hệ thống Cảnh báo Bảo mật' : '🛡️ Security Alert System'}
              </h4>
              <p className="text-sm text-foreground-muted">
                {isVietnamese 
                  ? 'Giám sát thời gian thực các mối đe dọa như phishing, rửa tiền, và hoạt động đáng ngờ. Nhấp vào cảnh báo để xem chi tiết và thực hiện hành động.'
                  : 'Real-time monitoring of threats like phishing, money laundering, and suspicious activities. Click on alerts to view details and take action.'
                }
              </p>
            </div>

            <div className="glass-card p-4 rounded-lg">
              <h4 className="font-semibold text-foreground mb-2">
                {isVietnamese ? '📊 Phân tích Luồng Giao dịch' : '📊 Transaction Flow Analysis'}
              </h4>
              <p className="text-sm text-foreground-muted">
                {isVietnamese 
                  ? 'Theo dõi và phân tích các giao dịch của ví được chọn. Sử dụng bộ lọc để tìm kiếm các mẫu giao dịch cụ thể và đánh giá rủi ro.'
                  : 'Track and analyze transactions for selected wallets. Use filters to find specific transaction patterns and assess risks.'
                }
              </p>
            </div>

            <div className="glass-card p-4 rounded-lg">
              <h4 className="font-semibold text-foreground mb-2">
                {isVietnamese ? '🔍 Phân tích Ví' : '🔍 Wallet Analysis'}
              </h4>
              <p className="text-sm text-foreground-muted">
                {isVietnamese 
                  ? 'Nhấp vào bất kỳ ví nào trên bản đồ để xem phân tích chi tiết bao gồm điểm rủi ro, lịch sử hoạt động, và kết nối mạng.'
                  : 'Click on any wallet in the map to view detailed analysis including risk scores, activity history, and network connections.'
                }
              </p>
            </div>

            <div className="glass-card p-4 rounded-lg">
              <h4 className="font-semibold text-foreground mb-2">
                {isVietnamese ? '🎯 Bộ lọc Rủi ro' : '🎯 Risk Filtering'}
              </h4>
              <p className="text-sm text-foreground-muted">
                {isVietnamese 
                  ? 'Sử dụng bộ lọc rủi ro để làm nổi bật các ví có mức độ rủi ro cao và ẩn các ví an toàn để tập trung vào điều tra.'
                  : 'Use risk filters to highlight high-risk wallets and hide safe ones to focus your investigation efforts.'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Tips */}
        <div className="border-t border-border-accent pt-6 mt-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">
            {isVietnamese ? '💡 Mẹo sử dụng' : '💡 Pro Tips'}
          </h3>
          <ul className="space-y-2 text-sm text-foreground-muted">
            <li className="flex items-start gap-2">
              <span className="text-accent-400 mt-1">•</span>
              {isVietnamese 
                ? 'Sử dụng phím Space để tạm dừng/tiếp tục giám sát thời gian thực'
                : 'Use Space bar to pause/resume real-time monitoring'
              }
            </li>
            <li className="flex items-start gap-2">
              <span className="text-accent-400 mt-1">•</span>
              {isVietnamese 
                ? 'Nhấn Escape để đóng bất kỳ panel nào đang mở'
                : 'Press Escape to close any open panel'
              }
            </li>
            <li className="flex items-start gap-2">
              <span className="text-accent-400 mt-1">•</span>
              {isVietnamese 
                ? 'Sử dụng Ctrl+E để xuất dữ liệu phân tích'
                : 'Use Ctrl+E to export analysis data'
              }
            </li>
            <li className="flex items-start gap-2">
              <span className="text-accent-400 mt-1">•</span>
              {isVietnamese 
                ? 'Nhấp chuột phải vào ví để xem menu ngữ cảnh'
                : 'Right-click on wallets for context menu'
              }
            </li>
          </ul>
        </div>

        {/* Footer */}
        <div className="border-t border-border-accent pt-4 mt-6 text-center">
          <p className="text-xs text-foreground-muted">
            {isVietnamese 
              ? 'VietChain Talents 2025 - Công cụ Phân tích Blockchain'
              : 'VietChain Talents 2025 - Blockchain Analysis Tool'
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default HelpModal;
