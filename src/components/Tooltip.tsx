import React, { useState, useRef, useEffect } from 'react';

interface TooltipProps {
  content: string;
  contentVi?: string; // Vietnamese translation
  children: React.ReactElement;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto' | 'center-right';
  delay?: number;
  className?: string;
  disabled?: boolean;
  showShortcut?: string;
  maxHeight?: string; // Custom max height for scrollable content
  enableScrolling?: boolean; // Enable vertical scrolling for long content
}

const Tooltip: React.FC<TooltipProps> = ({
  content,
  contentVi,
  children,
  position = 'auto',
  delay = 500,
  className = '',
  disabled = false,
  showShortcut,
  maxHeight = 'min(300px, 70vh)',
  enableScrolling = true
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState<'top' | 'bottom' | 'left' | 'right' | 'center-right'>('top');
  const [isVietnamese, setIsVietnamese] = useState(false);
  const [centerRightPosition, setCenterRightPosition] = useState({ top: 0, left: 0 });
  const timeoutRef = useRef<NodeJS.Timeout>();
  const tooltipRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  // Check if Vietnamese language is preferred (could be from localStorage or context)
  useEffect(() => {
    const lang = localStorage.getItem('language') || 'en';
    setIsVietnamese(lang === 'vi');
  }, []);

  // Enhanced map viewport calculation with layout manager integration
  const calculateMapViewport = () => {
    if (typeof window === 'undefined') {
      return { left: 400, top: 120, width: 800, height: 600, right: 1200, bottom: 680 };
    }

    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Device type detection for responsive calculations
    const isMobile = viewport.width < 768;
    const isTablet = viewport.width >= 768 && viewport.width < 1024;
    const isDesktop = viewport.width >= 1024;

    // Enhanced layout calculations
    const headerHeight = 80; // TopBar height
    const topBarBuffer = 20; // Reduced buffer for better space utilization
    const effectiveTopMargin = headerHeight + topBarBuffer; // 100px total

    // Dynamic sidebar width calculation
    let sidebarWidth = 0;
    if (isMobile) {
      sidebarWidth = 0; // Mobile: sidebar is overlay, doesn't affect map area
    } else if (isTablet) {
      sidebarWidth = Math.min(viewport.width * 0.4, 320); // Tablet: 40% of width, max 320px
    } else {
      sidebarWidth = 384; // Desktop: w-96 = 384px
    }

    // Control panel space calculation
    const controlPanelSpace = isMobile ? 0 : 200; // Mobile: bottom FAB doesn't affect map area

    // Bottom margin for performance monitor and mobile controls
    const bottomMargin = isMobile ? 100 : 60;

    return {
      left: sidebarWidth + controlPanelSpace + 16, // Sidebar + control panel + margin
      top: effectiveTopMargin,
      width: viewport.width - sidebarWidth - controlPanelSpace - 32,
      height: viewport.height - effectiveTopMargin - bottomMargin,
      right: viewport.width - 16,
      bottom: viewport.height - bottomMargin
    };
  };

  // Enhanced center-right position calculation with ResponsiveContainer awareness
  const calculateCenterRightPosition = () => {
    if (!triggerRef.current) {
      // Fallback to viewport-based calculation if no trigger element
      const mapArea = calculateMapViewport();
      const viewport = { width: window.innerWidth, height: window.innerHeight };
      const isMobile = viewport.width < 768;
      const isTablet = viewport.width >= 768 && viewport.width < 1024;

      let horizontalPercent = 0.75;
      let verticalPercent = 0.5;

      if (isMobile) {
        horizontalPercent = 0.5;
        verticalPercent = 0.4;
      } else if (isTablet) {
        horizontalPercent = 0.65;
        verticalPercent = 0.45;
      }

      const centerRightX = mapArea.left + (mapArea.width * horizontalPercent);
      const centerY = mapArea.top + (mapArea.height * verticalPercent);

      return { top: centerY, left: centerRightX };
    }

    // Get the actual position of the trigger element (after ResponsiveContainer positioning)
    const triggerRect = triggerRef.current.getBoundingClientRect();
    const viewport = { width: window.innerWidth, height: window.innerHeight };

    // Device-specific positioning strategy
    const isMobile = viewport.width < 768;
    const isTablet = viewport.width >= 768 && viewport.width < 1024;

    // Calculate position relative to trigger element for better accuracy
    const mapArea = calculateMapViewport();

    // Responsive tooltip dimensions
    const tooltipWidth = isMobile ? 280 : isTablet ? 300 : 320;
    const tooltipHeight = enableScrolling ? (isMobile ? 200 : 250) : (isMobile ? 150 : 200);
    const margin = isMobile ? 12 : 16;

    // Position tooltip to the right of the trigger element with some offset
    let left = triggerRect.right + 16; // 16px offset from trigger
    let top = triggerRect.top + (triggerRect.height / 2); // Center vertically with trigger

    // If there's not enough space to the right, position to the left
    if (left + tooltipWidth > viewport.width - margin) {
      left = triggerRect.left - tooltipWidth - 16;
    }

    // If still not enough space, use center-right of map area as fallback
    if (left < margin) {
      left = mapArea.left + (mapArea.width * 0.75);
      top = mapArea.top + (mapArea.height * 0.5);
    }

    // Ensure tooltip doesn't go off-screen vertically
    const minTopPosition = mapArea.top + (isMobile ? 10 : 20);
    const maxTopPosition = viewport.height - tooltipHeight - margin;

    top = Math.max(minTopPosition, Math.min(top, maxTopPosition));

    // Final bounds checking
    if (left + tooltipWidth > viewport.width - margin) {
      left = viewport.width - tooltipWidth - margin;
    }
    if (left < margin) {
      left = margin;
    }

    return { top, left };
  };

  const calculatePosition = () => {
    if (!triggerRef.current) return;

    // Handle center-right positioning with ResponsiveContainer awareness
    if (position === 'center-right') {
      const centerRightPos = calculateCenterRightPosition();
      setCenterRightPosition(centerRightPos);
      setTooltipPosition('center-right');
      return;
    }

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Enhanced margin for better spacing and mobile compatibility - prevent hydration issues
    const margin = typeof window !== 'undefined' && window.innerWidth < 768 ? 16 : 12;
    const safeZone = {
      top: 80, // Account for header
      bottom: 100, // Account for mobile navigation and performance monitor
      left: margin,
      right: margin
    };

    // Check if trigger element is inside a ResponsiveContainer
    const responsiveContainer = triggerRef.current.closest('[data-component]');
    const isInResponsiveContainer = !!responsiveContainer;

    if (position === 'auto') {
      // Enhanced auto-calculation with component awareness and ResponsiveContainer detection
      const spaceTop = triggerRect.top - safeZone.top;
      const spaceBottom = viewport.height - triggerRect.bottom - safeZone.bottom;
      const spaceLeft = triggerRect.left - safeZone.left;
      const spaceRight = viewport.width - triggerRect.right - safeZone.right;

      // Check for overlapping components (LeftSidebar, WalletAnalysisPanel, Performance Monitor)
      const isNearLeftSidebar = triggerRect.left < 420; // LeftSidebar width + margin
      const isNearRightPanel = triggerRect.right > viewport.width - 420; // WalletAnalysisPanel
      const isNearBottom = triggerRect.bottom > viewport.height - 140; // Performance Monitor area

      // If in ResponsiveContainer, prefer positions that work well with dynamic positioning
      if (isInResponsiveContainer) {
        // For ResponsiveContainer elements, prefer top/bottom to avoid horizontal positioning issues
        // Also consider the trigger element's position in viewport for better placement
        const triggerCenterX = triggerRect.left + triggerRect.width / 2;
        const viewportCenterX = viewport.width / 2;

        // Prefer top if there's enough space and element is not at the very top
        if (spaceTop >= 80 && triggerRect.top > 100) {
          setTooltipPosition('top');
        }
        // Prefer bottom if there's enough space
        else if (spaceBottom >= 80) {
          setTooltipPosition('bottom');
        }
        // If element is on the left side of viewport, prefer right positioning
        else if (triggerCenterX < viewportCenterX && spaceRight >= 150 && !isNearRightPanel) {
          setTooltipPosition('right');
        }
        // If element is on the right side of viewport, prefer left positioning
        else if (triggerCenterX >= viewportCenterX && spaceLeft >= 150 && !isNearLeftSidebar) {
          setTooltipPosition('left');
        }
        // Fallback: choose the position with most available space
        else {
          const spaces = { top: spaceTop, bottom: spaceBottom, left: spaceLeft, right: spaceRight };
          const maxSpace = Math.max(...Object.values(spaces));
          const bestPosition = Object.keys(spaces).find(key => spaces[key as keyof typeof spaces] === maxSpace) as 'top' | 'bottom' | 'left' | 'right';
          setTooltipPosition(bestPosition || 'top');
        }
      } else {
        // Standard positioning logic for non-ResponsiveContainer elements
        if (spaceTop >= 40 && !isNearBottom) {
          setTooltipPosition('top');
        } else if (spaceBottom >= 40 && !isNearBottom) {
          setTooltipPosition('bottom');
        } else if (spaceRight >= 100 && !isNearRightPanel) {
          setTooltipPosition('right');
        } else if (spaceLeft >= 100 && !isNearLeftSidebar) {
          setTooltipPosition('left');
        } else {
          // Fallback: choose position with most space, even if cramped
          const spaces = { top: spaceTop, bottom: spaceBottom, left: spaceLeft, right: spaceRight };
          const maxSpace = Math.max(...Object.values(spaces));
          const bestPosition = Object.keys(spaces).find(key => spaces[key as keyof typeof spaces] === maxSpace) as 'top' | 'bottom' | 'left' | 'right';
          setTooltipPosition(bestPosition || 'top');
        }
      }
    } else {
      setTooltipPosition(position);
    }
  };

  const showTooltip = () => {
    if (disabled) return;

    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      // Calculate position immediately when tooltip becomes visible
      requestAnimationFrame(() => {
        calculatePosition();
      });
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  const handleMouseEnter = () => showTooltip();
  const handleMouseLeave = () => hideTooltip();
  const handleFocus = () => showTooltip();
  const handleBlur = () => hideTooltip();

  // Touch-friendly interactions for mobile - prevent hydration issues
  const handleTouchStart = () => {
    if (typeof window !== 'undefined' && window.innerWidth < 768) {
      showTooltip();
    }
  };

  const handleTouchEnd = () => {
    if (typeof window !== 'undefined' && window.innerWidth < 768) {
      setTimeout(hideTooltip, 2000); // Auto-hide after 2 seconds on mobile
    }
  };

  const getTooltipClasses = () => {
    const viewport = typeof window !== 'undefined' ? { width: window.innerWidth, height: window.innerHeight } : { width: 1024, height: 768 };
    const isMobile = viewport.width < 768;
    const isTablet = viewport.width >= 768 && viewport.width < 1024;

    // Check if tooltip is in ResponsiveContainer
    const isInResponsiveContainer = triggerRef.current?.closest('[data-component]');

    // Handle center-right positioning with enhanced responsive glass morphism
    if (tooltipPosition === 'center-right') {
      const baseClasses = `
        tooltip-compact tooltip-center-right text-white
        transition-all duration-300 ease-out pointer-events-none
        break-words vietnamese-text
        ${isMobile ? 'max-w-[280px]' : isTablet ? 'max-w-[300px]' : 'max-w-[320px]'}
      `;

      const visibilityClasses = isVisible
        ? 'opacity-100 scale-100 translate-y-0'
        : 'opacity-0 scale-95 translate-y-2 pointer-events-none';

      return `${baseClasses} ${visibilityClasses} ${className}`.trim().replace(/\s+/g, ' ');
    }

    // Enhanced styling with responsive glass morphism for regular tooltips
    const scrollClasses = enableScrolling ? 'overflow-y-auto' : 'whitespace-nowrap';
    const responsiveMaxWidth = isMobile ? 'max-w-[280px]' : isTablet ? 'max-w-[300px]' : 'max-w-[320px]';

    // Use compact styling for ResponsiveContainer tooltips
    const tooltipClasses = isInResponsiveContainer ? 'tooltip-compact' : 'glass-card backdrop-blur-xl rounded-lg shadow-glow border border-gray-700/40 px-2 py-1.5';
    const positioningType = isInResponsiveContainer ? 'fixed' : 'absolute';

    const baseClasses = `
      ${positioningType} text-xs font-medium text-white
      ${tooltipClasses}
      transition-all duration-300 ease-out pointer-events-none
      ${responsiveMaxWidth} break-words vietnamese-text
      ${scrollClasses}
    `;

    // Enhanced positioning with responsive spacing - reduced spacing for better fit
    const spacing = isMobile ? 2 : 1;

    // Use different positioning classes for ResponsiveContainer vs regular tooltips
    const positionClasses = isInResponsiveContainer ? {
      // For ResponsiveContainer, don't use relative positioning classes since we use fixed positioning
      top: '',
      bottom: '',
      left: '',
      right: ''
    } : {
      top: `bottom-full left-1/2 transform -translate-x-1/2 mb-${spacing}`,
      bottom: `top-full left-1/2 transform -translate-x-1/2 mt-${spacing}`,
      left: `right-full top-1/2 transform -translate-y-1/2 mr-${spacing}`,
      right: `left-full top-1/2 transform -translate-y-1/2 ml-${spacing}`
    };

    const visibilityClasses = isVisible
      ? 'opacity-100 scale-100 translate-y-0'
      : 'opacity-0 scale-95 translate-y-1 pointer-events-none';

    return `${baseClasses} ${positionClasses[tooltipPosition as keyof typeof positionClasses] || ''} ${visibilityClasses} ${className}`;
  };

  const getArrowClasses = () => {
    const baseClasses = 'absolute w-1.5 h-1.5 bg-gray-900/90 border border-gray-700/40 transform rotate-45';

    const arrowPositions: Record<string, string> = {
      top: 'top-full left-1/2 transform -translate-x-1/2 -translate-y-1/2',
      bottom: 'bottom-full left-1/2 transform -translate-x-1/2 translate-y-1/2',
      left: 'left-full top-1/2 transform -translate-y-1/2 -translate-x-1/2',
      right: 'right-full top-1/2 transform -translate-y-1/2 translate-x-1/2'
    };

    return `${baseClasses} ${arrowPositions[tooltipPosition] || ''}`;
  };

  const displayContent = isVietnamese && contentVi ? contentVi : content;

  // Clone the child element and add event handlers
  const clonedChild = React.cloneElement(children, {
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    onFocus: handleFocus,
    onBlur: handleBlur,
    onTouchStart: handleTouchStart,
    onTouchEnd: handleTouchEnd,
    'aria-describedby': isVisible ? 'tooltip' : undefined,
    'aria-label': isVisible ? undefined : displayContent, // Fallback for screen readers
  });

  return (
    <div ref={triggerRef} className="relative inline-block">
      {clonedChild}

      {isVisible && (
        <div
          ref={tooltipRef}
          id="tooltip"
          role="tooltip"
          className={getTooltipClasses()}
          style={(() => {
            // Handle center-right positioning
            if (tooltipPosition === 'center-right') {
              return {
                top: `${centerRightPosition.top}px`,
                left: `${centerRightPosition.left}px`,
                transform: 'translate(-50%, -50%)'
              };
            }

            // Handle fixed positioning for ResponsiveContainer tooltips
            const isInResponsiveContainer = triggerRef.current?.closest('[data-component]');
            if (isInResponsiveContainer && triggerRef.current) {
              const triggerRect = triggerRef.current.getBoundingClientRect();
              const viewport = { width: window.innerWidth, height: window.innerHeight };
              const margin = viewport.width < 768 ? 16 : 12;

              // Estimate tooltip dimensions for better positioning
              const tooltipWidth = viewport.width < 768 ? 280 : viewport.width < 1024 ? 300 : 320;
              const tooltipHeight = enableScrolling ? 120 : 60; // Increased height for scrollable content

              // Calculate fixed position based on trigger element's actual position
              let top = triggerRect.top;
              let left = triggerRect.left + triggerRect.width / 2;

              // Reduced spacing and better positioning to avoid overlap
              const tooltipOffset = 8; // Slightly larger offset for better visibility

              switch (tooltipPosition) {
                case 'top':
                  top = triggerRect.top - tooltipHeight - tooltipOffset;
                  // Center horizontally but ensure it doesn't overflow
                  left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2);
                  break;
                case 'bottom':
                  top = triggerRect.bottom + tooltipOffset;
                  // Center horizontally but ensure it doesn't overflow
                  left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2);
                  break;
                case 'left':
                  left = triggerRect.left - tooltipWidth - tooltipOffset;
                  top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2);
                  break;
                case 'right':
                  left = triggerRect.right + tooltipOffset;
                  top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2);
                  break;
              }

              // Ensure tooltip doesn't overflow viewport boundaries
              if (left < margin) {
                left = margin;
              } else if (left + tooltipWidth > viewport.width - margin) {
                left = viewport.width - tooltipWidth - margin;
              }

              if (top < margin) {
                top = margin;
              } else if (top + tooltipHeight > viewport.height - margin) {
                top = viewport.height - tooltipHeight - margin;
              }

              return {
                top: `${top}px`,
                left: `${left}px`,
                transform: 'none' // Override default transform for fixed positioning
              };
            }

            return undefined;
          })()}
        >
          {/* Arrow - only show for non-center-right tooltips */}
          {tooltipPosition !== 'center-right' && (
            <div className={getArrowClasses()} />
          )}

          {/* Center-right tooltip indicator arrow - smaller size */}
          {tooltipPosition === 'center-right' && (
            <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-gray-900/90 border-l border-t border-gray-700/40 rotate-45" />
          )}

          {/* Scrollable Content Container */}
          <div
            className={`relative z-10 ${enableScrolling ? 'tooltip-container overflow-y-auto' : ''}`}
            style={enableScrolling ? { maxHeight } : undefined}
          >
            <div className={`text-white font-medium leading-snug tooltip-content ${isVietnamese ? 'vietnamese-text' : ''}`}>
              {/* Display full content with scrolling support for long content */}
              {displayContent}
            </div>
            {showShortcut && (
              <div className="mt-1 pt-1 border-t border-gray-600/50">
                <div className="flex items-center gap-1">
                  <span className="text-[10px] text-gray-300">Shortcut:</span>
                  <kbd className="px-1 py-0.5 bg-gray-800 border border-gray-600 rounded text-[10px] font-mono text-yellow-300">
                    {showShortcut}
                  </kbd>
                </div>
              </div>
            )}

            {/* Visual indicator for center-right tooltips - more compact */}
            {tooltipPosition === 'center-right' && (
              <div className="mt-1 pt-1 border-t border-gray-600/50">
                <div className="flex items-center gap-1">
                  <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse"></div>
                  <span className="text-[10px] text-gray-300">
                    {isVietnamese ? 'Từ thanh bên' : 'From sidebar'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Tooltip;
