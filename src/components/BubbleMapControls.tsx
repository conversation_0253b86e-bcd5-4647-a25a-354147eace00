import React, { useState } from 'react';
import { FaPlus, FaMinus, FaHome, FaExpand, FaCompress, FaCog, FaEye, FaEyeSlash, FaFilter, FaInfoCircle, FaLayerGroup, FaSearchPlus, FaSearchMinus } from 'react-icons/fa';
import BubbleMapLegend from './BubbleMapLegend';

interface BubbleMapControlsProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetView: () => void;
  onToggleFullscreen?: () => void;
  isFullscreen?: boolean;
  currentZoom: number;
  nodeCount: number;
  linkCount: number;
  showStats?: boolean;
  onToggleStats?: () => void;
  showLabels?: boolean;
  onToggleLabels?: () => void;
  // New props for image/color node counts
  imageNodeCount?: number;
  colorNodeCount?: number;
  showLegend?: boolean;
}

const BubbleMapControls: React.FC<BubbleMapControlsProps> = ({
  onZoomIn,
  onZoomOut,
  onResetView,
  onToggleFullscreen,
  isFullscreen = false,
  currentZoom,
  nodeCount,
  linkCount,
  showStats = false,
  onToggleStats,
  showLabels = true,
  onToggleLabels,
  imageNodeCount = 0,
  colorNodeCount = 0,
  showLegend = true,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <>
      {/* FAB Styles */}
      <style jsx>{`
        .fab-button {
          @apply w-14 h-14 rounded-full shadow-lg backdrop-blur-lg border border-border-secondary transition-all duration-300 flex items-center justify-center group hover:shadow-xl hover:scale-105;
        }
        .fab-main {
          @apply w-16 h-16 rounded-full bg-gradient-to-r from-accent-500 to-accent-600 text-white shadow-xl backdrop-blur-lg border border-accent-400/50 transition-all duration-300 flex items-center justify-center group hover:shadow-2xl hover:scale-110 hover:from-accent-600 hover:to-accent-700;
        }
      `}</style>

      <div className="fixed z-30 flex flex-col items-end space-y-3 bottom-6 right-6">
        {/* Expandable Action Menu */}
        {isExpanded && (
          <div className="flex flex-col space-y-3 animate-slide-up">
            {/* Secondary Controls Group */}
            <div className="flex flex-col space-y-2">
              {onToggleLabels && (
                <button
                  onClick={onToggleLabels}
                  className={`fab-button ${
                    showLabels
                      ? 'bg-accent-500/90 text-white shadow-glow'
                      : 'bg-background-card/90 text-foreground hover:bg-accent-500 hover:text-white'
                  }`}
                  title={showLabels ? "Hide Labels" : "Show Labels"}
                >
                  {showLabels ? (
                    <FaEye size={18} className="transition-transform group-hover:scale-110" />
                  ) : (
                    <FaEyeSlash size={18} className="transition-transform group-hover:scale-110" />
                  )}
                </button>
              )}

              <button
                className="fab-button bg-background-card/90 text-foreground hover:bg-orange-500 hover:text-white"
                title="Filter Options"
              >
                <FaFilter size={18} className="transition-transform group-hover:scale-110" />
              </button>

              {onToggleFullscreen && (
                <button
                  onClick={onToggleFullscreen}
                  className="fab-button bg-background-card/90 text-foreground hover:bg-secondary-500 hover:text-white"
                  title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
                >
                  {isFullscreen ? (
                    <FaCompress size={18} className="transition-transform group-hover:scale-110" />
                  ) : (
                    <FaExpand size={18} className="transition-transform group-hover:scale-110" />
                  )}
                </button>
              )}
            </div>

            {/* Zoom Controls Group */}
            <div className="flex flex-col space-y-2">
              <button
                onClick={onZoomIn}
                className="fab-button bg-background-card/90 text-foreground hover:bg-accent-500 hover:text-white"
                title="Zoom In"
              >
                <FaSearchPlus size={18} className="transition-transform group-hover:scale-110" />
              </button>
              <button
                onClick={onZoomOut}
                className="fab-button bg-background-card/90 text-foreground hover:bg-accent-500 hover:text-white"
                title="Zoom Out"
              >
                <FaSearchMinus size={18} className="transition-transform group-hover:scale-110" />
              </button>
              <button
                onClick={onResetView}
                className="fab-button bg-background-card/90 text-foreground hover:bg-primary-500 hover:text-white"
                title="Reset View"
              >
                <FaHome size={18} className="transition-transform group-hover:scale-110" />
              </button>
            </div>
          </div>
        )}

        {/* Main FAB Button */}
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className={`fab-main ${isExpanded ? 'rotate-45' : ''}`}
          title="Controls"
        >
          <FaLayerGroup size={22} className="transition-transform duration-300" />
        </button>
      </div>

      {/* Floating Stats Panel - Only show when expanded and stats enabled */}
      {isExpanded && onToggleStats && showStats && (
        <div className="fixed max-w-xs p-4 bottom-6 left-6 z-25 glass-card rounded-xl animate-slide-up">
          <div className="flex items-center gap-2 mb-3 text-sm font-medium text-foreground-secondary">
            <FaInfoCircle className="text-accent-400" />
            <span>Statistics</span>
          </div>

          <div className="space-y-3 text-sm">
            {/* Zoom Level */}
            <div className="flex items-center justify-between">
              <span className="text-foreground-muted">Zoom</span>
              <span className="font-mono text-accent-400">
                {(currentZoom * 100).toFixed(0)}%
              </span>
            </div>

            {/* Node Count */}
            <div className="flex items-center justify-between">
              <span className="text-foreground-muted">Nodes</span>
              <span className="font-mono text-primary-400">
                {nodeCount.toLocaleString()}
              </span>
            </div>

            {/* Link Count */}
            <div className="flex items-center justify-between">
              <span className="text-foreground-muted">Links</span>
              <span className="font-mono text-secondary-400">
                {linkCount.toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Legend Component */}
      {showLegend && (
        <BubbleMapLegend
          position="bottom-left"
          collapsible={true}
          showImageStats={true}
          imageNodeCount={imageNodeCount}
          colorNodeCount={colorNodeCount}
        />
      )}
    </>
  );
};

export default BubbleMapControls;
