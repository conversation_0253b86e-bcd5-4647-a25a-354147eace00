import React, { useState, useRef, useEffect } from 'react';
import { FaLayerGroup, FaChevronUp, FaChevronDown, FaImage, FaPalette, FaInfoCircle } from 'react-icons/fa';

interface BubbleMapLegendProps {
  className?: string;
  position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  collapsible?: boolean;
  showImageStats?: boolean;
  imageNodeCount?: number;
  colorNodeCount?: number;
}

const BubbleMapLegend: React.FC<BubbleMapLegendProps> = ({
  className = '',
  position = 'bottom-left',
  collapsible = true,
  showImageStats = true,
  imageNodeCount = 0,
  colorNodeCount = 0
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const legendRef = useRef<HTMLDivElement>(null);

  // <PERSON><PERSON> click outside to close legend
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (legendRef.current && !legendRef.current.contains(event.target as Node)) {
        setIsCollapsed(true);
      }
    };

    if (!isCollapsed) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isCollapsed]);

  const positionClasses = {
    'bottom-left': 'bottom-6 left-6',
    'bottom-right': 'bottom-6 right-6',
    'top-left': 'top-6 left-6',
    'top-right': 'top-6 right-6'
  };

  const nodeTypes = [
    {
      type: 'wallet',
      label: 'Wallet',
      color: 'from-indigo-400 to-indigo-600',
      borderColor: 'border-indigo-300',
      badge: '💰',
      description: 'Regular wallet addresses'
    },
    {
      type: 'exchange',
      label: 'Exchange',
      color: 'from-emerald-400 to-emerald-600',
      borderColor: 'border-emerald-300',
      badge: '⇄',
      indicator: 'circle',
      description: 'Cryptocurrency exchanges'
    },
    {
      type: 'contract',
      label: 'Contract',
      color: 'from-purple-400 to-purple-600',
      borderColor: 'border-purple-300',
      badge: '◆',
      indicator: 'diamond',
      description: 'Smart contracts'
    },
    {
      type: 'flagged',
      label: 'Flagged',
      color: 'from-red-400 to-red-600',
      borderColor: 'border-red-300',
      badge: '⚠',
      indicator: 'triangle',
      description: 'Suspicious or flagged addresses'
    },
    {
      type: 'bridge',
      label: 'Bridge',
      color: 'from-amber-400 to-amber-600',
      borderColor: 'border-amber-300',
      badge: '🌉',
      description: 'Cross-chain bridges'
    },
    {
      type: 'defi',
      label: 'DeFi',
      color: 'from-pink-400 to-pink-600',
      borderColor: 'border-pink-300',
      badge: '🏦',
      description: 'DeFi protocols'
    },
    {
      type: 'whale',
      label: 'Whale',
      color: 'from-cyan-400 to-cyan-600',
      borderColor: 'border-cyan-300',
      badge: '🐋',
      description: 'Large holders'
    },
    {
      type: 'miner',
      label: 'Miner',
      color: 'from-orange-400 to-orange-600',
      borderColor: 'border-orange-300',
      badge: '⛏',
      description: 'Mining addresses'
    }
  ];

  const renderImageNode = (nodeType: any) => (
    <div className="flex items-center gap-2">
      <div className="relative">
        <div className={`w-5 h-5 bg-gradient-to-br ${nodeType.color} rounded-full border border-white shadow-sm`}></div>
        <div className={`absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-gradient-to-br ${nodeType.color} rounded-full border border-white flex items-center justify-center`}>
          <span className="text-white text-[6px]">{nodeType.badge}</span>
        </div>
      </div>
      <div className="flex-1">
        <span className="text-xs text-gray-300 font-medium">{nodeType.label}</span>
      </div>
    </div>
  );

  const renderColorNode = (nodeType: any) => (
    <div className="flex items-center gap-2">
      <div className={`w-4 h-4 bg-gradient-to-br ${nodeType.color} rounded-full shadow-sm border ${nodeType.borderColor} flex items-center justify-center`}>
        {nodeType.indicator === 'circle' && (
          <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
        )}
        {nodeType.indicator === 'diamond' && (
          <div className="w-1.5 h-1.5 bg-white transform rotate-45"></div>
        )}
        {nodeType.indicator === 'triangle' && (
          <div className="w-0 h-0 border-l-[2px] border-r-[2px] border-b-[3px] border-l-transparent border-r-transparent border-b-white"></div>
        )}
      </div>
      <div className="flex-1">
        <span className="text-xs text-gray-300 font-medium">{nodeType.label}</span>
      </div>
    </div>
  );

  return (
    <div ref={legendRef} className={`fixed z-20 max-w-xs ${positionClasses[position]} ${className}`}>
      <div className="glass-card rounded-lg overflow-hidden">
        {/* Header */}
        <div className="px-3 py-2 border-b border-gray-600/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FaLayerGroup className="text-blue-400" size={12} />
              <span className="text-xs font-semibold text-white">Node Legend</span>
            </div>
            {collapsible && (
              <button
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="p-1 rounded-md hover:bg-gray-700/50 transition-colors"
                title={isCollapsed ? "Expand Legend" : "Collapse Legend"}
              >
                {isCollapsed ? (
                  <FaChevronUp className="text-gray-400" size={10} />
                ) : (
                  <FaChevronDown className="text-gray-400" size={10} />
                )}
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        {!isCollapsed && (
          <div className="p-3 space-y-3">
            {/* Image Nodes Section */}
            <div>
              <div className="flex items-center gap-1.5 mb-2">
                <FaImage className="text-blue-400" size={10} />
                <span className="text-xs font-medium text-gray-400">With Images</span>
                {showImageStats && imageNodeCount > 0 && (
                  <span className="text-xs text-blue-400 bg-blue-500/20 px-1.5 py-0.5 rounded-full">
                    {imageNodeCount}
                  </span>
                )}
              </div>
              <div className="space-y-1.5">
                {nodeTypes.slice(0, 3).map((nodeType) => (
                  <div key={`image-${nodeType.type}`}>
                    {renderImageNode(nodeType)}
                  </div>
                ))}
              </div>
            </div>

            {/* Divider */}
            <div className="border-t border-gray-600/50"></div>

            {/* Color Nodes Section */}
            <div>
              <div className="flex items-center gap-1.5 mb-2">
                <FaPalette className="text-purple-400" size={10} />
                <span className="text-xs font-medium text-gray-400">Color Legend</span>
                {showImageStats && colorNodeCount > 0 && (
                  <span className="text-xs text-purple-400 bg-purple-500/20 px-1.5 py-0.5 rounded-full">
                    {colorNodeCount}
                  </span>
                )}
              </div>
              <div className="space-y-1.5">
                {nodeTypes.slice(0, 5).map((nodeType) => (
                  <div key={`color-${nodeType.type}`}>
                    {renderColorNode(nodeType)}
                  </div>
                ))}
              </div>
            </div>

            {/* Statistics */}
            {showImageStats && (imageNodeCount > 0 || colorNodeCount > 0) && (
              <>
                <div className="border-t border-gray-600/50"></div>
                <div className="flex items-center gap-1.5">
                  <FaInfoCircle className="text-amber-400" size={10} />
                  <div className="text-xs text-gray-500">
                    <span className="text-blue-400 font-medium">{imageNodeCount}</span> images,
                    <span className="text-purple-400 font-medium"> {colorNodeCount}</span> colors
                  </div>
                </div>
              </>
            )}

            {/* Usage Note */}
            <div className="border-t border-gray-600/50 pt-2">
              <div className="text-xs text-gray-500 leading-tight">
                <span className="text-amber-400">💡</span> Images fallback to colors when unavailable
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BubbleMapLegend;
