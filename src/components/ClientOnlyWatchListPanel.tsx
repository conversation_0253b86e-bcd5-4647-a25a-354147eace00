import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Node } from '@/services/neo4jService';

// Dynamically import WatchListPanel with no SSR
const WatchListPanel = dynamic(() => import('./WatchListPanel'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full p-6">
      <div className="text-center">
        <div className="w-8 h-8 mx-auto mb-3 border-2 border-purple-400 rounded-full animate-spin border-t-transparent"></div>
        <p className="text-sm text-slate-400">Loading Watch List...</p>
      </div>
    </div>
  )
});

interface ClientOnlyWatchListPanelProps {
  onSelectWallet?: (wallet: Node) => void;
  onAddToWatchList?: (address: string) => void;
  onFocusWallet?: (address: string) => void;
  className?: string;
}

const ClientOnlyWatchListPanel: React.FC<ClientOnlyWatchListPanelProps> = (props) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render anything during SSR or before hydration
  if (!isMounted) {
    return (
      <div className="flex items-center justify-center h-full p-6">
        <div className="text-center">
          <div className="w-8 h-8 mx-auto mb-3 border-2 border-purple-400 rounded-full animate-spin border-t-transparent"></div>
          <p className="text-sm text-slate-400">Initializing Watch List...</p>
        </div>
      </div>
    );
  }

  return <WatchListPanel {...props} />;
};

export default ClientOnlyWatchListPanel;
