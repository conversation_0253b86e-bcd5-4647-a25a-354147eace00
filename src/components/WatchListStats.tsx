import React, { useState, useEffect } from 'react';
import {
  FaEye,
  FaBell,
  FaShieldAlt,
  FaEthereum,
  FaChartLine,
  FaExclamationTriangle
} from 'react-icons/fa';
import { getWatchListService, WatchListStats as StatsType } from '@/services/watchListService';

interface WatchListStatsProps {
  className?: string;
  compact?: boolean;
}

const WatchListStats: React.FC<WatchListStatsProps> = ({
  className = '',
  compact = false
}) => {
  const [stats, setStats] = useState<StatsType>({
    totalWallets: 0,
    activeAlerts: 0,
    highRiskWallets: 0,
    totalValue: 0,
    recentActivity: 0
  });

  useEffect(() => {
    // Skip on server-side
    if (typeof window === 'undefined') return;

    try {
      const service = getWatchListService();

      // Initial load
      setStats(service.getStats());

      // Subscribe to changes
      const unsubscribe = service.subscribe(() => {
        setStats(service.getStats());
      });

      return unsubscribe;
    } catch (error) {
      console.error('Failed to initialize watch list stats:', error);
      // Keep default empty stats on error
    }
  }, []);

  if (compact) {
    return (
      <div className={`flex items-center gap-3 ${className}`}>
        {/* Total Wallets */}
        <div className="flex items-center gap-1 text-sm">
          <FaEye className="text-purple-400" size={12} />
          <span className="text-slate-300">{stats.totalWallets}</span>
        </div>

        {/* Active Alerts */}
        {stats.activeAlerts > 0 && (
          <div className="flex items-center gap-1 text-sm">
            <FaBell className="text-red-400" size={12} />
            <span className="text-red-400">{stats.activeAlerts}</span>
          </div>
        )}

        {/* High Risk Wallets */}
        {stats.highRiskWallets > 0 && (
          <div className="flex items-center gap-1 text-sm">
            <FaExclamationTriangle className="text-orange-400" size={12} />
            <span className="text-orange-400">{stats.highRiskWallets}</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center gap-2 mb-4">
        <FaChartLine className="text-blue-400" />
        <h3 className="text-sm font-medium text-white">Watch List Statistics</h3>
      </div>

      <div className="grid grid-cols-2 gap-3">
        {/* Total Wallets */}
        <div className="p-3 bg-slate-700/50 border border-slate-600/50 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <FaEye className="text-purple-400" size={14} />
            <span className="text-xs text-slate-400">Total Wallets</span>
          </div>
          <p className="text-lg font-bold text-white">{stats.totalWallets}</p>
        </div>

        {/* Active Alerts */}
        <div className="p-3 bg-slate-700/50 border border-slate-600/50 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <FaBell className="text-red-400" size={14} />
            <span className="text-xs text-slate-400">Active Alerts</span>
          </div>
          <p className={`text-lg font-bold ${stats.activeAlerts > 0 ? 'text-red-400' : 'text-white'}`}>
            {stats.activeAlerts}
          </p>
        </div>

        {/* High Risk Wallets */}
        <div className="p-3 bg-slate-700/50 border border-slate-600/50 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <FaShieldAlt className="text-orange-400" size={14} />
            <span className="text-xs text-slate-400">High Risk</span>
          </div>
          <p className={`text-lg font-bold ${stats.highRiskWallets > 0 ? 'text-orange-400' : 'text-white'}`}>
            {stats.highRiskWallets}
          </p>
        </div>

        {/* Total Value */}
        <div className="p-3 bg-slate-700/50 border border-slate-600/50 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <FaEthereum className="text-blue-400" size={14} />
            <span className="text-xs text-slate-400">Total Value</span>
          </div>
          <p className="text-lg font-bold text-white">
            {stats.totalValue >= 1000
              ? `${(stats.totalValue / 1000).toFixed(1)}K`
              : stats.totalValue.toFixed(2)
            } ETH
          </p>
        </div>
      </div>

      {/* Recent Activity */}
      {stats.recentActivity > 0 && (
        <div className="p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-xs text-green-400">Recent Activity (24h)</span>
          </div>
          <p className="text-sm text-green-400">
            {stats.recentActivity} wallet{stats.recentActivity !== 1 ? 's' : ''} with activity
          </p>
        </div>
      )}
    </div>
  );
};

export default WatchListStats;
