import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import AskAIPanel with no SSR
const AskAIPanel = dynamic(() => import('./AskAIPanel'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full p-6">
      <div className="text-center">
        <div className="w-8 h-8 mx-auto mb-3 border-2 border-blue-400 rounded-full animate-spin border-t-transparent"></div>
        <p className="text-sm text-slate-400">Loading AI Assistant...</p>
      </div>
    </div>
  )
});

interface ClientOnlyAskAIPanelProps {
  selectedWallet?: string;
  onClose?: () => void;
  className?: string;
}

const ClientOnlyAskAIPanel: React.FC<ClientOnlyAskAIPanelProps> = (props) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render anything during SSR or before hydration
  if (!isMounted) {
    return (
      <div className="flex items-center justify-center h-full p-6">
        <div className="text-center">
          <div className="w-8 h-8 mx-auto mb-3 border-2 border-blue-400 rounded-full animate-spin border-t-transparent"></div>
          <p className="text-sm text-slate-400">Initializing AI Assistant...</p>
        </div>
      </div>
    );
  }

  return <AskAIPanel {...props} />;
};

export default ClientOnlyAskAIPanel;
