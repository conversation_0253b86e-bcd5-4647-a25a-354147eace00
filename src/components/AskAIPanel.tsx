import React, { useState, useRef, useEffect } from 'react';
import {
  FaRobot, FaPaperPlane, FaSpinner, FaCopy, FaThumbsUp, FaThumbsDown,
  FaHistory, FaTimes, FaArrowUp, FaLightbulb, FaChartLine, FaShieldAlt
} from 'react-icons/fa';
import Tooltip from './Tooltip';

interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: string;
  isLoading?: boolean;
}

interface AskAIPanelProps {
  selectedWallet?: string;
  onClose?: () => void;
}

const AskAIPanel: React.FC<AskAIPanelProps> = ({ selectedWallet, onClose }) => {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Suggested questions based on context
  const suggestedQuestions = [
    "Analyze the risk level of this wallet",
    "What are the main transaction patterns?",
    "Are there any suspicious activities?",
    "Show me connected high-risk addresses",
    "Explain the transaction flow analysis",
    "What compliance flags should I be aware of?"
  ];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    const handleScroll = () => {
      if (chatContainerRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
        setShowScrollToTop(scrollTop < scrollHeight - clientHeight - 200);
      }
    };

    const container = chatContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const scrollToTop = () => {
    chatContainerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleSendMessage = async (message?: string) => {
    const messageText = message || inputValue.trim();
    if (!messageText || isLoading) return;

    const userMessage: AIMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: messageText,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Add loading message
    const loadingMessage: AIMessage = {
      id: (Date.now() + 1).toString(),
      type: 'ai',
      content: '',
      timestamp: new Date().toISOString(),
      isLoading: true
    };

    setMessages(prev => [...prev, loadingMessage]);

    try {
      // Simulate AI response
      await new Promise(resolve => setTimeout(resolve, 2000));

      const aiResponse = generateAIResponse(messageText, selectedWallet);

      setMessages(prev => prev.map(msg =>
        msg.id === loadingMessage.id
          ? { ...msg, content: aiResponse, isLoading: false }
          : msg
      ));
    } catch (error) {
      console.error('Error getting AI response:', error);
      setMessages(prev => prev.map(msg =>
        msg.id === loadingMessage.id
          ? { ...msg, content: 'Sorry, I encountered an error. Please try again.', isLoading: false }
          : msg
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const generateAIResponse = (question: string, wallet?: string): string => {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('risk') || lowerQuestion.includes('suspicious')) {
      return `Based on my analysis${wallet ? ` of wallet ${wallet.slice(0, 8)}...${wallet.slice(-6)}` : ''}, I've identified several key risk factors:

🔴 **High Risk Indicators:**
• Multiple interactions with known phishing contracts
• Unusual transaction patterns suggesting possible money laundering
• Connections to sanctioned addresses

📊 **Risk Score: 85/100 (High Risk)**

🛡️ **Recommendations:**
• Flag for manual review
• Monitor future transactions closely
• Consider compliance reporting requirements`;
    }

    if (lowerQuestion.includes('pattern') || lowerQuestion.includes('transaction')) {
      return `Transaction pattern analysis${wallet ? ` for ${wallet.slice(0, 8)}...${wallet.slice(-6)}` : ''}:

📈 **Key Patterns Detected:**
• High-frequency micro-transactions (possible dust attacks)
• Large value transfers to privacy coins
• Circular transaction flows indicating potential mixing

⏰ **Timing Analysis:**
• Most active during off-peak hours (2-6 AM UTC)
• Burst activity patterns every 3-4 days
• Coordinated with other flagged addresses

🔍 **Notable Connections:**
• 15 direct connections to high-risk addresses
• 3 degrees of separation from known criminal entities`;
    }

    if (lowerQuestion.includes('compliance') || lowerQuestion.includes('flag')) {
      return `Compliance assessment${wallet ? ` for ${wallet.slice(0, 8)}...${wallet.slice(-6)}` : ''}:

⚠️ **Regulatory Flags:**
• OFAC sanctions list match (indirect)
• AML threshold violations detected
• Cross-border transaction reporting required

📋 **Required Actions:**
• File Suspicious Activity Report (SAR)
• Enhanced due diligence procedures
• Transaction monitoring enhancement

🏛️ **Jurisdictional Considerations:**
• Subject to EU AMLD5 requirements
• US FinCEN reporting obligations
• Local KYC/AML compliance needed`;
    }

    return `I understand you're asking about "${question}". Here's my analysis:

🤖 **AI Analysis:**
Based on the current blockchain data and transaction patterns, I can provide insights on wallet behavior, risk assessment, and compliance considerations.

💡 **Suggestions:**
• Try asking about specific risk factors
• Request transaction pattern analysis
• Inquire about compliance requirements
• Ask for connected address analysis

Would you like me to focus on any particular aspect of the blockchain analysis?`;
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const clearChat = () => {
    setMessages([]);
  };

  return (
    <div className="flex flex-col h-full glass-card">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border-accent">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-emerald-500/20">
            <FaRobot className="text-emerald-400" size={16} />
          </div>
          <div>
            <h3 className="font-semibold text-foreground">Ask AI Assistant</h3>
            <p className="text-xs text-foreground-muted">Blockchain Analysis & Insights</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {messages.length > 0 && (
            <Tooltip content="Clear chat history" contentVi="Xóa lịch sử trò chuyện">
              <button
                onClick={clearChat}
                className="p-2 transition-colors rounded-lg hover:bg-background-tertiary text-foreground-muted hover:text-foreground"
              >
                <FaHistory size={14} />
              </button>
            </Tooltip>
          )}
          {onClose && (
            <Tooltip content="Close AI panel" contentVi="Đóng bảng AI">
              <button
                onClick={onClose}
                className="p-2 transition-colors rounded-lg hover:bg-background-tertiary text-foreground-muted hover:text-foreground"
              >
                <FaTimes size={14} />
              </button>
            </Tooltip>
          )}
        </div>
      </div>

      {/* Chat Messages */}
      <div
        ref={chatContainerRef}
        className="flex-1 p-4 space-y-4 overflow-y-auto"
        style={{ maxHeight: 'calc(100vh - 200px)' }}
      >
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-emerald-500/10">
              <FaRobot className="text-emerald-400" size={24} />
            </div>
            <h4 className="mb-2 text-lg font-medium text-foreground">Welcome to AI Assistant</h4>
            <p className="mb-6 text-sm text-foreground-muted max-w-xs">
              Ask me anything about blockchain analysis, wallet risk assessment, or compliance insights.
            </p>

            {/* Suggested Questions */}
            <div className="w-full max-w-sm space-y-2">
              <p className="text-xs font-medium text-foreground-muted">Suggested questions:</p>
              {suggestedQuestions.slice(0, 3).map((question, index) => (
                <button
                  key={index}
                  onClick={() => handleSendMessage(question)}
                  className="w-full p-2 text-xs text-left transition-colors border rounded-lg border-border-accent hover:bg-background-tertiary text-foreground-muted hover:text-foreground"
                >
                  <FaLightbulb className="inline mr-2" size={10} />
                  {question}
                </button>
              ))}
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] p-3 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-emerald-500/20 text-emerald-100 border border-emerald-500/30'
                    : 'bg-background-secondary border border-border-accent text-foreground'
                }`}
              >
                {message.isLoading ? (
                  <div className="flex items-center gap-2">
                    <FaSpinner className="animate-spin" size={14} />
                    <span className="text-sm">AI is thinking...</span>
                  </div>
                ) : (
                  <>
                    <div className="text-sm whitespace-pre-wrap vietnamese-text">
                      {message.content}
                    </div>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs opacity-60">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </span>
                      {message.type === 'ai' && (
                        <div className="flex items-center gap-1">
                          <button
                            onClick={() => copyToClipboard(message.content)}
                            className="p-1 transition-colors rounded hover:bg-background-tertiary"
                          >
                            <FaCopy size={10} />
                          </button>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to Top Button */}
      {showScrollToTop && (
        <button
          onClick={scrollToTop}
          className="absolute p-2 transition-all duration-300 border rounded-full shadow-lg right-6 bottom-20 bg-background-secondary/90 border-border-accent hover:bg-background-tertiary backdrop-blur-sm"
        >
          <FaArrowUp size={12} />
        </button>
      )}

      {/* Input Area */}
      <div className="p-4 border-t border-border-accent">
        <div className="flex gap-2">
          <div className="relative flex-1">
            <textarea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={selectedWallet ? `Ask about wallet ${selectedWallet.slice(0, 8)}...` : "Ask me about blockchain analysis..."}
              className="w-full p-3 pr-12 text-sm border rounded-lg resize-none bg-background-secondary border-border-accent text-foreground placeholder-foreground-muted focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50"
              rows={1}
              style={{ minHeight: '44px', maxHeight: '120px' }}
              disabled={isLoading}
            />
            <button
              onClick={() => handleSendMessage()}
              disabled={!inputValue.trim() || isLoading}
              className="absolute p-2 transition-colors rounded-lg right-2 top-2 hover:bg-background-tertiary disabled:opacity-50 disabled:cursor-not-allowed text-emerald-400 hover:text-emerald-300"
            >
              {isLoading ? <FaSpinner className="animate-spin" size={14} /> : <FaPaperPlane size={14} />}
            </button>
          </div>
        </div>

        {/* Quick Actions */}
        {messages.length === 0 && (
          <div className="flex gap-2 mt-3">
            {suggestedQuestions.slice(3, 6).map((question, index) => (
              <button
                key={index}
                onClick={() => handleSendMessage(question)}
                className="px-3 py-1 text-xs transition-colors border rounded-full border-border-accent hover:bg-background-tertiary text-foreground-muted hover:text-foreground"
              >
                {question.split(' ').slice(0, 3).join(' ')}...
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AskAIPanel;
