import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { LayoutProvider, useLayout } from '../LayoutManager';

// Mock window object for testing
const mockWindow = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });
};

// Test component that uses the layout hook
const TestComponent: React.FC = () => {
  const { layout, getComponentPosition, getViewportInfo } = useLayout();
  
  return (
    <div data-testid="test-component">
      <div data-testid="screen-size">{layout.screenSize}</div>
      <div data-testid="is-mobile">{layout.isMobile.toString()}</div>
      <div data-testid="is-tablet">{layout.isTablet.toString()}</div>
      <div data-testid="is-desktop">{layout.isDesktop.toString()}</div>
      <div data-testid="control-panel-position">
        {JSON.stringify(getComponentPosition('controlPanel'))}
      </div>
      <div data-testid="viewport-info">
        {JSON.stringify(getViewportInfo())}
      </div>
    </div>
  );
};

describe('LayoutManager', () => {
  beforeEach(() => {
    // Reset window size before each test
    mockWindow(1024, 768);
  });

  afterEach(() => {
    // Clean up any event listeners
    jest.clearAllMocks();
  });

  it('should provide layout context to children', () => {
    render(
      <LayoutProvider>
        <TestComponent />
      </LayoutProvider>
    );

    expect(screen.getByTestId('test-component')).toBeInTheDocument();
  });

  it('should detect mobile screen size correctly', () => {
    mockWindow(375, 667); // iPhone size

    render(
      <LayoutProvider>
        <TestComponent />
      </LayoutProvider>
    );

    expect(screen.getByTestId('screen-size')).toHaveTextContent('sm');
    expect(screen.getByTestId('is-mobile')).toHaveTextContent('true');
    expect(screen.getByTestId('is-tablet')).toHaveTextContent('false');
    expect(screen.getByTestId('is-desktop')).toHaveTextContent('false');
  });

  it('should detect tablet screen size correctly', () => {
    mockWindow(768, 1024); // iPad size

    render(
      <LayoutProvider>
        <TestComponent />
      </LayoutProvider>
    );

    expect(screen.getByTestId('screen-size')).toHaveTextContent('md');
    expect(screen.getByTestId('is-mobile')).toHaveTextContent('false');
    expect(screen.getByTestId('is-tablet')).toHaveTextContent('true');
    expect(screen.getByTestId('is-desktop')).toHaveTextContent('false');
  });

  it('should detect desktop screen size correctly', () => {
    mockWindow(1920, 1080); // Desktop size

    render(
      <LayoutProvider>
        <TestComponent />
      </LayoutProvider>
    );

    expect(screen.getByTestId('screen-size')).toHaveTextContent('2xl');
    expect(screen.getByTestId('is-mobile')).toHaveTextContent('false');
    expect(screen.getByTestId('is-tablet')).toHaveTextContent('false');
    expect(screen.getByTestId('is-desktop')).toHaveTextContent('true');
  });

  it('should provide different control panel positions for different screen sizes', () => {
    // Test mobile
    mockWindow(375, 667);
    const { rerender } = render(
      <LayoutProvider>
        <TestComponent />
      </LayoutProvider>
    );

    let controlPanelPosition = JSON.parse(screen.getByTestId('control-panel-position').textContent || '{}');
    expect(controlPanelPosition.position).toContain('bottom');

    // Test desktop
    mockWindow(1920, 1080);
    rerender(
      <LayoutProvider>
        <TestComponent />
      </LayoutProvider>
    );

    controlPanelPosition = JSON.parse(screen.getByTestId('control-panel-position').textContent || '{}');
    expect(controlPanelPosition.position).toContain('top');
  });

  it('should provide correct viewport information', () => {
    mockWindow(1024, 768);

    render(
      <LayoutProvider>
        <TestComponent />
      </LayoutProvider>
    );

    const viewportInfo = JSON.parse(screen.getByTestId('viewport-info').textContent || '{}');
    expect(viewportInfo.width).toBe(1024);
    expect(viewportInfo.height).toBe(768);
    expect(viewportInfo.headerHeight).toBe(80);
  });

  it('should handle window resize events', () => {
    mockWindow(1024, 768);

    render(
      <LayoutProvider>
        <TestComponent />
      </LayoutProvider>
    );

    // Initially desktop
    expect(screen.getByTestId('is-desktop')).toHaveTextContent('true');

    // Simulate window resize to mobile
    act(() => {
      mockWindow(375, 667);
      window.dispatchEvent(new Event('resize'));
    });

    // Should now be mobile
    expect(screen.getByTestId('is-mobile')).toHaveTextContent('true');
    expect(screen.getByTestId('is-desktop')).toHaveTextContent('false');
  });

  it('should provide correct z-index hierarchy', () => {
    render(
      <LayoutProvider>
        <TestComponent />
      </LayoutProvider>
    );

    const { getComponentPosition } = useLayout();
    
    // Test z-index hierarchy
    expect(getComponentPosition('tooltip').zIndex).toBe(9999);
    expect(getComponentPosition('controlPanel').zIndex).toBe(45);
    expect(getComponentPosition('leftSidebar').zIndex).toBe(40);
    expect(getComponentPosition('walletAnalysis').zIndex).toBe(35);
  });
});
