import React, { useState, useEffect, useRef } from 'react';
import {
  FaChartLine,
  FaMemory,
  FaEye,
  FaCog,
  FaExclamationTriangle,
  FaCheckCircle,
  FaInfoCircle,
  FaChevronUp,
  FaChevronDown,
  FaTimes
} from 'react-icons/fa';
// Performance manager will be imported dynamically
type PerformanceMetrics = any;
type PerformanceConfig = any;
import Tooltip from './Tooltip';

interface PerformanceMonitorProps {
  onOpenSettings?: () => void;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  onOpenSettings
}) => {
  const [isMounted, setIsMounted] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [config, setConfig] = useState<PerformanceConfig | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [performanceStatus, setPerformanceStatus] = useState<'good' | 'warning' | 'critical'>('good');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLButtonElement>(null);

  // Client-side initialization
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsMounted(true);

      // Dynamically import performance manager
      import('@/utils/performanceManager').then(({ performanceManager }) => {
        setMetrics(performanceManager.getMetrics());
        setConfig(performanceManager.getConfig());
      }).catch(error => {
        console.warn('Failed to load performance manager:', error);
      });
    }
  }, []);

  // Click outside handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isDropdownOpen &&
        dropdownRef.current &&
        triggerRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isDropdownOpen) {
        setIsDropdownOpen(false);
        triggerRef.current?.focus();
      }
    };

    if (isMounted) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isDropdownOpen, isMounted]);

  useEffect(() => {
    if (!isMounted) return;

    const updateMetrics = async () => {
      try {
        const { performanceManager } = await import('@/utils/performanceManager');
        const newMetrics = performanceManager.getMetrics();
        const newConfig = performanceManager.getConfig();

        setMetrics(newMetrics);
        setConfig(newConfig);

        // Determine performance status
        const fpsRatio = newMetrics.fps / newConfig.frameRateLimit;
        if (fpsRatio >= 0.8) {
          setPerformanceStatus('good');
        } else if (fpsRatio >= 0.6) {
          setPerformanceStatus('warning');
        } else {
          setPerformanceStatus('critical');
        }

        // Auto-optimize if enabled and performance is poor
        if (newConfig.autoOptimize && fpsRatio < 0.6) {
          performanceManager.autoOptimize();
        }
      } catch (error) {
        console.warn('Failed to update performance metrics:', error);
      }
    };

    // Initial update
    updateMetrics();

    // Set up interval for regular updates
    const interval = setInterval(updateMetrics, 1000);

    return () => clearInterval(interval);
  }, [isMounted]);

  const getTriggerPosition = () => {
    // Position trigger button at bottom center, avoiding conflicts
    return 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-30';
  };

  const getDropdownPosition = () => {
    // Dropup positioning to avoid mobile navigation conflicts
    // Ensure full responsiveness with proper constraints
    return 'fixed bottom-20 left-2 right-2 z-30 max-w-sm mx-auto sm:left-4 sm:right-4 sm:max-w-md';
  };

  const getStatusColor = () => {
    switch (performanceStatus) {
      case 'good':
        return 'text-green-400 border-green-500/30 bg-green-500/10';
      case 'warning':
        return 'text-yellow-400 border-yellow-500/30 bg-yellow-500/10';
      case 'critical':
        return 'text-red-400 border-red-500/30 bg-red-500/10';
      default:
        return 'text-foreground-muted border-border-accent bg-background-secondary/50';
    }
  };

  const getStatusIcon = () => {
    switch (performanceStatus) {
      case 'good':
        return <FaCheckCircle className="text-green-400" />;
      case 'warning':
        return <FaExclamationTriangle className="text-yellow-400" />;
      case 'critical':
        return <FaExclamationTriangle className="text-red-400" />;
      default:
        return <FaInfoCircle className="text-foreground-muted" />;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getQualityBadge = () => {
    if (!config) return null;

    const qualityColors: Record<string, string> = {
      low: 'bg-red-500/20 text-red-400 border-red-500/30',
      medium: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30',
      high: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      ultra: 'bg-purple-500/20 text-purple-400 border-purple-500/30'
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs border ${qualityColors[config.renderingQuality] || qualityColors.medium}`}>
        {config.renderingQuality.toUpperCase()}
      </span>
    );
  };

  // Don't render until mounted and data is available
  if (!isMounted || !metrics || !config) {
    return null;
  }

  return (
    <div>
      {/* Trigger Button */}
      <div className={getTriggerPosition()}>
        <Tooltip
          content="Performance Monitor - Click to view detailed metrics"
          contentVi="Giám sát Hiệu suất - Nhấp để xem số liệu chi tiết"
          position="auto"
        >
          <button
            ref={triggerRef}
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                setIsDropdownOpen(!isDropdownOpen);
              }
            }}
            className={`
              w-14 h-14 glass-card rounded-full border-2 transition-all duration-300
              flex flex-col items-center justify-center gap-1 shadow-lg hover:shadow-glow
              focus:outline-none focus:ring-2 focus:ring-accent-500/50 focus:ring-offset-2 focus:ring-offset-background-primary
              ${getStatusColor()}
              ${isDropdownOpen ? 'scale-110 border-accent-500/50' : 'hover:scale-105'}
            `}
            aria-expanded={isDropdownOpen}
            aria-haspopup="true"
            aria-label="Open performance monitor"
          >
            {/* Status Icon */}
            <div className="flex items-center justify-center">
              {getStatusIcon()}
            </div>

            {/* FPS Display */}
            <div className="text-xs font-bold leading-none">
              {metrics.fps.toFixed(0)}
            </div>

            {/* Dropdown Indicator */}
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-background-secondary border border-border-accent rounded-full flex items-center justify-center">
              {isDropdownOpen ? (
                <FaChevronDown size={8} className="text-foreground-muted" />
              ) : (
                <FaChevronUp size={8} className="text-foreground-muted" />
              )}
            </div>
          </button>
        </Tooltip>
      </div>

      {/* Dropdown Panel */}
      {isDropdownOpen && (
        <div className={getDropdownPosition()}>
          <div
            ref={dropdownRef}
            className={`
              glass-card rounded-xl border shadow-glow animate-slide-up
              ${getStatusColor()} overflow-hidden
            `}
            role="dialog"
            aria-labelledby="performance-monitor-title"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-border-accent/50">
              <div className="flex items-center gap-3">
                <FaChartLine size={16} className="text-accent-400" />
                <div>
                  <h3 id="performance-monitor-title" className="font-semibold text-sm text-foreground">
                    Performance Monitor
                  </h3>
                  <div className="flex items-center gap-2 mt-1">
                    {getQualityBadge()}
                    <span className="text-xs text-foreground-muted">
                      {performanceStatus === 'good' && 'Optimal'}
                      {performanceStatus === 'warning' && 'Warning'}
                      {performanceStatus === 'critical' && 'Critical'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Settings Button */}
                {onOpenSettings && (
                  <Tooltip
                    content="Open Performance Settings Panel"
                    contentVi="Mở Bảng Cài đặt Hiệu suất"
                    position="auto"
                  >
                    <button
                      onClick={onOpenSettings}
                      className="p-2 hover:bg-background-tertiary rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-accent-500/50"
                      aria-label="Open performance settings"
                    >
                      <FaCog size={14} className="text-foreground-muted hover:text-accent-400 transition-colors" />
                    </button>
                  </Tooltip>
                )}

                {/* Close Button */}
                <Tooltip
                  content="Close"
                  contentVi="Đóng"
                  position="left"
                >
                  <button
                    onClick={() => setIsDropdownOpen(false)}
                    className="p-2 hover:bg-background-tertiary rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-accent-500/50"
                    aria-label="Close performance monitor"
                  >
                    <FaTimes size={14} className="text-foreground-muted hover:text-red-400 transition-colors" />
                  </button>
                </Tooltip>
              </div>
            </div>

            {/* Metrics Grid - Responsive Layout */}
            <div className="p-3 sm:p-4 space-y-3 sm:space-y-4">
              {/* Primary Metrics Row - Stack on mobile */}
              <div className="grid grid-cols-1 xs:grid-cols-2 gap-3 sm:gap-4">
                {/* FPS Metric */}
                <div className="glass-card p-2 sm:p-3 rounded-lg border border-border-accent/30 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <FaEye size={12} className="text-accent-400 flex-shrink-0" />
                    <span className="text-xs font-medium text-foreground-muted uppercase tracking-wide truncate">FPS</span>
                  </div>
                  <div className="space-y-2">
                    <div className="text-base sm:text-lg font-bold text-foreground">
                      {metrics.fps.toFixed(1)}
                    </div>
                    <div className="w-full bg-background-tertiary rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          metrics.fps >= config.frameRateLimit * 0.8 ? 'bg-green-400' :
                          metrics.fps >= config.frameRateLimit * 0.6 ? 'bg-yellow-400' : 'bg-red-400'
                        }`}
                        style={{ width: `${Math.min((metrics.fps / config.frameRateLimit) * 100, 100)}%` }}
                      />
                    </div>
                    <div className="text-xs text-foreground-muted truncate">
                      Target: {config.frameRateLimit}
                    </div>
                  </div>
                </div>

                {/* Memory Metric */}
                <div className="glass-card p-2 sm:p-3 rounded-lg border border-border-accent/30 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <FaMemory size={12} className="text-accent-400 flex-shrink-0" />
                    <span className="text-xs font-medium text-foreground-muted uppercase tracking-wide truncate">Memory</span>
                  </div>
                  <div className="space-y-2">
                    <div className="text-base sm:text-lg font-bold text-foreground truncate">
                      {formatBytes(metrics.memoryUsage * 1024 * 1024)}
                    </div>
                    <div className="text-xs text-foreground-muted truncate">
                      Cache: {config.dataCacheLimit}MB
                    </div>
                  </div>
                </div>
              </div>

              {/* Secondary Metrics Row - Stack on mobile */}
              <div className="grid grid-cols-1 xs:grid-cols-2 gap-3 sm:gap-4">
                {/* Frame Time */}
                <div className="glass-card p-2 sm:p-3 rounded-lg border border-border-accent/30 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <FaChartLine size={12} className="text-accent-400 flex-shrink-0" />
                    <span className="text-xs font-medium text-foreground-muted uppercase tracking-wide truncate">Frame Time</span>
                  </div>
                  <div className="text-base sm:text-lg font-bold text-foreground">
                    {metrics.frameTime.toFixed(1)}ms
                  </div>
                </div>

                {/* Node Count */}
                <div className="glass-card p-2 sm:p-3 rounded-lg border border-border-accent/30 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-3 h-3 bg-blue-400 rounded-full flex-shrink-0"></div>
                    <span className="text-xs font-medium text-foreground-muted uppercase tracking-wide truncate">Nodes</span>
                  </div>
                  <div className="text-base sm:text-lg font-bold text-foreground">
                    {metrics.nodeCount}
                  </div>
                  <div className="text-xs text-foreground-muted truncate">
                    Max: {config.maxNodes}
                  </div>
                </div>
              </div>

              {/* Status Section */}
              <div className="pt-4 border-t border-border-accent/50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    {getStatusIcon()}
                    <span className="text-sm font-medium text-foreground">
                      {performanceStatus === 'good' && 'Performance Optimal'}
                      {performanceStatus === 'warning' && 'Performance Warning'}
                      {performanceStatus === 'critical' && 'Performance Critical'}
                    </span>
                  </div>

                  {config.autoOptimize && (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-xs text-foreground-muted">Auto-Optimize</span>
                    </div>
                  )}
                </div>

                {performanceStatus !== 'good' && (
                  <div className="p-3 bg-background-tertiary/50 rounded-lg border border-border-accent/30">
                    <div className="text-xs text-foreground-muted">
                      {performanceStatus === 'warning' && 'Consider reducing visual effects or node count for better performance.'}
                      {performanceStatus === 'critical' && 'Performance severely degraded. Please check settings or reduce quality.'}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Detailed Metrics (expandable) */}
            {showDetails && (
              <div className="border-t border-border-accent/50 pt-4 animate-slide-down">
                <h4 className="text-xs font-medium text-foreground-muted uppercase tracking-wide mb-3">
                  Advanced Metrics
                </h4>
                <div className="grid grid-cols-1 gap-3">
                  <div className="flex justify-between items-center p-2 bg-background-tertiary/30 rounded">
                    <span className="text-xs text-foreground-muted">Render Time</span>
                    <span className="text-xs font-medium text-foreground">{metrics.renderTime.toFixed(2)}ms</span>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-background-tertiary/30 rounded">
                    <span className="text-xs text-foreground-muted">GPU Load</span>
                    <span className="text-xs font-medium text-foreground">{metrics.gpuLoad.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-background-tertiary/30 rounded">
                    <span className="text-xs text-foreground-muted">Update Interval</span>
                    <span className="text-xs font-medium text-foreground">{config.realTimeUpdateInterval / 1000}s</span>
                  </div>
                </div>
              </div>
            )}

            {/* Toggle Details Footer */}
            <div className="border-t border-border-accent/50 p-3">
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="w-full py-2 text-xs text-foreground-muted hover:text-accent-400 transition-colors flex items-center justify-center gap-2 hover:bg-background-tertiary/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-500/50"
                aria-expanded={showDetails}
              >
                {showDetails ? (
                  <>
                    <span>Hide Advanced Metrics</span>
                    <FaChevronUp size={10} />
                  </>
                ) : (
                  <>
                    <span>Show Advanced Metrics</span>
                    <FaChevronDown size={10} />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
