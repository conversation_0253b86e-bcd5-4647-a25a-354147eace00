import React, { useEffect, useRef, useState, useMemo, useCallback, forwardRef } from 'react';
import { GraphData, Node } from '@/services/neo4jService';
import { truncateAddress } from '@/utils/graphUtils';
import { VisualizationOptimizer, DEFAULT_OPTIMIZATION_CONFIG, easingFunctions } from '@/utils/visualizationOptimization';
import { imageCache, drawCircularImage, generatePlaceholderImage } from '@/utils/imageCache';

interface BubbleMapProps {
  graphData: GraphData;
  onNodeClick: (node: Node) => void;
  onLinkClick?: (sourceNode: Node, targetNode: Node, link: EnhancedLink) => void;
  showRiskIndicators?: boolean;
  riskScores?: { [address: string]: number };
  transactionVolumes?: { [linkId: string]: number };
  alertedWallets?: Set<string>;
}

// Enhanced node interface with animation properties
export interface EnhancedNode extends Node {
  // Animation properties
  animationScale?: number;
  animationOpacity?: number;
  pulsePhase?: number;
  glowIntensity?: number;
  hoverScale?: number;

  // Visual properties
  gradient?: CanvasGradient;
  shadowBlur?: number;
  shadowColor?: string;

  // Activity properties
  isActive?: boolean;
  activityLevel?: number;
  riskLevel?: 'low' | 'medium' | 'high';

  // Performance properties
  lastUpdate?: number;
  needsRedraw?: boolean;
}

// Enhanced link interface
interface EnhancedLink {
  source: string | EnhancedNode;
  target: string | EnhancedNode;
  value?: number;
  animated?: boolean;
  flowDirection?: 'bidirectional' | 'source-to-target' | 'target-to-source';
  animationOffset?: number;
  intensity?: number;
  highlighted?: boolean;
}

// Enhanced color schemes inspired by bubblemaps.io with improved gradients
const NODE_COLORS = {
  wallet: {
    primary: '#4F46E5', // Deeper indigo
    secondary: '#312E81',
    tertiary: '#1E1B4B',
    glow: '#818CF8',
    gradient: ['#6366F1', '#4F46E5', '#312E81'],
    hoverGradient: ['#818CF8', '#6366F1', '#4F46E5']
  },
  exchange: {
    primary: '#059669', // Rich emerald
    secondary: '#047857',
    tertiary: '#064E3B',
    glow: '#34D399',
    gradient: ['#10B981', '#059669', '#047857'],
    hoverGradient: ['#6EE7B7', '#10B981', '#059669']
  },
  contract: {
    primary: '#7C3AED', // Vibrant purple
    secondary: '#5B21B6',
    tertiary: '#4C1D95',
    glow: '#C4B5FD',
    gradient: ['#8B5CF6', '#7C3AED', '#5B21B6'],
    hoverGradient: ['#DDD6FE', '#8B5CF6', '#7C3AED']
  },
  flagged: {
    primary: '#DC2626', // Strong red
    secondary: '#991B1B',
    tertiary: '#7F1D1D',
    glow: '#FCA5A5',
    gradient: ['#EF4444', '#DC2626', '#991B1B'],
    hoverGradient: ['#FCA5A5', '#EF4444', '#DC2626']
  },
  bridge: {
    primary: '#D97706', // Warm amber
    secondary: '#92400E',
    tertiary: '#78350F',
    glow: '#FCD34D',
    gradient: ['#F59E0B', '#D97706', '#92400E'],
    hoverGradient: ['#FDE68A', '#F59E0B', '#D97706']
  },
  defi: {
    primary: '#BE185D', // Deep pink
    secondary: '#9D174D',
    tertiary: '#831843',
    glow: '#F9A8D4',
    gradient: ['#EC4899', '#BE185D', '#9D174D'],
    hoverGradient: ['#FBCFE8', '#EC4899', '#BE185D']
  },
  // New node types for enhanced categorization
  whale: {
    primary: '#0891B2', // Cyan for large holders
    secondary: '#0E7490',
    tertiary: '#155E75',
    glow: '#67E8F9',
    gradient: ['#06B6D4', '#0891B2', '#0E7490'],
    hoverGradient: ['#A5F3FC', '#06B6D4', '#0891B2']
  },
  miner: {
    primary: '#EA580C', // Orange for miners
    secondary: '#C2410C',
    tertiary: '#9A3412',
    glow: '#FDBA74',
    gradient: ['#F97316', '#EA580C', '#C2410C'],
    hoverGradient: ['#FED7AA', '#F97316', '#EA580C']
  }
};

// Enhanced animation constants with bubblemaps.io inspired effects
const ANIMATION_CONFIG = {
  pulseSpeed: 0.015, // Slower, more elegant pulse
  hoverScale: 1.4, // More pronounced hover effect
  clickScale: 1.6, // Stronger click feedback
  glowIntensity: 1.2, // Increased glow for better visibility
  fadeSpeed: 0.03, // Smoother fade transitions
  particleSpeed: 1.5, // Optimized particle movement
  connectionAnimSpeed: 0.025, // Smoother connection animations
  rippleSpeed: 0.8, // New ripple effect speed
  breathingSpeed: 0.008, // Subtle breathing animation
  floatAmplitude: 2, // Gentle floating motion
  colorTransitionSpeed: 0.02 // Smooth color transitions
};

// Performance optimization constants
const PERFORMANCE_CONFIG = {
  maxNodes: 1000,
  lodThreshold: 500,
  animationThreshold: 100,
  renderDistance: 2000
};

// Enhanced ref handle interface with modern zoom controls
export interface BubbleMapRefHandle {
  zoomIn: () => void;
  zoomOut: () => void;
  resetView: () => void;
  getCurrentZoom: () => number;
  setZoom: (newZoom: number) => void;
  highlightNode: (nodeId: string) => void;
  resetHighlight: () => void;
  zoomToNode: (nodeId: string, targetZoom?: number) => void;
}

const BubbleMap = forwardRef<BubbleMapRefHandle, BubbleMapProps>(({
  graphData,
  onNodeClick,
  onLinkClick,
  showRiskIndicators = true,
  riskScores = {},
  transactionVolumes = {},
  alertedWallets = new Set()
}, ref) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [draggedNode, setDraggedNode] = useState<EnhancedNode | null>(null);
  const [hoveredNode, setHoveredNode] = useState<EnhancedNode | null>(null);
  const [selectedNode, setSelectedNode] = useState<EnhancedNode | null>(null);
  const [hoveredLink, setHoveredLink] = useState<EnhancedLink | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [nodes, setNodes] = useState<EnhancedNode[]>([]);
  const [links, setLinks] = useState<EnhancedLink[]>([]);
  const requestRef = useRef<number>();
  const animationSpeed = 0.05;
  const relatedNodePullStrength = 10;
  const previousPositionRef = useRef<{x: number, y: number} | null>(null);
  const clickStartPositionRef = useRef<{x: number, y: number} | null>(null);
  const dragThreshold = 3;

  // Enhanced animation and interaction states
  const [isMapDragging, setIsMapDragging] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const panOffsetRef = useRef({ x: 0, y: 0 }); // For immediate updates during dragging
  const [animationTime, setAnimationTime] = useState(0);
  const [particleSystem, setParticleSystem] = useState<any[]>([]);
  const [highlightedNodes, setHighlightedNodes] = useState<Set<string>>(new Set());
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());

  const mapDragStartRef = useRef<{x: number, y: number} | null>(null);
  const zoomPointRef = useRef<{x: number, y: number} | null>(null);
  const lastFrameTime = useRef<number>(0);
  const performanceMode = useRef<boolean>(false);
  const optimizer = useRef<VisualizationOptimizer>(new VisualizationOptimizer(DEFAULT_OPTIMIZATION_CONFIG));

  const minZoom = 0.3;
  const maxZoom = 5;
  const zoomFactor = 0.08; // Reduced from 0.15 for smoother zooming

  // Constants for force-directed layout
  const REPULSION_STRENGTH = 12.0;
  const MIN_DISTANCE = 80;
  const DAMPING = 0.7;
  const SIMULATION_ACTIVE = useRef(false);

  // Enhanced node classification with balance-based categorization
  const classifyNode = useCallback((node: Node): keyof typeof NODE_COLORS => {
    // Priority-based classification
    if (node.tags?.includes('flagged') || node.tags?.includes('suspicious')) return 'flagged';
    if (node.tags?.includes('exchange') || node.tags?.includes('Exchange')) return 'exchange';
    if (node.tags?.includes('contract') || node.tags?.includes('Contract')) return 'contract';
    if (node.tags?.includes('bridge') || node.tags?.includes('Bridge')) return 'bridge';
    if (node.tags?.includes('defi') || node.tags?.includes('DeFi')) return 'defi';
    if (node.tags?.includes('miner') || node.tags?.includes('mining')) return 'miner';

    // Balance-based classification for whales
    if (node.balance) {
      const balance = parseFloat(node.balance);
      if (balance > 10000) return 'whale'; // Large holders get special treatment
    }

    // Transaction count based classification
    if (node.transactionCount && node.transactionCount > 50000) {
      return 'whale'; // High activity addresses
    }

    return 'wallet';
  }, []);

  // Advanced proportional node size calculation with logarithmic scaling
  const calculateNodeSize = useCallback((node: Node, allNodes: Node[] = []): number => {
    const baseSize = 8;
    const maxSize = 45; // Increased max size for better visual distinction
    const minSize = 5;

    // Calculate balance-based size with logarithmic scaling
    let balanceSize = baseSize;
    if (node.balance) {
      const balance = parseFloat(node.balance);
      if (balance > 0) {
        // Use logarithmic scale for better proportional representation
        const logBalance = Math.log10(balance + 1);
        balanceSize = baseSize + (logBalance * 4); // Scale factor of 4

        // Additional scaling for very high balances
        if (balance > 10000) balanceSize += 8;
        else if (balance > 1000) balanceSize += 5;
        else if (balance > 100) balanceSize += 3;
      }
    }

    // Transaction count influence (secondary factor)
    let txSize = 0;
    if (node.transactionCount) {
      const logTx = Math.log10(node.transactionCount + 1);
      txSize = logTx * 2; // Smaller influence than balance
    }

    // Node type multipliers for visual hierarchy
    const nodeType = classifyNode(node);
    let typeMultiplier = 1;
    switch (nodeType) {
      case 'exchange':
        typeMultiplier = 1.4; // Exchanges are important
        break;
      case 'contract':
        typeMultiplier = 1.2;
        break;
      case 'bridge':
        typeMultiplier = 1.3;
        break;
      case 'defi':
        typeMultiplier = 1.25;
        break;
      case 'flagged':
        typeMultiplier = 1.1; // Slightly larger for attention
        break;
      default:
        typeMultiplier = 1;
    }

    // Calculate final size with smooth scaling
    const rawSize = (balanceSize + txSize) * typeMultiplier;
    const finalSize = Math.min(Math.max(rawSize, minSize), maxSize);

    // Add subtle randomization for visual appeal (±5%)
    const randomFactor = 0.95 + (Math.random() * 0.1);

    return Math.round(finalSize * randomFactor);
  }, [classifyNode]);

  // Enhanced gradient creation with multi-stop gradients and lighting effects
  const createNodeGradient = useCallback((
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    radius: number,
    nodeType: keyof typeof NODE_COLORS,
    isHovered: boolean = false,
    glowIntensity: number = 0
  ): CanvasGradient => {
    const colors = NODE_COLORS[nodeType];

    // Create sophisticated radial gradient with lighting effect
    const lightOffsetX = radius * 0.35;
    const lightOffsetY = radius * 0.35;
    const gradient = ctx.createRadialGradient(
      x - lightOffsetX, y - lightOffsetY, 0, // Light source position
      x, y, radius * 1.1 // Gradient extends slightly beyond node
    );

    // Use hover gradient if hovered, otherwise use normal gradient
    const gradientColors = isHovered ? colors.hoverGradient : colors.gradient;

    // Enhanced gradient stops for depth and lighting
    gradient.addColorStop(0, gradientColors[0]); // Highlight
    gradient.addColorStop(0.3, gradientColors[1]); // Mid-tone
    gradient.addColorStop(0.7, gradientColors[2]); // Shadow
    gradient.addColorStop(1, colors.tertiary); // Deep shadow edge

    return gradient;
  }, []);

  // Create glow effect gradient for enhanced visual appeal
  const createGlowGradient = useCallback((
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    radius: number,
    nodeType: keyof typeof NODE_COLORS,
    intensity: number = 1
  ): CanvasGradient => {
    const colors = NODE_COLORS[nodeType];
    const glowRadius = radius * (2 + intensity);

    const glowGradient = ctx.createRadialGradient(x, y, radius, x, y, glowRadius);
    glowGradient.addColorStop(0, `${colors.glow}${Math.round(intensity * 80).toString(16).padStart(2, '0')}`);
    glowGradient.addColorStop(0.5, `${colors.glow}${Math.round(intensity * 40).toString(16).padStart(2, '0')}`);
    glowGradient.addColorStop(1, `${colors.glow}00`);

    return glowGradient;
  }, []);

  // Enhanced risk assessment
  const calculateRiskLevel = useCallback((node: Node): 'low' | 'medium' | 'high' => {
    if (node.tags?.some(tag => ['flagged', 'suspicious', 'blacklist'].includes(tag.toLowerCase()))) {
      return 'high';
    }
    if (node.tags?.some(tag => ['mixer', 'privacy', 'tornado'].includes(tag.toLowerCase()))) {
      return 'medium';
    }
    return 'low';
  }, []);

  // Tạo một map lưu trữ mối quan hệ giữa các node
  const nodeRelationships = useMemo(() => {
    const relationships = new Map<string, string[]>();

    // Khởi tạo danh sách trống cho mỗi node
    nodes.forEach(node => {
      relationships.set(node.id, []);
    });

    // Thêm liên kết giữa các node
    links.forEach(link => {
      const sourceId = typeof link.source === 'string' ? link.source : link.source.id;
      const targetId = typeof link.target === 'string' ? link.target : link.target.id;

      if (relationships.has(sourceId)) {
        relationships.get(sourceId)?.push(targetId);
      }

      if (relationships.has(targetId)) {
        relationships.get(targetId)?.push(sourceId);
      }
    });

    return relationships;
  }, [nodes, links]);

  // Preload images for nodes that have them
  useEffect(() => {
    const imageUrls = graphData.nodes
      .filter(node => node.imageUrl || node.avatar)
      .map(node => node.imageUrl || node.avatar!)
      .filter(Boolean);

    if (imageUrls.length > 0) {
      imageCache.preloadImages(imageUrls).then(() => {
        setLoadedImages(new Set(imageUrls));
      });
    }
  }, [graphData.nodes]);

  // Initialize enhanced nodes and links with professional visual properties
  useEffect(() => {
    const enhancedNodes: EnhancedNode[] = graphData.nodes.map((node, index) => {
      const nodeType = classifyNode(node);
      const size = calculateNodeSize(node);
      const riskLevel = calculateRiskLevel(node);

      return {
        ...node,
        // Physics properties
        vx: 0,
        vy: 0,
        targetX: node.x,
        targetY: node.y,
        size,

        // Visual enhancement properties
        animationScale: 1,
        animationOpacity: 1,
        pulsePhase: Math.random() * Math.PI * 2, // Random phase for pulse animation
        glowIntensity: 0,
        hoverScale: 1,

        // Activity and risk properties
        isActive: false,
        activityLevel: Math.random() * 0.5 + 0.5, // Random activity level
        riskLevel,

        // Color assignment based on type
        color: NODE_COLORS[nodeType].primary,

        // Performance properties
        lastUpdate: Date.now(),
        needsRedraw: true
      };
    });

    const enhancedLinks: EnhancedLink[] = graphData.links.map(link => ({
      ...link,
      animated: Math.random() > 0.7, // 30% of links are animated
      flowDirection: Math.random() > 0.5 ? 'source-to-target' : 'bidirectional',
      animationOffset: Math.random() * Math.PI * 2,
      intensity: (link.value || 1) / 10,
      highlighted: false
    }));

    setNodes(enhancedNodes);
    setLinks(enhancedLinks);

    // Initialize particle system for active transactions
    const particles = [];
    for (let i = 0; i < Math.min(20, enhancedLinks.length); i++) {
      if (enhancedLinks[i]?.animated) {
        particles.push({
          linkIndex: i,
          progress: Math.random(),
          speed: 0.01 + Math.random() * 0.02,
          size: 2 + Math.random() * 3,
          opacity: 0.8
        });
      }
    }
    setParticleSystem(particles);
  }, [graphData, classifyNode, calculateNodeSize, calculateRiskLevel]);

  // Apply force-directed layout to prevent node overlap
  const applyForces = useCallback(() => {
    if (!canvasRef.current || nodes.length === 0) return;

    let forceApplied = false;
    const updatedNodes = [...nodes];

    // First, calculate repulsion forces between all pairs of nodes
    for (let i = 0; i < updatedNodes.length; i++) {
      const nodeA = updatedNodes[i];
      if (nodeA.x === undefined || nodeA.y === undefined ||
          nodeA.targetX === undefined || nodeA.targetY === undefined) continue;

      // Skip the dragged node
      if (draggedNode && draggedNode.id === nodeA.id) continue;

      let fx = 0, fy = 0;

      // Calculate repulsion from other nodes
      for (let j = 0; j < updatedNodes.length; j++) {
        if (i === j) continue;

        const nodeB = updatedNodes[j];
        if (nodeB.x === undefined || nodeB.y === undefined ||
            nodeB.targetX === undefined || nodeB.targetY === undefined) continue;

        const dx = nodeA.targetX - nodeB.targetX;
        const dy = nodeA.targetY - nodeB.targetY;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // Only apply repulsion if nodes are too close
        if (distance > 0 && distance < MIN_DISTANCE * zoomLevel) {
          // Calculate repulsion (inverse square law)
          const force = REPULSION_STRENGTH * (MIN_DISTANCE * zoomLevel) / (distance * distance);

          // Normalized direction
          const unitX = dx / distance;
          const unitY = dy / distance;

          // Accumulate forces (scale by node size)
          const nodeSizeA = nodeA.size || 10;
          const nodeSizeB = nodeB.size || 10;
          const sizeFactor = (nodeSizeA + nodeSizeB) / 20;

          fx += unitX * force * sizeFactor;
          fy += unitY * force * sizeFactor;
          forceApplied = true;
        }
      }

      // Apply attraction for connected nodes
      const connectedNodeIds = nodeRelationships.get(nodeA.id) || [];
      for (const connectedId of connectedNodeIds) {
        const connectedNode = updatedNodes.find(n => n.id === connectedId);
        if (!connectedNode || connectedNode.targetX === undefined || connectedNode.targetY === undefined) continue;

        const dx = connectedNode.targetX - nodeA.targetX;
        const dy = connectedNode.targetY - nodeA.targetY;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // Only attract if nodes are too far apart
        if (distance > MIN_DISTANCE * 2 * zoomLevel) {
          const force = 0.03 * (distance - MIN_DISTANCE * 2 * zoomLevel);

          // Normalized direction
          const unitX = dx / distance;
          const unitY = dy / distance;

          fx += unitX * force;
          fy += unitY * force;
          forceApplied = true;
        }
      }

      // Apply forces with damping
      if (Math.abs(fx) > 0.01 || Math.abs(fy) > 0.01) {
        nodeA.targetX += fx * DAMPING;
        nodeA.targetY += fy * DAMPING;
      }
    }

    if (forceApplied) {
      setNodes(updatedNodes);
      SIMULATION_ACTIVE.current = true;
    } else {
      SIMULATION_ACTIVE.current = false;
    }
  }, [nodes, links, draggedNode, nodeRelationships, zoomLevel]);

  // Enhanced zoom function with smooth easing and proper canvas transformation
  const zoom = useCallback((delta: number, zoomPoint: {x: number, y: number}) => {
    // Apply easing to the delta for smoother zoom transitions
    const easedDelta = delta * 0.7; // Reduce the delta by 30% for smoother feel
    const newZoom = Math.max(minZoom, Math.min(maxZoom, zoomLevel + easedDelta));
    if (Math.abs(newZoom - zoomLevel) < 0.001) return; // Prevent micro-adjustments

    // Calculate zoom change ratio
    const zoomRatio = newZoom / zoomLevel;

    // Adjust pan offset to zoom towards the zoom point with smooth interpolation
    const newPanOffset = {
      x: zoomPoint.x - (zoomPoint.x - panOffset.x) * zoomRatio,
      y: zoomPoint.y - (zoomPoint.y - panOffset.y) * zoomRatio
    };

    setPanOffset(newPanOffset);
    panOffsetRef.current = newPanOffset; // Keep ref in sync
    setZoomLevel(newZoom);
  }, [zoomLevel, panOffset]);

  // Enhanced zoom control functions
  const zoomIn = useCallback(() => {
    if (!canvasRef.current) return;
    const canvas = canvasRef.current;
    const center = {
      x: canvas.width / 2,
      y: canvas.height / 2
    };
    zoom(zoomFactor, center);
  }, [zoom]);

  const zoomOut = useCallback(() => {
    if (!canvasRef.current) return;
    const canvas = canvasRef.current;
    const center = {
      x: canvas.width / 2,
      y: canvas.height / 2
    };
    zoom(-zoomFactor, center);
  }, [zoom]);

  // Reset view function
  const resetView = useCallback(() => {
    setZoomLevel(1);
    setPanOffset({ x: 0, y: 0 });
    panOffsetRef.current = { x: 0, y: 0 };
  }, []);

  // Enhanced node selection with visual feedback
  const handleNodeSelection = useCallback((node: EnhancedNode) => {
    setSelectedNode(node);

    // Add selection animation
    setNodes(prev => prev.map(n =>
      n.id === node.id
        ? { ...n, animationScale: ANIMATION_CONFIG.clickScale, isActive: true }
        : { ...n, animationScale: 1, isActive: false }
    ));

    // Animate back to normal scale
    setTimeout(() => {
      setNodes(prev => prev.map(n =>
        n.id === node.id
          ? { ...n, animationScale: 1 }
          : n
      ));
    }, 200);

    onNodeClick(node);
  }, [onNodeClick]);

  // Zoom to a specific node with smooth animation
  const zoomToNode = useCallback((nodeId: string, targetZoom: number = 2.5) => {
    if (!canvasRef.current) return;

    const node = nodes.find(n => n.id === nodeId);
    if (!node || node.x === undefined || node.y === undefined) return;

    const canvas = canvasRef.current;

    // First, add a subtle highlight to show which node we're focusing on
    setNodes(prev => prev.map(n =>
      n.id === nodeId
        ? { ...n, glowIntensity: 0.8, hoverScale: 1.2 }
        : n
    ));

    // Calculate the center of the canvas
    const canvasCenterX = canvas.width / 2;
    const canvasCenterY = canvas.height / 2;

    // Calculate the required pan offset to center the node
    const targetPanX = canvasCenterX - node.x * targetZoom;
    const targetPanY = canvasCenterY - node.y * targetZoom;

    // Animate zoom and pan smoothly
    const startZoom = zoomLevel;
    const startPanX = panOffset.x;
    const startPanY = panOffset.y;
    const duration = 1000; // 1000ms animation for smoother feel
    const startTime = Date.now();

    const animateZoomToPan = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Use easing function for smooth animation (ease-in-out)
      const easeProgress = progress < 0.5
        ? 2 * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 2) / 2;

      // Interpolate zoom and pan
      const currentZoom = startZoom + (targetZoom - startZoom) * easeProgress;
      const currentPanX = startPanX + (targetPanX - startPanX) * easeProgress;
      const currentPanY = startPanY + (targetPanY - startPanY) * easeProgress;

      // Apply the interpolated values
      setZoomLevel(currentZoom);
      setPanOffset({ x: currentPanX, y: currentPanY });
      panOffsetRef.current = { x: currentPanX, y: currentPanY };

      // Continue animation if not finished
      if (progress < 1) {
        requestAnimationFrame(animateZoomToPan);
      } else {
        // Animation complete, add a small delay then select the node
        setTimeout(() => {
          handleNodeSelection(node);

          // Add a pulse effect after selection
          setNodes(prev => prev.map(n =>
            n.id === nodeId
              ? { ...n, animationScale: 1.3, glowIntensity: 1.0 }
              : n
          ));

          // Reset the pulse effect
          setTimeout(() => {
            setNodes(prev => prev.map(n =>
              n.id === nodeId
                ? { ...n, animationScale: 1, glowIntensity: 0.6, hoverScale: 1 }
                : n
            ));
          }, 300);
        }, 100);
      }
    };

    // Start the animation
    requestAnimationFrame(animateZoomToPan);
  }, [nodes, zoomLevel, panOffset, handleNodeSelection]);

  // Professional hover effect handler
  const handleNodeHover = useCallback((node: EnhancedNode | null) => {
    if (hoveredNode?.id !== node?.id) {
      // Reset previous hovered node
      if (hoveredNode) {
        setNodes(prev => prev.map(n =>
          n.id === hoveredNode.id
            ? { ...n, hoverScale: 1, glowIntensity: 0 }
            : n
        ));
      }

      // Set new hovered node with enhanced effects
      if (node) {
        setNodes(prev => prev.map(n =>
          n.id === node.id
            ? { ...n, hoverScale: ANIMATION_CONFIG.hoverScale, glowIntensity: ANIMATION_CONFIG.glowIntensity }
            : n
        ));

        // Highlight connected nodes
        const connectedNodeIds = nodeRelationships.get(node.id) || [];
        setHighlightedNodes(new Set([node.id, ...connectedNodeIds]));
      } else {
        setHighlightedNodes(new Set());
      }

      setHoveredNode(node);
    }
  }, [hoveredNode, nodeRelationships]);

  // Enhanced ref methods for external control
  React.useImperativeHandle(
    ref,
    () => ({
      zoomIn,
      zoomOut,
      resetView,
      getCurrentZoom: () => zoomLevel,
      setZoom: (newZoom: number) => {
        if (!canvasRef.current) return;
        const canvas = canvasRef.current;
        const center = {
          x: canvas.width / 2,
          y: canvas.height / 2
        };
        const delta = newZoom - zoomLevel;
        zoom(delta, center);
      },
      highlightNode: (nodeId: string) => {
        const node = nodes.find(n => n.id === nodeId);
        if (node) {
          handleNodeSelection(node);
        }
      },
      zoomToNode: (nodeId: string, targetZoom?: number) => {
        zoomToNode(nodeId, targetZoom);
      },
      resetHighlight: () => {
        setSelectedNode(null);
        setHoveredNode(null);
        setHighlightedNodes(new Set());
        setNodes(prev => prev.map(n => ({
          ...n,
          animationScale: 1,
          hoverScale: 1,
          glowIntensity: 0,
          isActive: false
        })));
      }
    }),
    [zoomIn, zoomOut, resetView, zoomLevel, zoom, nodes, handleNodeSelection, zoomToNode]
  );

  // Professional rendering function with advanced visual effects
  useEffect(() => {
    if (!canvasRef.current || !nodes.length) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Enhanced performance monitoring with optimizer
    const checkPerformance = () => {
      const nodeCount = nodes.length;
      performanceMode.current = optimizer.current.shouldEnablePerformanceMode(nodeCount);
      return optimizer.current.getOptimizedRenderSettings(nodeCount, zoomLevel);
    };

    // Enhanced background rendering that perfectly matches application theme
    const renderBackground = () => {
      // Clear canvas completely - this ensures no artifacts from previous renders
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Create a complex gradient that matches the body background from globals.css
      // This replicates the multi-layered radial gradients from the application theme
      // IMPORTANT: Use full canvas dimensions to ensure consistent background at all zoom levels

      // Base background color (matches body background-color: #0f172a)
      ctx.fillStyle = '#0f172a';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Layer 1: Blue gradient (matches first radial-gradient at 20% 80%)
      const gradient1 = ctx.createRadialGradient(
        canvas.width * 0.2, canvas.height * 0.8, 0,
        canvas.width * 0.2, canvas.height * 0.8, Math.max(canvas.width, canvas.height) * 0.5
      );
      gradient1.addColorStop(0, 'rgba(37, 99, 235, 0.12)');
      gradient1.addColorStop(0.5, 'rgba(37, 99, 235, 0.06)');
      gradient1.addColorStop(1, 'transparent');
      ctx.fillStyle = gradient1;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Layer 2: Purple gradient (matches second radial-gradient at 80% 20%)
      const gradient2 = ctx.createRadialGradient(
        canvas.width * 0.8, canvas.height * 0.2, 0,
        canvas.width * 0.8, canvas.height * 0.2, Math.max(canvas.width, canvas.height) * 0.5
      );
      gradient2.addColorStop(0, 'rgba(139, 92, 246, 0.12)');
      gradient2.addColorStop(0.5, 'rgba(139, 92, 246, 0.06)');
      gradient2.addColorStop(1, 'transparent');
      ctx.fillStyle = gradient2;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Layer 3: Green gradient (matches third radial-gradient at 40% 40%)
      const gradient3 = ctx.createRadialGradient(
        canvas.width * 0.4, canvas.height * 0.4, 0,
        canvas.width * 0.4, canvas.height * 0.4, Math.max(canvas.width, canvas.height) * 0.6
      );
      gradient3.addColorStop(0, 'rgba(16, 185, 129, 0.08)');
      gradient3.addColorStop(0.5, 'rgba(16, 185, 129, 0.04)');
      gradient3.addColorStop(1, 'transparent');
      ctx.fillStyle = gradient3;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Layer 4: Pink gradient (matches fourth radial-gradient at 60% 60%)
      const gradient4 = ctx.createRadialGradient(
        canvas.width * 0.6, canvas.height * 0.6, 0,
        canvas.width * 0.6, canvas.height * 0.6, Math.max(canvas.width, canvas.height) * 0.45
      );
      gradient4.addColorStop(0, 'rgba(236, 72, 153, 0.06)');
      gradient4.addColorStop(0.5, 'rgba(236, 72, 153, 0.03)');
      gradient4.addColorStop(1, 'transparent');
      ctx.fillStyle = gradient4;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Layer 5: Amber gradient (matches fifth radial-gradient at 90% 10%)
      const gradient5 = ctx.createRadialGradient(
        canvas.width * 0.9, canvas.height * 0.1, 0,
        canvas.width * 0.9, canvas.height * 0.1, Math.max(canvas.width, canvas.height) * 0.55
      );
      gradient5.addColorStop(0, 'rgba(245, 158, 11, 0.04)');
      gradient5.addColorStop(0.5, 'rgba(245, 158, 11, 0.02)');
      gradient5.addColorStop(1, 'transparent');
      ctx.fillStyle = gradient5;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Add subtle grid pattern for professional look
      if (!performanceMode.current) {
        ctx.strokeStyle = 'rgba(148, 163, 184, 0.03)';
        ctx.lineWidth = 1;
        const gridSize = 50 * zoomLevel;

        for (let x = 0; x < canvas.width; x += gridSize) {
          ctx.beginPath();
          ctx.moveTo(x, 0);
          ctx.lineTo(x, canvas.height);
          ctx.stroke();
        }

        for (let y = 0; y < canvas.height; y += gridSize) {
          ctx.beginPath();
          ctx.moveTo(0, y);
          ctx.lineTo(canvas.width, y);
          ctx.stroke();
        }
      }
    };

    // Professional connection rendering with flow animations
    const renderConnections = () => {
      links.forEach((link, linkIndex) => {
        const sourceNode = typeof link.source === 'string'
          ? nodes.find(n => n.id === link.source)
          : link.source as EnhancedNode;
        const targetNode = typeof link.target === 'string'
          ? nodes.find(n => n.id === link.target)
          : link.target as EnhancedNode;

        if (!sourceNode || !targetNode ||
            sourceNode.x === undefined || sourceNode.y === undefined ||
            targetNode.x === undefined || targetNode.y === undefined) return;

        const isHighlighted = link.highlighted ||
          (draggedNode && (sourceNode.id === draggedNode.id || targetNode.id === draggedNode.id)) ||
          (hoveredNode && (sourceNode.id === hoveredNode.id || targetNode.id === hoveredNode.id)) ||
          (hoveredLink && hoveredLink === link);

        // Calculate link properties
        const distance = Math.sqrt(
          Math.pow(targetNode.x - sourceNode.x, 2) +
          Math.pow(targetNode.y - sourceNode.y, 2)
        );

        const baseWidth = Math.max(1, (link.value || 1) * 0.5);
        const isHovered = hoveredLink && hoveredLink === link;
        const width = isHighlighted ? baseWidth * 2 : isHovered ? baseWidth * 1.5 : baseWidth;
        const opacity = isHighlighted ? 0.8 : isHovered ? 0.6 : 0.3;

        // Enhanced connection styling
        ctx.lineWidth = width * zoomLevel;
        ctx.lineCap = 'round';

        if (isHighlighted) {
          // Glowing effect for highlighted connections
          ctx.shadowColor = '#8B5CF6';
          ctx.shadowBlur = 10 * zoomLevel;
          ctx.strokeStyle = `rgba(139, 92, 246, ${opacity})`;
        } else if (isHovered) {
          // Hover effect for links
          ctx.shadowColor = '#60A5FA';
          ctx.shadowBlur = 6 * zoomLevel;
          ctx.strokeStyle = `rgba(96, 165, 250, ${opacity})`;
        } else {
          ctx.shadowBlur = 0;
          ctx.strokeStyle = `rgba(148, 163, 184, ${opacity})`;
        }

        // Draw main connection line
        ctx.beginPath();
        ctx.moveTo(sourceNode.x, sourceNode.y);
        ctx.lineTo(targetNode.x, targetNode.y);
        ctx.stroke();

        // Reset shadow
        ctx.shadowBlur = 0;

        // Animated flow particles for active connections
        if (link.animated && !performanceMode.current) {
          const particle = particleSystem.find(p => p.linkIndex === linkIndex);
          if (particle) {
            const x = sourceNode.x + (targetNode.x - sourceNode.x) * particle.progress;
            const y = sourceNode.y + (targetNode.y - sourceNode.y) * particle.progress;

            // Draw flow particle
            ctx.beginPath();
            ctx.arc(x, y, particle.size * zoomLevel, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(139, 92, 246, ${particle.opacity})`;
            ctx.fill();

            // Update particle position
            particle.progress += particle.speed;
            if (particle.progress > 1) {
              particle.progress = 0;
            }
          }
        }
      });
    };



    // Professional node rendering with advanced visual effects
    const renderNodes = () => {
      nodes.forEach((node) => {
        if (node.x === undefined || node.y === undefined) return;

        const nodeType = classifyNode(node);
        const colors = NODE_COLORS[nodeType];
        const radius = (node.size || 10) * (node.animationScale || 1) * (node.hoverScale || 1);

        // Skip rendering if node is too small (LOD optimization)
        if (performanceMode.current && radius < 2) return;

        const isSelected = selectedNode?.id === node.id;
        const isHovered = hoveredNode?.id === node.id;
        const isDragged = draggedNode?.id === node.id;
        const isHighlighted = highlightedNodes.has(node.id);
        const isRelated = draggedNode && nodeRelationships.get(draggedNode.id)?.includes(node.id);

        // Calculate dynamic properties
        const pulseScale = 1 + Math.sin(animationTime * ANIMATION_CONFIG.pulseSpeed + (node.pulsePhase || 0)) * 0.1;
        const glowIntensity = node.glowIntensity || 0;

        // Enhanced visual state
        let finalRadius = radius;
        let shadowBlur = 0;
        let shadowColor = colors.glow;

        if (isDragged) {
          finalRadius *= 1.3;
          shadowBlur = 20;
          shadowColor = '#F59E0B';
        } else if (isHovered) {
          finalRadius *= 1.2;
          shadowBlur = 15;
        } else if (isSelected || isHighlighted) {
          finalRadius *= pulseScale;
          shadowBlur = 12;
        } else if (isRelated) {
          finalRadius *= 1.1;
          shadowBlur = 8;
          shadowColor = '#F59E0B';
        }

        // Risk-based visual indicators
        if (node.riskLevel === 'high') {
          shadowColor = NODE_COLORS.flagged.glow;
          shadowBlur = Math.max(shadowBlur, 10);
        }

        // Create gradient fill
        const gradient = createNodeGradient(ctx, node.x, node.y, finalRadius, nodeType);

        // Apply glow effect
        if (shadowBlur > 0 && !performanceMode.current) {
          ctx.shadowColor = shadowColor;
          ctx.shadowBlur = shadowBlur * zoomLevel;
        }

        // Check if node has an image and if it's loaded
        const hasImage = node.imageUrl || node.avatar;
        const imageUrl = node.imageUrl || node.avatar;
        const cachedImage = hasImage ? imageCache.getCachedImage(imageUrl!) : null;

        if (hasImage && cachedImage) {
          // Draw image as circular avatar
          ctx.shadowBlur = shadowBlur > 0 && !performanceMode.current ? shadowBlur * zoomLevel : 0;
          ctx.shadowColor = shadowColor;

          drawCircularImage(ctx, cachedImage, node.x, node.y, finalRadius, colors.primary);

          // Reset shadow for border
          ctx.shadowBlur = 0;

          // Draw enhanced border for image nodes
          ctx.beginPath();
          ctx.arc(node.x, node.y, finalRadius, 0, Math.PI * 2);
          ctx.strokeStyle = isSelected || isDragged ? '#FFFFFF' : colors.secondary;
          ctx.lineWidth = (isSelected || isDragged ? 4 : 3) * zoomLevel;
          ctx.stroke();

          // Add inner border for depth
          if (!performanceMode.current) {
            ctx.beginPath();
            ctx.arc(node.x, node.y, finalRadius - 2, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 1 * zoomLevel;
            ctx.stroke();
          }
        } else {
          // Fallback to gradient circle for nodes without images or failed loads
          ctx.beginPath();
          ctx.arc(node.x, node.y, finalRadius, 0, Math.PI * 2);
          ctx.fillStyle = gradient;
          ctx.fill();

          // Draw border with enhanced styling
          ctx.shadowBlur = 0;
          ctx.strokeStyle = isSelected || isDragged ? '#FFFFFF' : colors.secondary;
          ctx.lineWidth = (isSelected || isDragged ? 3 : 2) * zoomLevel;
          ctx.stroke();
        }

        // Draw inner indicator for special node types (only for non-image nodes or as overlay)
        if (!performanceMode.current && (!hasImage || !cachedImage)) {
          if (nodeType === 'exchange') {
            // Exchange indicator
            ctx.beginPath();
            ctx.arc(node.x, node.y, finalRadius * 0.4, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.fill();
          } else if (nodeType === 'contract') {
            // Contract indicator (diamond shape)
            const size = finalRadius * 0.4;
            ctx.beginPath();
            ctx.moveTo(node.x, node.y - size);
            ctx.lineTo(node.x + size, node.y);
            ctx.lineTo(node.x, node.y + size);
            ctx.lineTo(node.x - size, node.y);
            ctx.closePath();
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.fill();
          } else if (nodeType === 'flagged') {
            // Warning indicator
            ctx.beginPath();
            ctx.moveTo(node.x, node.y - finalRadius * 0.4);
            ctx.lineTo(node.x - finalRadius * 0.35, node.y + finalRadius * 0.2);
            ctx.lineTo(node.x + finalRadius * 0.35, node.y + finalRadius * 0.2);
            ctx.closePath();
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fill();
          }
        }

        // Add type indicator badge for image nodes
        if (hasImage && cachedImage && !performanceMode.current) {
          const badgeRadius = finalRadius * 0.25;
          const badgeX = node.x + finalRadius * 0.6;
          const badgeY = node.y - finalRadius * 0.6;

          // Badge background
          ctx.beginPath();
          ctx.arc(badgeX, badgeY, badgeRadius, 0, Math.PI * 2);
          ctx.fillStyle = colors.primary;
          ctx.fill();
          ctx.strokeStyle = '#FFFFFF';
          ctx.lineWidth = 2;
          ctx.stroke();

          // Badge icon based on node type
          ctx.fillStyle = '#FFFFFF';
          ctx.font = `${badgeRadius}px Arial`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';

          let badgeIcon = '';
          switch (nodeType) {
            case 'exchange': badgeIcon = '⇄'; break;
            case 'contract': badgeIcon = '◆'; break;
            case 'flagged': badgeIcon = '⚠'; break;
            case 'bridge': badgeIcon = '🌉'; break;
            case 'defi': badgeIcon = '🏦'; break;
            case 'whale': badgeIcon = '🐋'; break;
            case 'miner': badgeIcon = '⛏'; break;
            default: badgeIcon = '💰'; break;
          }

          ctx.fillText(badgeIcon, badgeX, badgeY);
        }

        // Activity pulse animation for active nodes
        if (node.isActive && !performanceMode.current) {
          const pulseRadius = finalRadius + Math.sin(animationTime * 0.05) * 5;
          ctx.beginPath();
          ctx.arc(node.x, node.y, pulseRadius, 0, Math.PI * 2);
          ctx.strokeStyle = `rgba(139, 92, 246, ${0.3 * (1 - Math.sin(animationTime * 0.05) * 0.5)})`;
          ctx.lineWidth = 2;
          ctx.stroke();
        }

        // Draw labels with enhanced typography
        if (zoomLevel > 0.5) {
          const fontSize = Math.max(8, 10 * zoomLevel);
          const label = node.label || truncateAddress(node.address);

          ctx.font = `${fontSize}px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'top';

          // Label background for better readability
          const textMetrics = ctx.measureText(label);
          const textWidth = textMetrics.width;
          const textHeight = fontSize;
          const padding = 4;

          const labelY = node.y + finalRadius + 8;

          if (!performanceMode.current) {
            // Draw label background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(
              node.x - textWidth / 2 - padding,
              labelY - padding,
              textWidth + padding * 2,
              textHeight + padding * 2
            );
          }

          // Draw label text
          ctx.fillStyle = isSelected || isHovered ? '#FFFFFF' : '#E2E8F0';
          ctx.fillText(label, node.x, labelY);
        }
      });
    };

    // Main rendering function that orchestrates all visual elements
    const renderGraph = () => {
      const renderStartTime = performance.now();

      // Set canvas dimensions
      canvas.width = canvas.clientWidth;
      canvas.height = canvas.clientHeight;

      // Get optimized render settings
      const renderSettings = checkPerformance();

      // Render background BEFORE any transformations to cover entire canvas
      renderBackground();

      // Save the current transformation matrix
      ctx.save();

      // Apply zoom and pan transformations using ref for immediate updates during dragging
      const currentPanOffset = isMapDragging ? panOffsetRef.current : panOffset;
      ctx.translate(currentPanOffset.x, currentPanOffset.y);
      ctx.scale(zoomLevel, zoomLevel);

      // Render connections
      renderConnections();

      // Render nodes with optimization
      renderNodes();

      // Restore the transformation matrix
      ctx.restore();

      // Render UI elements
      if (!renderSettings.performanceMode) {
        // Zoom indicator
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.font = '12px -apple-system, BlinkMacSystemFont, sans-serif';
        ctx.textAlign = 'right';
        ctx.fillText(`${Math.round(zoomLevel * 100)}%`, canvas.width - 15, canvas.height - 15);

        // Performance indicator
        if (renderSettings.performanceMode) {
          ctx.fillStyle = 'rgba(245, 158, 11, 0.8)';
          ctx.font = '10px -apple-system, BlinkMacSystemFont, sans-serif';
          ctx.textAlign = 'left';
          ctx.fillText('Performance Mode', 15, canvas.height - 15);
        }
      }

      // Update performance metrics
      const metrics = optimizer.current.updatePerformanceMetrics(renderStartTime);
      metrics.nodeCount = nodes.length;
    };

    // Enhanced animation loop with professional timing and effects
    const animate = () => {
      const currentTime = Date.now();
      const deltaTime = currentTime - lastFrameTime.current;
      lastFrameTime.current = currentTime;

      // Update animation time for effects
      setAnimationTime(prev => prev + deltaTime * 0.001);

      let needsUpdate = false;

      // Apply physics simulation
      if (!isDragging && !isMapDragging) {
        applyForces();
      }

      // Update node animations and positions
      nodes.forEach(node => {
        if (node.targetX === undefined || node.targetY === undefined ||
            node.x === undefined || node.y === undefined) return;

        // Skip dragged node animation
        if (draggedNode && draggedNode.id === node.id) return;

        // Smooth position interpolation
        const dx = node.targetX - node.x;
        const dy = node.targetY - node.y;

        if (Math.abs(dx) > 0.1 || Math.abs(dy) > 0.1) {
          node.x += dx * animationSpeed;
          node.y += dy * animationSpeed;
          needsUpdate = true;
        }

        // Update visual animation properties
        if (node.hoverScale && node.hoverScale > 1) {
          node.hoverScale = Math.max(1, node.hoverScale - 0.05);
          needsUpdate = true;
        }

        if (node.glowIntensity && node.glowIntensity > 0) {
          node.glowIntensity = Math.max(0, node.glowIntensity - 0.02);
          needsUpdate = true;
        }
      });

      // Update particle system
      particleSystem.forEach(particle => {
        particle.progress += particle.speed;
        if (particle.progress > 1) {
          particle.progress = 0;
        }
        needsUpdate = true;
      });

      // Render the graph
      renderGraph();

      // Continue animation loop - always run at full speed during map dragging for smooth panning
      if (needsUpdate || SIMULATION_ACTIVE.current || !performanceMode.current || isMapDragging) {
        requestRef.current = requestAnimationFrame(animate);
      } else {
        // Reduced frequency for static scenes only when not dragging
        setTimeout(() => {
          requestRef.current = requestAnimationFrame(animate);
        }, 100);
      }
    };

    // Initialize and start animation
    renderGraph();
    requestRef.current = requestAnimationFrame(animate);

    // Cleanup animation on unmount
    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [nodes, links, draggedNode, hoveredLink, nodeRelationships, zoomLevel, applyForces, isDragging, isMapDragging]);

  // Enhanced node detection with canvas transformation support
  const findNodeUnderCursor = useCallback((x: number, y: number): EnhancedNode | null => {
    // Transform mouse coordinates to canvas coordinates using current pan offset
    const currentPanOffset = isMapDragging ? panOffsetRef.current : panOffset;
    const transformedX = (x - currentPanOffset.x) / zoomLevel;
    const transformedY = (y - currentPanOffset.y) / zoomLevel;

    for (const node of nodes) {
      if (node.x === undefined || node.y === undefined) continue;

      const dx = transformedX - node.x;
      const dy = transformedY - node.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // Enhanced hit testing with animation scale consideration
      const hitRadius = (node.size || 10) * (node.hoverScale || 1);
      if (distance <= hitRadius) {
        return node;
      }
    }
    return null;
  }, [nodes, panOffset, zoomLevel]);

  // Enhanced link detection with canvas transformation support
  const findLinkUnderCursor = useCallback((x: number, y: number): { link: EnhancedLink, sourceNode: EnhancedNode, targetNode: EnhancedNode } | null => {
    // Transform mouse coordinates to canvas coordinates using current pan offset
    const currentPanOffset = isMapDragging ? panOffsetRef.current : panOffset;
    const transformedX = (x - currentPanOffset.x) / zoomLevel;
    const transformedY = (y - currentPanOffset.y) / zoomLevel;

    for (const link of links) {
      const sourceNode = typeof link.source === 'string'
        ? nodes.find(n => n.id === link.source)
        : link.source as EnhancedNode;
      const targetNode = typeof link.target === 'string'
        ? nodes.find(n => n.id === link.target)
        : link.target as EnhancedNode;

      if (!sourceNode || !targetNode ||
          sourceNode.x === undefined || sourceNode.y === undefined ||
          targetNode.x === undefined || targetNode.y === undefined) continue;

      // Calculate distance from point to line segment
      const A = transformedX - sourceNode.x;
      const B = transformedY - sourceNode.y;
      const C = targetNode.x - sourceNode.x;
      const D = targetNode.y - sourceNode.y;

      const dot = A * C + B * D;
      const lenSq = C * C + D * D;

      if (lenSq === 0) continue; // Source and target are the same point

      let param = dot / lenSq;
      param = Math.max(0, Math.min(1, param)); // Clamp to line segment

      const xx = sourceNode.x + param * C;
      const yy = sourceNode.y + param * D;

      const dx = transformedX - xx;
      const dy = transformedY - yy;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // Check if click is within link width tolerance
      const baseWidth = Math.max(1, (link.value || 1) * 0.5);
      const clickTolerance = Math.max(8, baseWidth * 2); // Minimum 8px click area

      if (distance <= clickTolerance) {
        return { link, sourceNode, targetNode };
      }
    }

    return null;
  }, [links, nodes, zoomLevel, panOffset, isMapDragging]);

  // Mouse event handlers
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Mouse down handler
    const handleMouseDown = (event: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      const node = findNodeUnderCursor(x, y);
      if (node) {
        // Node dragging
        setDraggedNode(node);
        setIsDragging(true);
        canvas.style.cursor = 'grabbing';
        previousPositionRef.current = { x, y };
        clickStartPositionRef.current = { x, y };
      } else {
        // Check for link click
        const linkResult = findLinkUnderCursor(x, y);
        if (linkResult && onLinkClick) {
          // Handle link click - convert enhanced nodes back to regular nodes
          const sourceNode: Node = {
            id: linkResult.sourceNode.id,
            address: linkResult.sourceNode.address,
            label: linkResult.sourceNode.label,
            balance: linkResult.sourceNode.balance,
            transactionCount: linkResult.sourceNode.transactionCount,
            tags: linkResult.sourceNode.tags,
            color: linkResult.sourceNode.color,
            size: linkResult.sourceNode.size,
            x: linkResult.sourceNode.x,
            y: linkResult.sourceNode.y,
            socialProfiles: linkResult.sourceNode.socialProfiles,
            hasVerifiedSocials: linkResult.sourceNode.hasVerifiedSocials,
            socialScore: linkResult.sourceNode.socialScore
          };

          const targetNode: Node = {
            id: linkResult.targetNode.id,
            address: linkResult.targetNode.address,
            label: linkResult.targetNode.label,
            balance: linkResult.targetNode.balance,
            transactionCount: linkResult.targetNode.transactionCount,
            tags: linkResult.targetNode.tags,
            color: linkResult.targetNode.color,
            size: linkResult.targetNode.size,
            x: linkResult.targetNode.x,
            y: linkResult.targetNode.y,
            socialProfiles: linkResult.targetNode.socialProfiles,
            hasVerifiedSocials: linkResult.targetNode.hasVerifiedSocials,
            socialScore: linkResult.targetNode.socialScore
          };

          onLinkClick(sourceNode, targetNode, linkResult.link);
          return; // Don't start map dragging
        }

        // Map dragging (when clicking on empty space)
        setIsMapDragging(true);
        mapDragStartRef.current = { x, y };
        canvas.style.cursor = 'move';
      }
    };

    // Mouse move handler
    const handleMouseMove = (event: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Node dragging with proper coordinate transformation
      if (isDragging && draggedNode) {
        // Transform mouse coordinates to canvas coordinates using current pan offset
        const currentPanOffset = isMapDragging ? panOffsetRef.current : panOffset;
        const canvasX = (x - currentPanOffset.x) / zoomLevel;
        const canvasY = (y - currentPanOffset.y) / zoomLevel;

        // Calculate delta in canvas space if we have previous position
        let deltaX = 0, deltaY = 0;
        if (previousPositionRef.current) {
          const prevCanvasX = (previousPositionRef.current.x - currentPanOffset.x) / zoomLevel;
          const prevCanvasY = (previousPositionRef.current.y - currentPanOffset.y) / zoomLevel;
          deltaX = canvasX - prevCanvasX;
          deltaY = canvasY - prevCanvasY;
        }

        // Update previous position in screen coordinates
        previousPositionRef.current = { x, y };

        // Update the dragged node position in canvas coordinates
        setNodes(prev => {
          const updatedNodes = prev.map(node => {
            if (node.id === draggedNode.id) {
              return {
                ...node,
                x: canvasX,
                y: canvasY,
                targetX: canvasX,
                targetY: canvasY
              };
            }

            // Update related nodes with proper canvas coordinate transformation
            if (nodeRelationships.get(draggedNode.id)?.includes(node.id)) {
              // Calculate distance between dragged node and related node in canvas space
              const dx = (node.x || 0) - canvasX;
              const dy = (node.y || 0) - canvasY;
              const distance = Math.sqrt(dx * dx + dy * dy);

              // Calculate pull strength based on distance
              // Closer nodes get pulled more strongly
              const pullStrength = Math.max(0.1, relatedNodePullStrength * (1 - Math.min(1, distance / 300)));

              // Move related node in canvas coordinates
              const newX = (node.x || 0) + deltaX * pullStrength;
              const newY = (node.y || 0) + deltaY * pullStrength;

              return {
                ...node,
                targetX: newX,
                targetY: newY
              };
            }

            return node;
          });

          return updatedNodes;
        });

        // Trigger force simulation
        SIMULATION_ACTIVE.current = true;
      }

      // Map dragging (panning) - optimized for smooth performance
      else if (isMapDragging && mapDragStartRef.current) {
        const deltaX = x - mapDragStartRef.current.x;
        const deltaY = y - mapDragStartRef.current.y;

        // Update pan offset ref immediately for smooth rendering
        panOffsetRef.current = {
          x: panOffsetRef.current.x + deltaX,
          y: panOffsetRef.current.y + deltaY
        };

        // Update state for React consistency (batched)
        setPanOffset(panOffsetRef.current);

        // Update map drag start position
        mapDragStartRef.current = { x, y };
      }
    };

    // Mouse up handler
    const handleMouseUp = (event: MouseEvent) => {
      if (isDragging && draggedNode) {
        // Trigger force simulation after drag
        SIMULATION_ACTIVE.current = true;

        // Check if it was a click (not much movement) using the threshold
        if (clickStartPositionRef.current) {
          const rect = canvas.getBoundingClientRect();
          const x = event.clientX - rect.left;
          const y = event.clientY - rect.top;

          const dx = x - clickStartPositionRef.current.x;
          const dy = y - clickStartPositionRef.current.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < dragThreshold) {
            // This was more like a click than a drag - use enhanced selection
            handleNodeSelection(draggedNode);
          }
        }
      }

      setIsDragging(false);
      setDraggedNode(null);
      setIsMapDragging(false);
      mapDragStartRef.current = null;
      canvas.style.cursor = 'default';
      previousPositionRef.current = null;
      clickStartPositionRef.current = null;
    };

    // Mouse out handler
    const handleMouseOut = () => {
      setIsDragging(false);
      setDraggedNode(null);
      setHoveredLink(null);
      setIsMapDragging(false);
      mapDragStartRef.current = null;
      canvas.style.cursor = 'default';
      previousPositionRef.current = null;
      clickStartPositionRef.current = null;
    };

    // Mouse wheel handler for zooming
    const handleWheel = (event: WheelEvent) => {
      event.preventDefault();

      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // Determine zoom direction
      const delta = event.deltaY < 0 ? zoomFactor : -zoomFactor;
      zoom(delta, { x, y });
    };

    // Enhanced cursor and hover handling
    const handleMouseOverNode = (event: MouseEvent) => {
      if (isMapDragging || isDragging) return;

      const rect = canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      const node = findNodeUnderCursor(x, y);
      const linkResult = !node ? findLinkUnderCursor(x, y) : null;

      // Update cursor based on what's under the mouse
      if (node) {
        canvas.style.cursor = 'grab';
        setHoveredLink(null);
      } else if (linkResult) {
        canvas.style.cursor = 'pointer';
        setHoveredLink(linkResult.link);
      } else {
        canvas.style.cursor = 'default';
        setHoveredLink(null);
      }

      // Handle hover effects
      handleNodeHover(node);
    };

    // Add event listeners
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseout', handleMouseOut);
    canvas.addEventListener('wheel', handleWheel, { passive: false });
    canvas.addEventListener('mousemove', handleMouseOverNode);

    // Cleanup
    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseout', handleMouseOut);
      canvas.removeEventListener('wheel', handleWheel);
      canvas.removeEventListener('mousemove', handleMouseOverNode);
    };
  }, [nodes, isDragging, isMapDragging, draggedNode, onNodeClick, onLinkClick, nodeRelationships, findNodeUnderCursor, findLinkUnderCursor, zoom, zoomLevel, handleNodeHover]);

  // Add touch support for mobile
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Find node under touch with canvas transformation support
    const findNodeUnderTouch = (x: number, y: number): Node | null => {
      // Transform touch coordinates to canvas coordinates using current pan offset
      const currentPanOffset = isMapDragging ? panOffsetRef.current : panOffset;
      const transformedX = (x - currentPanOffset.x) / zoomLevel;
      const transformedY = (y - currentPanOffset.y) / zoomLevel;

      for (const node of nodes) {
        if (node.x === undefined || node.y === undefined) continue;

        const dx = transformedX - node.x;
        const dy = transformedY - node.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // Use base node size without zoom scaling
        if (distance <= (node.size || 10)) {
          return node;
        }
      }
      return null;
    };

    // Touch start handler
    const handleTouchStart = (event: TouchEvent) => {
      if (event.touches.length !== 1) return;

      const touch = event.touches[0];
      const rect = canvas.getBoundingClientRect();
      const x = touch.clientX - rect.left;
      const y = touch.clientY - rect.top;

      const node = findNodeUnderTouch(x, y);
      if (node) {
        // Node dragging
        setDraggedNode(node);
        setIsDragging(true);
        previousPositionRef.current = { x, y };
        clickStartPositionRef.current = { x, y };

        // Tạo hiệu ứng phản hồi khi chạm vào node trên thiết bị di động
        const feedback = document.createElement('div');
        feedback.className = 'touch-feedback';
        feedback.style.left = `${x}px`;
        feedback.style.top = `${y}px`;
        canvas.parentElement?.appendChild(feedback);

        // Xóa hiệu ứng sau khi animation kết thúc
        setTimeout(() => {
          if (feedback.parentElement) {
            feedback.parentElement.removeChild(feedback);
          }
        }, 800);
      } else {
        // Map dragging (when touching empty space)
        setIsMapDragging(true);
        mapDragStartRef.current = { x, y };
      }
    };

    // Touch move handler
    const handleTouchMove = (event: TouchEvent) => {
      if (event.touches.length !== 1) return;

      const touch = event.touches[0];
      const rect = canvas.getBoundingClientRect();
      const x = touch.clientX - rect.left;
      const y = touch.clientY - rect.top;

      // Node dragging with proper coordinate transformation for touch
      if (isDragging && draggedNode) {
        // Transform touch coordinates to canvas coordinates using current pan offset
        const currentPanOffset = isMapDragging ? panOffsetRef.current : panOffset;
        const canvasX = (x - currentPanOffset.x) / zoomLevel;
        const canvasY = (y - currentPanOffset.y) / zoomLevel;

        // Calculate delta in canvas space if we have previous position
        let deltaX = 0, deltaY = 0;
        if (previousPositionRef.current) {
          const prevCanvasX = (previousPositionRef.current.x - currentPanOffset.x) / zoomLevel;
          const prevCanvasY = (previousPositionRef.current.y - currentPanOffset.y) / zoomLevel;
          deltaX = canvasX - prevCanvasX;
          deltaY = canvasY - prevCanvasY;
        }

        // Update previous position in screen coordinates
        previousPositionRef.current = { x, y };

        // Update the dragged node and related nodes in canvas coordinates
        setNodes(prev => {
          const updatedNodes = prev.map(node => {
            if (node.id === draggedNode.id) {
              return {
                ...node,
                x: canvasX,
                y: canvasY,
                targetX: canvasX,
                targetY: canvasY
              };
            }

            // Update related nodes with proper canvas coordinate transformation for touch
            if (nodeRelationships.get(draggedNode.id)?.includes(node.id)) {
              // Calculate distance between dragged node and related node in canvas space
              const dx = (node.x || 0) - canvasX;
              const dy = (node.y || 0) - canvasY;
              const distance = Math.sqrt(dx * dx + dy * dy);

              // Calculate pull strength based on distance
              const pullStrength = Math.max(0.1, relatedNodePullStrength * (1 - Math.min(1, distance / 300)));

              // Move related node in canvas coordinates
              const newX = (node.x || 0) + deltaX * pullStrength;
              const newY = (node.y || 0) + deltaY * pullStrength;

              return {
                ...node,
                targetX: newX,
                targetY: newY
              };
            }

            return node;
          });

          return updatedNodes;
        });

        // Trigger force simulation
        SIMULATION_ACTIVE.current = true;
      }

      // Map dragging (panning) - optimized for smooth touch performance
      else if (isMapDragging && mapDragStartRef.current) {
        const deltaX = x - mapDragStartRef.current.x;
        const deltaY = y - mapDragStartRef.current.y;

        // Update pan offset ref immediately for smooth rendering
        panOffsetRef.current = {
          x: panOffsetRef.current.x + deltaX,
          y: panOffsetRef.current.y + deltaY
        };

        // Update state for React consistency (batched)
        setPanOffset(panOffsetRef.current);

        // Update map drag start position
        mapDragStartRef.current = { x, y };
      }

      // Prevent scrolling when interacting with the map
      event.preventDefault();
    };

    // Touch end handler
    const handleTouchEnd = (event: TouchEvent) => {
      if (isDragging && draggedNode) {
        // Trigger force simulation after drag
        SIMULATION_ACTIVE.current = true;

        // Check if it was a tap (not much movement) using the threshold
        if (clickStartPositionRef.current && event.changedTouches.length > 0) {
          const touch = event.changedTouches[0];
          const rect = canvas.getBoundingClientRect();
          const x = touch.clientX - rect.left;
          const y = touch.clientY - rect.top;

          const dx = x - clickStartPositionRef.current.x;
          const dy = y - clickStartPositionRef.current.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < dragThreshold) {
            // This was more like a tap than a drag - use enhanced selection
            handleNodeSelection(draggedNode);
          }
        }
      }

      setIsDragging(false);
      setDraggedNode(null);
      setIsMapDragging(false);
      mapDragStartRef.current = null;
      previousPositionRef.current = null;
      clickStartPositionRef.current = null;
    };

    // Add pinch-to-zoom for mobile
    let initialDistance = 0;
    let initialZoom = zoomLevel;

    const handleTouchZoomStart = (event: TouchEvent) => {
      if (event.touches.length !== 2) return;

      // Calculate initial distance between two fingers
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      initialDistance = Math.hypot(
        touch1.clientX - touch2.clientX,
        touch1.clientY - touch2.clientY
      );
      initialZoom = zoomLevel;

      // Store the midpoint as the zoom center
      const rect = canvas.getBoundingClientRect();
      const zoomCenterX = (touch1.clientX + touch2.clientX) / 2 - rect.left;
      const zoomCenterY = (touch1.clientY + touch2.clientY) / 2 - rect.top;
      zoomPointRef.current = { x: zoomCenterX, y: zoomCenterY };
    };

    const handleTouchZoomMove = (event: TouchEvent) => {
      if (event.touches.length !== 2 || !zoomPointRef.current) return;

      // Calculate new distance between two fingers
      const touch1 = event.touches[0];
      const touch2 = event.touches[1];
      const currentDistance = Math.hypot(
        touch1.clientX - touch2.clientX,
        touch1.clientY - touch2.clientY
      );

      // Calculate zoom change
      const zoomChange = currentDistance / initialDistance;
      const newZoom = Math.max(minZoom, Math.min(maxZoom, initialZoom * zoomChange));

      // Update zoom level
      if (newZoom !== zoomLevel) {
        const delta = newZoom - zoomLevel;
        zoom(delta, zoomPointRef.current);
      }

      event.preventDefault();
    };

    // Add touch event listeners
    canvas.addEventListener('touchstart', handleTouchStart);
    canvas.addEventListener('touchmove', handleTouchMove, { passive: false });
    canvas.addEventListener('touchend', handleTouchEnd);
    canvas.addEventListener('touchstart', handleTouchZoomStart);
    canvas.addEventListener('touchmove', handleTouchZoomMove, { passive: false });

    // Cleanup
    return () => {
      canvas.removeEventListener('touchstart', handleTouchStart);
      canvas.removeEventListener('touchmove', handleTouchMove);
      canvas.removeEventListener('touchend', handleTouchEnd);
      canvas.removeEventListener('touchstart', handleTouchZoomStart);
      canvas.removeEventListener('touchmove', handleTouchZoomMove);
    };
  }, [nodes, isDragging, isMapDragging, draggedNode, onNodeClick, nodeRelationships, zoomLevel, zoom, handleNodeSelection]);

  // Run initial force simulation to spread out nodes
  useEffect(() => {
    if (nodes.length > 0) {
      SIMULATION_ACTIVE.current = true;
    }
  }, [nodes.length]);

  // Sync panOffsetRef with panOffset state
  useEffect(() => {
    panOffsetRef.current = panOffset;
  }, [panOffset]);

  return (
    <canvas
      ref={canvasRef}
      className="w-full h-full"
      style={{
        touchAction: 'none',
        backgroundColor: 'transparent', // Ensure canvas background is transparent
        backgroundImage: 'none' // Remove any default background images
      }}
    />
  );
});

// Đặt displayName cho component để dễ debug
BubbleMap.displayName = 'BubbleMap';

export default BubbleMap;