import React from 'react';
import { FaWallet, FaExchangeAlt, FaEthereum, FaShieldAlt, FaExclamationTriangle, FaNetworkWired } from 'react-icons/fa';
import { EnhancedNode } from './BubbleMap';

interface ProfessionalTooltipProps {
  node: EnhancedNode;
  x: number;
  y: number;
  visible: boolean;
}

const ProfessionalTooltip: React.FC<ProfessionalTooltipProps> = ({ node, x, y, visible }) => {
  if (!visible) return null;

  // Determine node type icon
  const getNodeIcon = () => {
    if (node.tags?.includes('exchange') || node.tags?.includes('Exchange')) {
      return <FaExchangeAlt className="text-secondary-400" />;
    }
    if (node.tags?.includes('contract') || node.tags?.includes('Contract')) {
      return <FaNetworkWired className="text-accent-400" />;
    }
    if (node.tags?.includes('flagged') || node.tags?.includes('suspicious')) {
      return <FaExclamationTriangle className="text-red-400" />;
    }
    return <FaWallet className="text-primary-400" />;
  };

  // Get risk level styling
  const getRiskStyling = () => {
    switch (node.riskLevel) {
      case 'high':
        return 'border-red-500/50 bg-red-500/10';
      case 'medium':
        return 'border-orange-500/50 bg-orange-500/10';
      default:
        return 'border-secondary-500/50 bg-secondary-500/10';
    }
  };

  // Format balance
  const formatBalance = (balance?: string) => {
    if (!balance) return 'N/A';
    const num = parseFloat(balance);
    if (num > 1000) return `${(num / 1000).toFixed(1)}K ETH`;
    if (num > 1) return `${num.toFixed(2)} ETH`;
    return `${num.toFixed(4)} ETH`;
  };

  // Format transaction count
  const formatTxCount = (count?: number) => {
    if (!count) return 'N/A';
    if (count > 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count > 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };

  // Truncate address
  const truncateAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <div
      className="fixed z-50 pointer-events-none animate-fade-in"
      style={{
        left: `${x}px`,
        top: `${y}px`,
        transform: 'translate(-50%, -120%)',
      }}
    >
      <div className={`glass-card p-4 rounded-xl shadow-bubble border-2 ${getRiskStyling()} max-w-xs`}>
        {/* Header */}
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 bg-gradient-brand rounded-lg flex items-center justify-center">
            {getNodeIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-bold text-foreground text-sm truncate">
              {node.label || 'Unknown Wallet'}
            </h3>
            <p className="text-xs text-foreground-muted font-mono">
              {truncateAddress(node.address)}
            </p>
          </div>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-2 gap-3 mb-3">
          <div className="bg-background-secondary rounded-lg p-2">
            <div className="flex items-center gap-1 mb-1">
              <FaEthereum className="text-primary-400 text-xs" />
              <span className="text-xs text-foreground-muted">Balance</span>
            </div>
            <p className="text-sm font-bold text-foreground">
              {formatBalance(node.balance)}
            </p>
          </div>

          <div className="bg-background-secondary rounded-lg p-2">
            <div className="flex items-center gap-1 mb-1">
              <FaExchangeAlt className="text-secondary-400 text-xs" />
              <span className="text-xs text-foreground-muted">Txns</span>
            </div>
            <p className="text-sm font-bold text-foreground">
              {formatTxCount(node.transactionCount)}
            </p>
          </div>
        </div>

        {/* Risk Assessment */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <FaShieldAlt className="text-accent-400 text-xs" />
            <span className="text-xs text-foreground-muted">Risk Level</span>
          </div>
          <span className={`text-xs px-2 py-1 rounded-full font-medium ${
            node.riskLevel === 'high'
              ? 'bg-red-500/20 text-red-400'
              : node.riskLevel === 'medium'
              ? 'bg-orange-500/20 text-orange-400'
              : 'bg-secondary-500/20 text-secondary-400'
          }`}>
            {node.riskLevel?.toUpperCase() || 'LOW'}
          </span>
        </div>

        {/* Tags */}
        {node.tags && node.tags.length > 0 && (
          <div className="mb-3">
            <p className="text-xs text-foreground-muted mb-2">Tags</p>
            <div className="flex flex-wrap gap-1">
              {node.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="text-xs px-2 py-1 bg-accent-500/20 text-accent-400 rounded-full"
                >
                  {tag}
                </span>
              ))}
              {node.tags.length > 3 && (
                <span className="text-xs px-2 py-1 bg-foreground-muted/20 text-foreground-muted rounded-full">
                  +{node.tags.length - 3}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Activity Indicator */}
        {node.isActive && (
          <div className="flex items-center gap-2 pt-2 border-t border-border">
            <div className="w-2 h-2 bg-secondary-400 rounded-full animate-pulse"></div>
            <span className="text-xs text-secondary-400 font-medium">Active</span>
          </div>
        )}

        {/* Tooltip Arrow */}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2">
          <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-border-accent"></div>
        </div>
      </div>
    </div>
  );
};

export default ProfessionalTooltip;
