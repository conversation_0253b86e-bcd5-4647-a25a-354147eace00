import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Node } from '@/services/neo4jService';

// Dynamically import LeftSidebar with no SSR
const LeftSidebar = dynamic(() => import('./LeftSidebar'), {
  ssr: false,
  loading: () => null
});

interface RiskFilterSettings {
  minRiskLevel: 'low' | 'medium' | 'high' | 'critical';
  showOnlyFlagged: boolean;
  hideWhitelisted: boolean;
  highlightSuspicious: boolean;
  autoHideClean: boolean;
  riskThreshold: number;
  filterTypes: {
    phishing: boolean;
    mev: boolean;
    laundering: boolean;
    sanctions: boolean;
    scam: boolean;
    suspicious: boolean;
  };
}

export interface ClientOnlyLeftSidebarProps {
  selectedWallet: Node | null;
  showTransactionFlow: boolean;
  showSecurityAlerts: boolean;
  showRiskFilter: boolean;
  showWatchList: boolean;
  showAskAI: boolean;
  riskFilterSettings: RiskFilterSettings;
  onAlertSelect?: (alert: any) => void;
  onTransactionSelect?: (transaction: any) => void;
  onRiskFilterChange?: (settings: RiskFilterSettings) => void;
  onApplyRiskFilter?: (settings: RiskFilterSettings) => void;
  onResetRiskFilter?: () => void;
  onWalletSelect?: (wallet: Node) => void;
  onAddToWatchList?: (address: string) => void;
  onFocusWallet?: (address: string) => void;
  totalWallets?: number;
  filteredWallets?: number;
  flaggedWallets?: number;
}

const ClientOnlyLeftSidebar: React.FC<ClientOnlyLeftSidebarProps> = (props) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render anything during SSR or before hydration
  if (!isMounted) {
    return null;
  }

  return <LeftSidebar {...props} />;
};

export default ClientOnlyLeftSidebar;
