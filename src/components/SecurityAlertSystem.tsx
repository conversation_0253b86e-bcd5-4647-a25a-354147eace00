import React, { useState, useEffect, useRef } from 'react';
import {
  FaExclamation<PERSON>riangle, FaShieldAlt, FaBan, FaRobot,
  FaFish, FaMoneyBillWave, FaBell, FaEye, FaClock,
  FaCheckCircle, FaTimes, FaFilter, FaDownload, FaArrowUp,
  FaWallet, FaExchangeAlt
} from 'react-icons/fa';
import Tooltip from './Tooltip';

interface SecurityAlert {
  id: string;
  type: 'phishing' | 'mev' | 'laundering' | 'sanctions' | 'scam' | 'suspicious';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  walletAddress: string;
  timestamp: string;
  status: 'active' | 'resolved' | 'investigating';
  confidence: number;
  relatedTransactions: string[];
  actionRequired: boolean;
}

interface SecurityAlertSystemProps {
  onAlertSelect?: (alert: SecurityAlert) => void;
}

const SecurityAlertSystem: React.FC<SecurityAlertSystemProps> = ({ onAlertSelect }) => {
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<'all' | 'critical' | 'active' | 'resolved'>('all');
  const [selectedAlert, setSelectedAlert] = useState<SecurityAlert | null>(null);
  const [realTimeEnabled, setRealTimeEnabled] = useState(true);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const alertListRef = useRef<HTMLDivElement>(null);
  const modalContentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchAlerts();
  }, [filter]);

  // Handle scroll indicators for modal content
  useEffect(() => {
    const handleModalScroll = () => {
      if (modalContentRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = modalContentRef.current;
        const hasScrollTop = scrollTop > 10;
        const hasScrollBottom = scrollTop < scrollHeight - clientHeight - 10;

        modalContentRef.current.classList.toggle('has-scroll-top', hasScrollTop);
        modalContentRef.current.classList.toggle('has-scroll-bottom', hasScrollBottom);
      }
    };

    const modalContent = modalContentRef.current;
    if (modalContent) {
      modalContent.addEventListener('scroll', handleModalScroll);
      // Initial check
      handleModalScroll();

      return () => modalContent.removeEventListener('scroll', handleModalScroll);
    }
  }, [selectedAlert]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (realTimeEnabled) {
      interval = setInterval(fetchAlerts, 10000); // Check every 10 seconds
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [realTimeEnabled]);

  // Enhanced scroll event handler for scroll-to-top button with throttling
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          if (alertListRef.current) {
            const scrollTop = alertListRef.current.scrollTop;
            const shouldShow = scrollTop > 200;

            // Only update state if it actually changed
            setShowScrollToTop(prev => {
              if (prev !== shouldShow) {
                return shouldShow;
              }
              return prev;
            });

            // Debug logging for development
            if (process.env.NODE_ENV === 'development') {
              console.log('SecurityAlerts scroll:', { scrollTop, shouldShow });
            }
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    const alertList = alertListRef.current;
    if (alertList) {
      // Add scroll event listener with passive option for better performance
      alertList.addEventListener('scroll', handleScroll, { passive: true });
      // Initial check in case content is already scrolled
      handleScroll();

      return () => {
        alertList.removeEventListener('scroll', handleScroll);
      };
    }
  }, [alerts]); // Re-run when alerts change to ensure proper setup

  // Scroll to top when new alerts are added
  useEffect(() => {
    if (alertListRef.current && alerts.length > 0) {
      // Only scroll to top if we're not already at the top
      if (alertListRef.current.scrollTop > 100) {
        alertListRef.current.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    }
  }, [alerts.length]);

  const fetchAlerts = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));

      // Generate comprehensive mock alerts
      const mockAlerts: SecurityAlert[] = [
        {
          id: 'alert_1',
          type: 'phishing',
          severity: 'critical',
          title: 'Phishing Attack Detected',
          description: 'Wallet interacted with known phishing contract',
          walletAddress: '******************************************',
          timestamp: new Date().toISOString(),
          status: 'active',
          confidence: 95,
          relatedTransactions: ['0xabc123...', '0xdef456...'],
          actionRequired: true
        },
        {
          id: 'alert_2',
          type: 'mev',
          severity: 'high',
          title: 'MEV Bot Activity',
          description: 'Suspicious MEV manipulation detected',
          walletAddress: '0x8ba1f109551bD432803012645Hac136c22C501e',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          status: 'investigating',
          confidence: 87,
          relatedTransactions: ['0x123abc...'],
          actionRequired: false
        },
        {
          id: 'alert_3',
          type: 'laundering',
          severity: 'high',
          title: 'Money Laundering Pattern',
          description: 'Complex transaction pattern suggesting money laundering',
          walletAddress: '******************************************',
          timestamp: new Date(Date.now() - 600000).toISOString(),
          status: 'active',
          confidence: 78,
          relatedTransactions: ['0x789xyz...', '0x456def...'],
          actionRequired: true
        },
        {
          id: 'alert_4',
          type: 'sanctions',
          severity: 'critical',
          title: 'Sanctioned Address Interaction',
          description: 'Transaction with OFAC sanctioned address',
          walletAddress: '******************************************',
          timestamp: new Date(Date.now() - 900000).toISOString(),
          status: 'active',
          confidence: 100,
          relatedTransactions: ['0xsanc123...'],
          actionRequired: true
        },
        {
          id: 'alert_5',
          type: 'scam',
          severity: 'medium',
          title: 'Potential Scam Activity',
          description: 'Wallet shows patterns consistent with scam operations',
          walletAddress: '******************************************',
          timestamp: new Date(Date.now() - 1200000).toISOString(),
          status: 'resolved',
          confidence: 65,
          relatedTransactions: ['0xscam789...'],
          actionRequired: false
        }
      ];

      setAlerts(mockAlerts);
    } catch (error) {
      console.error('Error fetching alerts:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredAlerts = alerts.filter(alert => {
    if (filter === 'all') return true;
    if (filter === 'critical') return alert.severity === 'critical';
    if (filter === 'active') return alert.status === 'active';
    if (filter === 'resolved') return alert.status === 'resolved';
    return true;
  });

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'high': return 'text-orange-400 bg-orange-500/20 border-orange-500/30';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      default: return 'text-blue-400 bg-blue-500/20 border-blue-500/30';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'phishing': return <FaFish className="text-red-400" />;
      case 'mev': return <FaRobot className="text-orange-400" />;
      case 'laundering': return <FaMoneyBillWave className="text-yellow-400" />;
      case 'sanctions': return <FaBan className="text-red-500" />;
      case 'scam': return <FaExclamationTriangle className="text-red-400" />;
      default: return <FaShieldAlt className="text-blue-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-red-400 bg-red-500/20';
      case 'investigating': return 'text-yellow-400 bg-yellow-500/20';
      case 'resolved': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const formatTime = (timestamp: string) => {
    const now = new Date();
    const alertTime = new Date(timestamp);
    const diffMs = now.getTime() - alertTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  };

  const resolveAlert = (alertId: string) => {
    setAlerts(prev => prev.map(alert =>
      alert.id === alertId
        ? { ...alert, status: 'resolved' as const }
        : alert
    ));
  };

  const exportAlerts = () => {
    const csvContent = [
      ['ID', 'Type', 'Severity', 'Title', 'Description', 'Wallet', 'Status', 'Confidence', 'Timestamp'],
      ...filteredAlerts.map(alert => [
        alert.id,
        alert.type,
        alert.severity,
        alert.title,
        alert.description,
        alert.walletAddress,
        alert.status,
        alert.confidence.toString(),
        alert.timestamp
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `security-alerts-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="relative flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border-accent bg-background-secondary/30">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r from-red-500 to-orange-500">
            <FaShieldAlt className="text-sm text-white" />
          </div>
          <div>
            <h3 className="font-bold text-foreground">Security Alerts</h3>
            <p className="text-xs text-foreground-muted">
              Real-time threat detection
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Tooltip
            content={realTimeEnabled ? "Disable real-time alerts" : "Enable real-time alerts"}
            contentVi={realTimeEnabled ? "Tắt cảnh báo thời gian thực" : "Bật cảnh báo thời gian thực"}
            showShortcut="Alt+N"
          >
            <button
              onClick={() => setRealTimeEnabled(!realTimeEnabled)}
              className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                realTimeEnabled
                  ? 'bg-green-500/20 text-green-400 shadow-green-500/20'
                  : 'bg-background-tertiary text-foreground-muted hover:text-foreground'
              }`}
            >
              <FaBell size={14} />
            </button>
          </Tooltip>
          <Tooltip
            content="Export security alerts to report file"
            contentVi="Xuất cảnh báo bảo mật ra file báo cáo"
            showShortcut="Ctrl+Shift+E"
          >
            <button
              onClick={exportAlerts}
              className="p-2 transition-all duration-200 rounded-lg bg-background-tertiary hover:bg-background-secondary hover:scale-110"
            >
              <FaDownload size={14} />
            </button>
          </Tooltip>
        </div>
      </div>

      {/* Alert Summary - Compact */}
      <div className="p-4">
        <div className="grid grid-cols-2 gap-2 mb-4">
          <div className="p-2 text-center rounded-lg glass-card">
            <div className="text-sm font-bold text-red-400">
              {alerts.filter(a => a.severity === 'critical').length}
            </div>
            <div className="text-xs text-foreground-muted">Critical</div>
          </div>
          <div className="p-2 text-center rounded-lg glass-card">
            <div className="text-sm font-bold text-orange-400">
              {alerts.filter(a => a.severity === 'high').length}
            </div>
            <div className="text-xs text-foreground-muted">High</div>
          </div>
          <div className="p-2 text-center rounded-lg glass-card">
            <div className="text-sm font-bold text-green-400">
              {alerts.filter(a => a.status === 'active').length}
            </div>
            <div className="text-xs text-foreground-muted">Active</div>
          </div>
          <div className="p-2 text-center rounded-lg glass-card">
            <div className="text-sm font-bold text-blue-400">
              {alerts.filter(a => a.actionRequired).length}
            </div>
            <div className="text-xs text-foreground-muted">Action</div>
          </div>
        </div>

        {/* Filters - Compact */}
        <div className="flex p-1 mb-4 rounded-lg bg-background-tertiary">
          {['all', 'critical', 'active', 'resolved'].map(filterType => (
            <button
              key={filterType}
              onClick={() => setFilter(filterType as any)}
              className={`flex-1 px-2 py-1 text-xs font-medium rounded-md transition-colors ${
                filter === filterType
                  ? 'bg-accent-500 text-white'
                  : 'text-foreground-muted hover:text-foreground'
              }`}
            >
              {filterType.charAt(0).toUpperCase() + filterType.slice(1, 4)}
            </button>
          ))}
        </div>
      </div>

      {/* Alert List - Enhanced Scrollable for LeftSidebar with scroll-to-top positioning context */}
      <div className="relative flex-1 px-4 overflow-y-auto sidebar-panel-container security-alerts-list scrollable-container"
           ref={alertListRef}
           style={{
             scrollBehavior: 'smooth',
             WebkitOverflowScrolling: 'touch',
             overscrollBehavior: 'contain',
             position: 'relative', // Positioning context for scroll-to-top button
             isolation: 'isolate' // Create new stacking context
           }}>
        {/* Scroll Indicator */}
        <div className="scroll-indicator" style={{ top: '1rem', bottom: '1rem' }} />

        <div className="pb-4 space-y-2">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="w-6 h-6 border-2 rounded-full animate-spin border-accent-400 border-t-transparent"></div>
            </div>
          ) : (
            filteredAlerts.map(alert => (
              <div
                key={alert.id}
                onClick={() => {
                  setSelectedAlert(alert);
                  onAlertSelect?.(alert);
                }}
                className={`glass-card p-3 rounded-lg cursor-pointer transition-all hover:bg-background-tertiary/50 border security-alert-item ${getSeverityColor(alert.severity)}`}
                style={{
                  scrollMarginTop: '0.5rem',
                  scrollMarginBottom: '0.5rem'
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-background-tertiary rounded-full flex items-center justify-center mt-0.5">
                      {getTypeIcon(alert.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-sm font-semibold text-foreground">{alert.title}</h4>
                        {alert.actionRequired && (
                          <span className="px-2 py-0.5 bg-red-500/20 text-red-400 rounded-full text-xs">
                            ACTION REQUIRED
                          </span>
                        )}
                      </div>
                      <p className="mb-2 text-xs text-foreground-muted line-clamp-2">{alert.description}</p>
                      <div className="flex flex-wrap items-center gap-2 text-xs">
                        <span className="text-foreground-muted truncate max-w-[120px]">
                          {alert.walletAddress.slice(0, 10)}...{alert.walletAddress.slice(-8)}
                        </span>
                        <span className={`px-2 py-0.5 rounded-full whitespace-nowrap ${getStatusColor(alert.status)}`}>
                          {alert.status.toUpperCase()}
                        </span>
                        <span className="text-foreground-muted whitespace-nowrap">
                          {alert.confidence}%
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="mb-2 text-xs text-foreground-muted">
                      {formatTime(alert.timestamp)}
                    </div>
                    {alert.status === 'active' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          resolveAlert(alert.id);
                        }}
                        className="p-1 text-green-400 transition-colors rounded bg-green-500/20 hover:bg-green-500/30"
                      >
                        <FaCheckCircle size={12} />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

      </div>

      {/* Fixed Scroll to Top Button - positioned absolute relative to component container */}
      <button
        onClick={() => {
          alertListRef.current?.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        }}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            alertListRef.current?.scrollTo({
              top: 0,
              behavior: 'smooth'
            });
          }
        }}
        className={`absolute bottom-4 right-4 z-50 w-11 h-11 bg-gradient-to-br from-accent-500 to-accent-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center ${showScrollToTop ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'}`}
        style={{
          backdropFilter: 'blur(12px)',
          border: '1px solid rgba(139, 92, 246, 0.3)'
        }}
        aria-label="Scroll to top of security alerts"
        aria-hidden={!showScrollToTop}
        tabIndex={showScrollToTop ? 0 : -1}
        title="Scroll to top (Home key also works)"
      >
        <FaArrowUp size={16} />
      </button>

      {/* Alert Details Modal */}
      {selectedAlert && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[9100] p-4">
          <div className="glass-card rounded-xl p-4 sm:p-6 max-w-sm sm:max-w-lg w-full max-h-[90vh] flex flex-col"
               style={{
                 minHeight: '300px'
               }}>
            {/* Fixed Header */}
            <div className="flex items-center justify-between flex-shrink-0 mb-4">
              <h3 className="font-bold text-foreground">Alert Details</h3>
              <button
                onClick={() => setSelectedAlert(null)}
                className="p-1 rounded hover:bg-background-tertiary"
              >
                <FaTimes />
              </button>
            </div>

            {/* Scrollable Content Area */}
            <div className="flex-1 overflow-hidden">
              <div className="h-full modal-content-scrollable" ref={modalContentRef} tabIndex={0} role="region" aria-label="Alert details content">
                <div className="p-2 space-y-4">
                  {/* Alert Summary */}
                  <div className={`p-3 rounded-lg border ${getSeverityColor(selectedAlert.severity)}`}>
                    <div className="flex items-center gap-2 mb-2">
                      {getTypeIcon(selectedAlert.type)}
                      <span className="font-semibold">{selectedAlert.title}</span>
                    </div>
                    <div className="modal-section-scrollable" tabIndex={0} role="region" aria-label="Alert description">
                      <p className="text-sm leading-relaxed text-foreground-muted">
                        {selectedAlert.description}
                      </p>
                    </div>
                  </div>

                  {/* Alert Metadata */}
                  <div className="grid grid-cols-1 gap-4 text-sm xs:grid-cols-2">
                    <div>
                      <span className="text-foreground-muted">Severity:</span>
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getSeverityColor(selectedAlert.severity)}`}>
                        {selectedAlert.severity.toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <span className="text-foreground-muted">Status:</span>
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(selectedAlert.status)}`}>
                        {selectedAlert.status.toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <span className="text-foreground-muted">Confidence:</span>
                      <span className="ml-2 font-semibold text-foreground">{selectedAlert.confidence}%</span>
                    </div>
                    <div>
                      <span className="text-foreground-muted">Time:</span>
                      <span className="ml-2 text-foreground">{formatTime(selectedAlert.timestamp)}</span>
                    </div>
                  </div>

                  {/* Wallet Address */}
                  <div>
                    <h4 className="flex items-center gap-2 mb-2 text-sm font-semibold">
                      <FaWallet className="text-accent-400" size={12} />
                      Wallet Address
                    </h4>
                    <div className="p-2 font-mono text-xs break-all rounded bg-background-tertiary">
                      {selectedAlert.walletAddress}
                    </div>
                  </div>

                  {/* Related Transactions - Enhanced Scrollable Section */}
                  <div>
                    <h4 className="flex items-center gap-2 mb-2 text-sm font-semibold">
                      <FaExchangeAlt className="text-secondary-400" size={12} />
                      Related Transactions ({selectedAlert.relatedTransactions.length})
                    </h4>
                    <div className="modal-section-scrollable" tabIndex={0} role="region" aria-label="Related transactions list">
                      <div className="space-y-2">
                        {selectedAlert.relatedTransactions.map((tx, index) => (
                          <div key={tx} className="p-3 transition-colors border rounded-lg bg-background-tertiary border-border/50 hover:border-accent-400/30">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-xs text-foreground-muted">Transaction #{index + 1}</span>
                              <button className="text-xs text-accent-400 hover:text-accent-300">
                                View Details
                              </button>
                            </div>
                            <div className="font-mono text-xs break-all text-foreground">
                              {tx}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Additional Alert Information */}
                  {selectedAlert.actionRequired && (
                    <div className="p-3 border rounded-lg bg-red-500/10 border-red-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <FaExclamationTriangle className="text-red-400" size={14} />
                        <span className="font-semibold text-red-400">Action Required</span>
                      </div>
                      <p className="text-sm text-foreground-muted">
                        This alert requires immediate attention. Please investigate and take appropriate action.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Fixed Footer */}
            <div className="flex flex-shrink-0 gap-2 pt-4 mt-4 border-t border-border">
              <button className="flex-1 py-2 text-xs btn-primary">
                Investigate
              </button>
              <button
                onClick={() => {
                  resolveAlert(selectedAlert.id);
                  setSelectedAlert(null);
                }}
                className="flex-1 py-2 text-xs btn-secondary"
              >
                Mark Resolved
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SecurityAlertSystem;
