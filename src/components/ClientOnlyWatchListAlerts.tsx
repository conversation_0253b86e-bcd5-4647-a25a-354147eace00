import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import WatchListAlerts with no SSR
const WatchListAlerts = dynamic(() => import('./WatchListAlerts'), {
  ssr: false,
  loading: () => (
    <div className="p-4 bg-slate-700/30 border border-slate-600/50 rounded-lg">
      <div className="flex items-center gap-2 mb-2">
        <div className="w-4 h-4 border-2 border-purple-400 rounded-full animate-spin border-t-transparent"></div>
        <span className="text-sm font-medium text-slate-300">Loading Alerts...</span>
      </div>
    </div>
  )
});

interface ClientOnlyWatchListAlertsProps {
  className?: string;
  maxAlerts?: number;
  showOnlyUnacknowledged?: boolean;
}

const ClientOnlyWatchListAlerts: React.FC<ClientOnlyWatchListAlertsProps> = (props) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render anything during SSR or before hydration
  if (!isMounted) {
    return (
      <div className="p-4 bg-slate-700/30 border border-slate-600/50 rounded-lg">
        <div className="flex items-center gap-2 mb-2">
          <div className="w-4 h-4 border-2 border-purple-400 rounded-full animate-spin border-t-transparent"></div>
          <span className="text-sm font-medium text-slate-300">Initializing Alerts...</span>
        </div>
      </div>
    );
  }

  return <WatchListAlerts {...props} />;
};

export default ClientOnlyWatchListAlerts;
