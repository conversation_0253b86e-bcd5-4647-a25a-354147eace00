import { useState, FormEvent, useRef, useEffect } from 'react';
import { FaSearch, FaHistory, FaTimes, FaWallet, FaEthereum } from 'react-icons/fa';

interface SearchBarProps {
  onSearch: (address: string) => void;
  compact?: boolean; // For fullscreen mode
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearch, compact = false }) => {
  const [searchInput, setSearchInput] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Sample popular addresses for suggestions
  const popularAddresses = [
    { address: '******************************************', label: 'Uniswap Token', type: 'Token' },
    { address: '******************************************', label: 'Compound', type: 'DeFi' },
    { address: '******************************************', label: 'Vitalik.eth', type: 'ENS' },
  ];

  useEffect(() => {
    // Load search history from localStorage
    const history = localStorage.getItem('searchHistory');
    if (history) {
      setSearchHistory(JSON.parse(history));
    }
  }, []);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (searchInput.trim()) {
      const address = searchInput.trim();
      onSearch(address);

      // Add to search history
      const newHistory = [address, ...searchHistory.filter(h => h !== address)].slice(0, 5);
      setSearchHistory(newHistory);
      localStorage.setItem('searchHistory', JSON.stringify(newHistory));

      setShowSuggestions(false);
      inputRef.current?.blur();
    }
  };

  const handleSuggestionClick = (address: string) => {
    setSearchInput(address);
    onSearch(address);
    setShowSuggestions(false);
    inputRef.current?.blur();
  };

  const clearSearch = () => {
    setSearchInput('');
    inputRef.current?.focus();
  };

  const clearHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem('searchHistory');
  };

  return (
    <div className="relative w-full">
      <form onSubmit={handleSubmit} className="relative">
        <div className={`relative flex items-center transition-all duration-300 ${isSearchFocused ? 'transform scale-[1.02]' : ''}`}>
          <div className="absolute inset-0 transition-opacity opacity-0 bg-gradient-brand rounded-xl blur-sm group-hover:opacity-20"></div>

          {/* Search Icon */}
          <FaSearch className={`absolute z-10 text-accent-400 ${
            compact ? 'left-3 text-sm' : 'left-4 text-base'
          }`} />

          {/* Input Field */}
          <input
            ref={inputRef}
            type="text"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onFocus={() => {
              setIsSearchFocused(true);
              setShowSuggestions(true);
            }}
            onBlur={() => {
              setIsSearchFocused(false);
              // Delay hiding suggestions to allow clicks
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            placeholder={compact ? "Search address..." : "Search tokens, addresses, or ENS names..."}
            className={`flex-1 glass-card rounded-xl focus-ring text-foreground placeholder-foreground-muted font-medium transition-all duration-300 ${
              compact
                ? 'py-2.5 pl-10 pr-3 text-sm'
                : 'py-3 pl-12 pr-4 text-base'
            }`}
          />

          {/* Clear Button */}
          {searchInput && (
            <button
              type="button"
              onClick={clearSearch}
              className={`absolute z-10 top-1/2 transform -translate-y-1/2 text-foreground-muted hover:text-foreground p-1 rounded-full hover:bg-background-tertiary transition-colors ${
                compact ? 'right-12' : 'right-16'
              }`}
            >
              <FaTimes size={compact ? 12 : 14} />
            </button>
          )}

          {/* Go/Search Button */}
          <button
            type="submit"
            disabled={!searchInput.trim()}
            className={`absolute z-10 top-1/2 transform -translate-y-1/2
              overflow-hidden group
              bg-gradient-to-r from-primary-600 via-accent-600 to-secondary-600
              hover:from-primary-500 hover:via-accent-500 hover:to-secondary-500
              active:from-primary-700 active:via-accent-700 active:to-secondary-700
              text-white font-semibold
              border border-primary-500/30 hover:border-accent-400/50
              backdrop-blur-sm
              shadow-lg hover:shadow-glow
              transition-all duration-300 ease-out
              disabled:opacity-40 disabled:cursor-not-allowed disabled:hover:shadow-lg
              disabled:hover:from-primary-600 disabled:hover:via-accent-600 disabled:hover:to-secondary-600
              ${compact
                ? 'right-1 px-2.5 py-1.5 text-xs rounded-md'
                : 'right-1.5 px-3.5 py-2 text-sm rounded-lg'
              }
            `}
          >
            {/* Glass morphism overlay */}
            <div className="absolute inset-0 transition-opacity duration-300 opacity-0 bg-gradient-to-r from-white/10 via-white/5 to-transparent group-hover:opacity-100"></div>

            {/* Animated background glow */}
            <div className="absolute inset-0 transition-all duration-300 opacity-0 bg-gradient-brand group-hover:opacity-20 blur-sm group-active:opacity-30"></div>

            {/* Button content */}
            <span className="relative z-10 flex items-center justify-center">
              {compact ? (
                <span className="text-xs font-semibold">Go</span>
              ) : (
                <>
                  <FaSearch className="text-xs mr-1.5 transition-transform duration-200 group-hover:scale-110" />
                  <span className="text-sm font-semibold">Go</span>
                </>
              )}
            </span>

            {/* Ripple effect on click */}
            <div className="absolute inset-0 transition-all duration-150 scale-0 rounded-full opacity-0 group-active:opacity-100 bg-white/20 group-active:scale-100"></div>
          </button>
        </div>
      </form>

      {/* Enhanced suggestions dropdown */}
      {showSuggestions && (
        <div className="absolute left-0 right-0 z-50 mt-2 border top-full glass-card rounded-xl border-border-accent shadow-glow animate-slide-up">
          <div className="p-4">
            {/* Search History */}
            {searchHistory.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="flex items-center gap-2 text-sm font-medium text-foreground-secondary">
                    <FaHistory className="text-accent-400" />
                    Recent Searches
                  </h4>
                  <button
                    onClick={clearHistory}
                    className="text-xs transition-colors text-foreground-muted hover:text-foreground"
                  >
                    Clear
                  </button>
                </div>
                <div className="space-y-1">
                  {searchHistory.map((address, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(address)}
                      className="w-full px-3 py-2 font-mono text-sm text-left transition-colors rounded-lg hover:bg-background-tertiary text-foreground-muted hover:text-foreground"
                    >
                      {address.length > 42 ? `${address.slice(0, 20)}...${address.slice(-10)}` : address}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Popular Addresses */}
            <div>
              <h4 className="flex items-center gap-2 mb-2 text-sm font-medium text-foreground-secondary">
                <FaWallet className="text-accent-400" />
                Popular Addresses
              </h4>
              <div className="space-y-1">
                {popularAddresses.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(item.address)}
                    className="w-full px-3 py-3 text-left transition-colors rounded-lg hover:bg-background-tertiary group"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-brand">
                          <FaEthereum className="text-sm text-white" />
                        </div>
                        <div>
                          <div className="text-sm font-medium transition-colors text-foreground group-hover:text-accent-400">
                            {item.label}
                          </div>
                          <div className="font-mono text-xs text-foreground-muted">
                            {`${item.address.slice(0, 10)}...${item.address.slice(-8)}`}
                          </div>
                        </div>
                      </div>
                      <span className="px-2 py-1 text-xs rounded-full bg-accent-500/20 text-accent-400">
                        {item.type}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {!compact && (
        <p className="flex items-center gap-2 px-2 mt-3 text-sm text-foreground-muted">
          <span className="w-1 h-1 rounded-full bg-accent-400"></span>
          Enter any Ethereum address, ENS name, or token contract to explore its network
        </p>
      )}
    </div>
  );
};

export default SearchBar;