import React, { useState, useEffect } from 'react';
import {
  <PERSON>aEye,
  FaEyeSlash,
  FaPlus,
  FaTimes,
  FaWallet,
  FaExchangeAlt,
  FaEthereum,
  FaShieldAlt,
  FaExclamationTriangle,
  FaBell,
  FaBellSlash,
  FaTag,
  FaFilter,
  FaSort,
  FaTrash,
  FaExternalLinkAlt,
  FaChartLine,
  FaSearch,
  FaEdit,
  FaCrosshairs,
  FaClock,
  FaFire,
  FaCoins
} from 'react-icons/fa';
import AnimatedDropdown from './AnimatedDropdown';
import { Node } from '@/services/neo4jService';
import { useLayout } from './LayoutManager';
import { getWatchListService, WatchedWallet } from '@/services/watchListService';
import WatchListStats from './WatchListStats';

interface WatchListPanelProps {
  onSelectWallet?: (wallet: Node) => void;
  onAddToWatchList?: (address: string) => void;
  onFocusWallet?: (address: string) => void;
  className?: string;
}

const WatchListPanel: React.FC<WatchListPanelProps> = ({
  onSelectWallet,
  onAddToWatchList,
  onFocusWallet,
  className = ''
}) => {
  const { layout } = useLayout();
  const [watchedWallets, setWatchedWallets] = useState<WatchedWallet[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'addedAt' | 'lastActivity' | 'riskScore' | 'balance'>('addedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showAddForm, setShowAddForm] = useState(false);
  const [newWalletAddress, setNewWalletAddress] = useState('');
  const [newWalletLabel, setNewWalletLabel] = useState('');
  const [newWalletTags, setNewWalletTags] = useState<string[]>([]);
  const [editingWallet, setEditingWallet] = useState<string | null>(null);
  const [focusingWallet, setFocusingWallet] = useState<string | null>(null);

  // Available tags for categorization
  const availableTags = [
    'Exchange', 'DeFi', 'Personal', 'Suspicious', 'High-Risk', 'Whale',
    'Contract', 'Bridge', 'MEV', 'Arbitrage', 'Institution', 'DAO'
  ];

  // Load watch list from service
  useEffect(() => {
    // Skip on server-side
    if (typeof window === 'undefined') return;

    let unsubscribe: (() => void) | undefined;

    try {
      const service = getWatchListService();
      setWatchedWallets(service.getWatchedWallets());

      // Add some mock data if empty (for demo purposes)
      if (service.getWatchedWallets().length === 0) {
        const mockWallets = [
        {
          address: '******************************************',
          label: 'Vitalik Buterin',
          tags: ['Whale', 'Personal'],
          balance: '1247.85',
          transactionCount: 1523,
          riskScore: 15
        },
        {
          address: '0x8ba1f109551bD432803012645Hac136c',
          label: 'Binance Hot Wallet',
          tags: ['Exchange', 'High-Risk'],
          balance: '45230.12',
          transactionCount: 89234,
          riskScore: 75
        },
        {
          address: '******************************************',
          label: 'Uniswap Router',
          tags: ['DeFi', 'Contract'],
          balance: '892.34',
          transactionCount: 45678,
          riskScore: 25
        }
        ];

        mockWallets.forEach(wallet => {
          try {
            service.addWallet(wallet);
          } catch (error) {
            // Wallet might already exist
          }
        });
      }

      // Subscribe to changes
      unsubscribe = service.subscribe((wallets) => {
        setWatchedWallets(wallets);
      });
    } catch (error) {
      console.error('Failed to initialize watch list:', error);
      // Set empty state on error
      setWatchedWallets([]);
    }

    // Cleanup function
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  // Add wallet to watch list
  const addWallet = () => {
    if (!newWalletAddress.trim()) return;

    try {
      const service = getWatchListService();
      service.addWallet({
        address: newWalletAddress.trim(),
        label: newWalletLabel.trim() || undefined,
        tags: newWalletTags,
        alertsEnabled: true,
        customThresholds: {
          balanceChange: 10, // 10% change
          transactionVolume: 100, // 100 ETH
          riskScoreIncrease: 20 // 20 points
        }
      });

      // Reset form
      setNewWalletAddress('');
      setNewWalletLabel('');
      setNewWalletTags([]);
      setShowAddForm(false);

      // Notify parent component
      onAddToWatchList?.(newWalletAddress.trim());
    } catch (error) {
      console.error('Failed to add wallet:', error);
      // Could show error message to user
    }
  };

  // Remove wallet from watch list
  const removeWallet = (id: string) => {
    const service = getWatchListService();
    service.removeWallet(id);
  };

  // Toggle alerts for wallet
  const toggleAlerts = (id: string) => {
    const service = getWatchListService();
    service.toggleAlerts(id);
  };

  // Handle focus wallet with loading state
  const handleFocusWallet = (address: string) => {
    setFocusingWallet(address);
    onFocusWallet?.(address);

    // Reset focusing state after animation duration
    setTimeout(() => {
      setFocusingWallet(null);
    }, 1200); // Slightly longer than zoom animation
  };

  // Filter and sort wallets
  const filteredAndSortedWallets = watchedWallets
    .filter(wallet => {
      const matchesSearch = !searchTerm ||
        wallet.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        wallet.label?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesTag = selectedTag === 'all' || wallet.tags.includes(selectedTag);

      return matchesSearch && matchesTag;
    })
    .sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'addedAt':
          aValue = a.addedAt.getTime();
          bValue = b.addedAt.getTime();
          break;
        case 'lastActivity':
          aValue = a.lastActivity?.getTime() || 0;
          bValue = b.lastActivity?.getTime() || 0;
          break;
        case 'riskScore':
          aValue = a.riskScore || 0;
          bValue = b.riskScore || 0;
          break;
        case 'balance':
          aValue = parseFloat(a.balance || '0');
          bValue = parseFloat(b.balance || '0');
          break;
        default:
          return 0;
      }

      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });

  // Get unique tags from watched wallets
  const usedTags = Array.from(new Set(watchedWallets.flatMap(w => w.tags)));

  // Format balance display
  const formatBalance = (balance?: string) => {
    if (!balance) return '0.00 ETH';
    const num = parseFloat(balance);
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K ETH`;
    return `${num.toFixed(2)} ETH`;
  };

  // Format date display
  const formatDate = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  // Get risk color
  const getRiskColor = (riskScore?: number) => {
    if (!riskScore) return 'text-slate-400';
    if (riskScore >= 70) return 'text-red-400';
    if (riskScore >= 40) return 'text-orange-400';
    if (riskScore >= 20) return 'text-yellow-400';
    return 'text-green-400';
  };

  return (
    <div className={`h-full flex flex-col bg-slate-800 rounded-xl border border-slate-600 ${className}`}>
      {/* Header */}
      <div className="flex-shrink-0 p-4 border-b border-slate-600">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center">
              <FaEye className="text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-white">Watch List</h2>
              <p className="text-sm text-slate-400">
                {watchedWallets.length} wallet{watchedWallets.length !== 1 ? 's' : ''} monitored
              </p>
            </div>
          </div>

          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="p-2 bg-purple-500 hover:bg-purple-600 rounded-lg transition-colors text-white"
            title="Add wallet to watch list"
          >
            <FaPlus size={14} />
          </button>
        </div>

        {/* Search and Filters */}
        <div className="space-y-3">
          {/* Search */}
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 transition-colors" size={14} />
            <input
              type="text"
              placeholder="Search wallets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-200 hover:border-purple-400"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white transition-colors"
              >
                <FaTimes size={12} />
              </button>
            )}
          </div>

          {/* Filters */}
          <div className="flex gap-2">
            <AnimatedDropdown
              className="flex-1"
              options={[
                { value: 'all', label: 'All Tags', icon: <FaTag />, description: 'Show all wallets' },
                ...usedTags.map(tag => ({
                  value: tag,
                  label: tag,
                  icon: <FaTag />,
                  description: `Filter by ${tag} tag`
                }))
              ]}
              value={selectedTag}
              onChange={setSelectedTag}
              placeholder="All Tags"
              searchable={usedTags.length > 5}
              compact={true}
              showDescriptions={false}
              maxHeight="150px"
            />

            <AnimatedDropdown
              className="min-w-[140px]"
              options={[
                {
                  value: 'addedAt-desc',
                  label: 'Newest',
                  icon: <FaClock />,
                  description: 'Recently added'
                },
                {
                  value: 'addedAt-asc',
                  label: 'Oldest',
                  icon: <FaClock />,
                  description: 'Oldest added'
                },
                {
                  value: 'lastActivity-desc',
                  label: 'Active',
                  icon: <FaFire />,
                  description: 'Most active'
                },
                {
                  value: 'riskScore-desc',
                  label: 'Risky',
                  icon: <FaExclamationTriangle />,
                  description: 'Highest risk'
                },
                {
                  value: 'balance-desc',
                  label: 'Wealthy',
                  icon: <FaCoins />,
                  description: 'Highest balance'
                }
              ]}
              value={`${sortBy}-${sortOrder}`}
              onChange={(value) => {
                const [field, order] = value.split('-');
                setSortBy(field as any);
                setSortOrder(order as any);
              }}
              placeholder="Sort"
              compact={true}
              showDescriptions={true}
              maxHeight="180px"
            />
          </div>
        </div>

        {/* Watch List Statistics */}
        {watchedWallets.length > 0 && (
          <div className="mt-4">
            <WatchListStats compact />
          </div>
        )}
      </div>

      {/* Add Wallet Form */}
      {showAddForm && (
        <div className="flex-shrink-0 p-4 bg-slate-700/50 border-b border-slate-600">
          <div className="space-y-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Wallet address (0x...)"
                value={newWalletAddress}
                onChange={(e) => setNewWalletAddress(e.target.value)}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-200 hover:border-purple-400"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <FaWallet className="text-slate-400" size={14} />
              </div>
            </div>

            <div className="relative">
              <input
                type="text"
                placeholder="Label (optional)"
                value={newWalletLabel}
                onChange={(e) => setNewWalletLabel(e.target.value)}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-200 hover:border-purple-400"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <FaTag className="text-slate-400" size={14} />
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              {availableTags.map(tag => (
                <button
                  key={tag}
                  onClick={() => {
                    if (newWalletTags.includes(tag)) {
                      setNewWalletTags(newWalletTags.filter(t => t !== tag));
                    } else {
                      setNewWalletTags([...newWalletTags, tag]);
                    }
                  }}
                  className={`px-2 py-1 text-xs rounded-full border transition-colors ${
                    newWalletTags.includes(tag)
                      ? 'bg-purple-500 border-purple-500 text-white'
                      : 'bg-slate-700 border-slate-600 text-slate-300 hover:border-purple-500'
                  }`}
                >
                  {tag}
                </button>
              ))}
            </div>

            <div className="flex gap-2">
              <button
                onClick={addWallet}
                disabled={!newWalletAddress.trim()}
                className="flex-1 px-4 py-2 bg-purple-500 hover:bg-purple-600 disabled:bg-slate-600 disabled:cursor-not-allowed rounded-lg text-white font-medium transition-colors"
              >
                Add Wallet
              </button>
              <button
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 bg-slate-600 hover:bg-slate-500 rounded-lg text-white transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Wallet List */}
      <div className="flex-1 overflow-y-auto scrollable-container p-4">
        {filteredAndSortedWallets.length === 0 ? (
          <div className="text-center py-8">
            <FaEyeSlash className="mx-auto mb-3 text-4xl text-slate-400" />
            <p className="text-slate-400 mb-2">
              {watchedWallets.length === 0 ? 'No wallets in watch list' : 'No wallets match your filters'}
            </p>
            <p className="text-sm text-slate-500">
              {watchedWallets.length === 0
                ? 'Add wallets to monitor their activity and receive alerts'
                : 'Try adjusting your search or filter criteria'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredAndSortedWallets.map(wallet => (
              <div
                key={wallet.id}
                className="p-4 bg-slate-700/50 border border-slate-600/50 rounded-lg hover:bg-slate-600/50 transition-colors"
              >
                {/* Wallet Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-white truncate">
                        {wallet.label || 'Unnamed Wallet'}
                      </h3>
                      {wallet.riskScore && wallet.riskScore >= 50 && (
                        <FaExclamationTriangle className="text-red-400" size={12} />
                      )}
                    </div>
                    <p className="text-xs font-mono text-slate-400 truncate">
                      {wallet.address}
                    </p>
                  </div>

                  <div className="flex items-center gap-1 ml-2">
                    <button
                      onClick={() => toggleAlerts(wallet.id)}
                      className={`p-1 rounded transition-colors ${
                        wallet.alertsEnabled
                          ? 'text-purple-400 hover:text-purple-300'
                          : 'text-slate-500 hover:text-slate-400'
                      }`}
                      title={wallet.alertsEnabled ? 'Disable alerts' : 'Enable alerts'}
                    >
                      {wallet.alertsEnabled ? <FaBell size={12} /> : <FaBellSlash size={12} />}
                    </button>

                    <button
                      onClick={() => handleFocusWallet(wallet.address)}
                      disabled={focusingWallet === wallet.address}
                      className={`p-1 transition-colors ${
                        focusingWallet === wallet.address
                          ? 'text-yellow-400 animate-pulse cursor-not-allowed'
                          : 'text-slate-400 hover:text-yellow-400'
                      }`}
                      title={focusingWallet === wallet.address ? "Focusing..." : "Focus on bubble map"}
                    >
                      {focusingWallet === wallet.address ? (
                        <div className="w-3 h-3 border border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <FaCrosshairs size={12} />
                      )}
                    </button>

                    <button
                      onClick={() => onSelectWallet?.({
                        id: wallet.id,
                        address: wallet.address,
                        label: wallet.label,
                        balance: wallet.balance,
                        transactionCount: wallet.transactionCount,
                        tags: wallet.tags
                      } as Node)}
                      className="p-1 text-slate-400 hover:text-white transition-colors"
                      title="View wallet details"
                    >
                      <FaExternalLinkAlt size={12} />
                    </button>

                    <button
                      onClick={() => removeWallet(wallet.id)}
                      className="p-1 text-slate-500 hover:text-red-400 transition-colors"
                      title="Remove from watch list"
                    >
                      <FaTrash size={12} />
                    </button>
                  </div>
                </div>

                {/* Wallet Metrics */}
                <div className="grid grid-cols-3 gap-3 mb-3">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <FaEthereum className="text-purple-400" size={10} />
                      <span className="text-xs text-slate-400">Balance</span>
                    </div>
                    <p className="text-sm font-medium text-white">
                      {formatBalance(wallet.balance)}
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <FaExchangeAlt className="text-indigo-400" size={10} />
                      <span className="text-xs text-slate-400">Txns</span>
                    </div>
                    <p className="text-sm font-medium text-white">
                      {wallet.transactionCount?.toLocaleString() || '0'}
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 mb-1">
                      <FaShieldAlt className={getRiskColor(wallet.riskScore)} size={10} />
                      <span className="text-xs text-slate-400">Risk</span>
                    </div>
                    <p className={`text-sm font-medium ${getRiskColor(wallet.riskScore)}`}>
                      {wallet.riskScore || 0}/100
                    </p>
                  </div>
                </div>

                {/* Tags */}
                {wallet.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-2">
                    {wallet.tags.map(tag => (
                      <span
                        key={tag}
                        className="px-2 py-1 text-xs bg-slate-600 text-slate-300 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}

                {/* Footer */}
                <div className="flex items-center justify-between text-xs text-slate-500">
                  <span>Added {formatDate(wallet.addedAt)}</span>
                  {wallet.lastActivity && (
                    <span>Active {formatDate(wallet.lastActivity)}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default WatchListPanel;
