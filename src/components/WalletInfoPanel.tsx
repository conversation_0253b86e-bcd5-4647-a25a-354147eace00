import React, { useState, useEffect } from 'react';
import { FaWallet, FaExchangeAlt, FaEthereum, FaExternalLinkAlt, FaQrcode, FaCopy, FaChartLine, FaNetworkWired, FaShieldAlt, FaUsers, FaPercent, FaEye, FaCheck, FaTimes, FaStar, FaRegStar, FaLink } from 'react-icons/fa';
import SocialConnections from './SocialConnections';
import PairwiseTransactionView from './PairwiseTransactionView';
import WalletTransactionFlow from './WalletTransactionFlow';
import { SocialProfiles } from '@/services/neo4jService';
import { getWatchListService } from '@/services/watchListService';

interface WalletInfoPanelProps {
  walletData: {
    id: string;
    address: string;
    balance?: string;
    transactionCount?: number;
    label?: string;
    tags?: string[];
    // Additional fields for enhanced display
    usdValue?: number;
    percentage?: number;
    riskScore?: number;
    connections?: number;
    // Social media connectivity
    socialProfiles?: SocialProfiles;
    hasVerifiedSocials?: boolean;
    socialScore?: number;
  } | null;
  selectedWalletPair?: {
    sourceWallet: {
      id: string;
      address: string;
      label?: string;
    };
    targetWallet: {
      id: string;
      address: string;
      label?: string;
    };
  } | null;
  onAddToWatchList?: (address: string) => void; // Callback when wallet is added to watch list
}

const WalletInfoPanel: React.FC<WalletInfoPanelProps> = ({ walletData, selectedWalletPair, onAddToWatchList }) => {
  const [copySuccess, setCopySuccess] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'tokens'>('overview');
  const [transactionSubTab, setTransactionSubTab] = useState<'all' | 'pairwise'>('all');
  const [isInWatchList, setIsInWatchList] = useState(false);
  const [isAddingToWatchList, setIsAddingToWatchList] = useState(false);
  const [isRemovingFromWatchList, setIsRemovingFromWatchList] = useState(false);

  // Auto-switch to transactions tab and pairwise sub-tab when wallet pair is selected
  useEffect(() => {
    if (selectedWalletPair) {
      setActiveTab('transactions');
      setTransactionSubTab('pairwise');
    }
  }, [selectedWalletPair]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    });
  };

  // Check if wallet is already in watch list
  useEffect(() => {
    if (!walletData || typeof window === 'undefined') return;

    try {
      const service = getWatchListService();
      setIsInWatchList(service.isWatched(walletData.address));
    } catch (error) {
      console.error('Failed to check watch list status:', error);
    }
  }, [walletData]);

  // Handle adding wallet to watch list
  const handleAddToWatchList = async () => {
    if (!walletData || isAddingToWatchList || typeof window === 'undefined') return;

    setIsAddingToWatchList(true);
    try {
      const service = getWatchListService();

      // Determine tags based on wallet characteristics
      const tags: string[] = [];
      if (walletData.tags) {
        tags.push(...walletData.tags);
      }

      // Add risk-based tags
      const riskScore = walletData.riskScore || 0;
      if (riskScore >= 70) {
        tags.push('High-Risk');
      } else if (riskScore >= 40) {
        tags.push('Medium-Risk');
      }

      // Add balance-based tags
      const balance = parseFloat(walletData.balance || '0');
      if (balance >= 1000) {
        tags.push('Whale');
      }

      service.addWallet({
        address: walletData.address,
        label: walletData.label || `Wallet ${walletData.address.slice(0, 8)}...`,
        tags: Array.from(new Set(tags)), // Remove duplicates
        balance: walletData.balance,
        transactionCount: walletData.transactionCount,
        riskScore: riskScore,
        alertsEnabled: true,
        customThresholds: {
          balanceChange: 10, // 10% change
          transactionVolume: 100, // 100 ETH
          riskScoreIncrease: 20 // 20 points
        }
      });

      setIsInWatchList(true);

      // Notify parent component
      onAddToWatchList?.(walletData.address);

      console.log('Wallet added to watch list:', walletData.address);
    } catch (error) {
      console.error('Failed to add wallet to watch list:', error);
      // Could show error message to user
    } finally {
      setIsAddingToWatchList(false);
    }
  };

  // Handle removing wallet from watch list
  const handleRemoveFromWatchList = async () => {
    if (!walletData || isRemovingFromWatchList || typeof window === 'undefined') return;

    setIsRemovingFromWatchList(true);
    try {
      const service = getWatchListService();
      const watchedWallet = service.getWalletByAddress(walletData.address);

      if (watchedWallet) {
        service.removeWallet(watchedWallet.id);
        setIsInWatchList(false);
        console.log('Wallet removed from watch list:', walletData.address);
      }
    } catch (error) {
      console.error('Failed to remove wallet from watch list:', error);
      // Could show error message to user
    } finally {
      setIsRemovingFromWatchList(false);
    }
  };

  if (!walletData) {
    return (
      <div className="p-8 h-full rounded-xl glass-card">
        <div className="flex flex-col justify-center items-center h-full text-center">
          <div className="relative mb-6">
            <div className="absolute inset-0 rounded-full opacity-20 blur-lg bg-gradient-brand"></div>
            <div className="flex relative justify-center items-center w-20 h-20 rounded-full bg-gradient-brand">
              <FaNetworkWired className="text-2xl text-white" />
            </div>
          </div>
          <h3 className="mb-3 text-xl font-bold gradient-text">Address List</h3>
          <p className="leading-relaxed text-center text-foreground-muted">
            Click on any bubble in the visualization to see detailed wallet information,
            transaction history, and network analysis.
          </p>
          <div className="flex gap-2 items-center mt-6 text-sm text-accent-400">
            <div className="w-2 h-2 rounded-full animate-pulse bg-accent-400"></div>
            <span>Ready to explore</span>
          </div>
        </div>
      </div>
    );
  }

  // Enhanced address display
  const displayAddress = `${walletData.address.slice(0, 6)}...${walletData.address.slice(-4)}`;
  const fullDisplayAddress = `${walletData.address.slice(0, 10)}...${walletData.address.slice(-8)}`;

  // Mock data for enhanced display (in real app, this would come from props)
  const mockData = {
    usdValue: walletData.usdValue || 125420.50,
    percentage: walletData.percentage || 15.7,
    riskScore: walletData.riskScore || 2.3,
    connections: walletData.connections || 47,
  };

  // Mock social data for testing
  const mockSocialProfiles = walletData.socialProfiles || {
    twitter: 'vitalikbuterin',
    github: 'vbuterin',
    website: 'vitalik.ca',
    telegram: 'vitalik_eth'
  };

  return (
    <div className="flex flex-col h-full rounded-xl glass-card">
      {/* Header with tabs - Fixed at top */}
      <div className="flex-shrink-0 p-6 pb-0">
        <div className="flex justify-between items-center mb-6">
          <div className="flex gap-3 items-center flex-1 min-w-0">
            <div className="flex justify-center items-center w-10 h-10 rounded-lg bg-gradient-brand flex-shrink-0">
              <FaWallet className="text-white" />
            </div>
            <div className="flex gap-2 items-center flex-1 min-w-0">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h2 className="text-base font-bold text-foreground truncate">
                    {walletData.label || "Address Details"}
                  </h2>
                  {/* Show additional info on wider screens */}
                  <span className="hidden lg:inline-block px-2 py-1 text-xs rounded-full bg-primary-500/20 text-primary-400">
                    Active
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <p className="text-xs text-foreground-muted">
                    {mockData.percentage}% of total supply
                  </p>
                  {/* Show last activity on wider screens */}
                  <p className="hidden xl:block text-xs text-foreground-muted">
                    Last active: 2 hours ago
                  </p>
                </div>
              </div>
              {/* Add/Remove Watch List Icon */}
              <button
                onClick={isInWatchList ? handleRemoveFromWatchList : handleAddToWatchList}
                disabled={isAddingToWatchList || isRemovingFromWatchList}
                className={`p-1.5 rounded-lg transition-all duration-300 transform hover:scale-110 ${
                  isInWatchList
                    ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 hover:bg-yellow-500/30'
                    : 'bg-gray-500/20 text-gray-400 border border-gray-500/30 hover:bg-yellow-500/20 hover:text-yellow-400 hover:border-yellow-500/30'
                } ${(isAddingToWatchList || isRemovingFromWatchList) ? 'opacity-50 cursor-not-allowed' : ''}`}
                title={isAddingToWatchList
                  ? "Adding to watch list..."
                  : isRemovingFromWatchList
                    ? "Removing from watch list..."
                    : isInWatchList
                      ? "Remove from watch list"
                      : "Add to watch list"
                }
              >
                {isAddingToWatchList ? (
                  <div className="w-3.5 h-3.5 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
                ) : isRemovingFromWatchList ? (
                  <div className="w-3.5 h-3.5 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
                ) : isInWatchList ? (
                  <FaStar size={12} className="text-yellow-400" />
                ) : (
                  <FaRegStar size={12} className="text-gray-400 hover:text-yellow-400" />
                )}
              </button>
            </div>
          </div>
          <div className="flex gap-1">
            <a
              href={`https://etherscan.io/address/${walletData.address}`}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 btn-ghost"
              title="View on Etherscan"
            >
              <FaExternalLinkAlt size={14} />
            </a>
            <button
              className="p-2 btn-ghost"
              title="Show QR Code"
            >
              <FaQrcode size={14} />
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex gap-1 p-1 mb-6 rounded-lg bg-slate-700/50">
        {[
          { id: 'overview', label: 'Overview', icon: FaChartLine },
          { id: 'transactions', label: 'Txns', icon: FaExchangeAlt },
          { id: 'tokens', label: 'Tokens', icon: FaEthereum },
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id as any)}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 ${
              activeTab === id
                ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg shadow-purple-500/25'
                : 'text-gray-400 hover:text-white hover:bg-slate-600/50'
            }`}
          >
            <Icon size={14} />
            <span className="hidden sm:inline">{label}</span>
          </button>
        ))}
        </div>
      </div>

      {/* Scrollable Tab Content */}
      <div className="overflow-y-auto flex-1 px-6 pb-6 scrollable-container">
        <div className="space-y-4">
        {activeTab === 'overview' && (
          <>
            {/* Address Section - Enhanced for wider layout */}
            <div className="relative">
              <label className="block mb-2 text-xs font-medium text-foreground-muted">Wallet Address</label>
              <div className="flex gap-3 items-center p-3 rounded-lg border bg-background-secondary border-border">
                <div className="overflow-hidden flex-grow font-mono text-sm text-foreground">
                  <span className="block lg:hidden">{displayAddress}</span>
                  <span className="hidden lg:block xl:hidden">{fullDisplayAddress}</span>
                  <span className="hidden xl:block">{walletData.address}</span>
                </div>
                <button
                  onClick={() => copyToClipboard(walletData.address)}
                  className="p-2 text-white bg-gradient-to-r from-amber-500 to-yellow-500 rounded-lg shadow-lg transition-all duration-300 transform hover:from-amber-600 hover:to-yellow-600 hover:scale-110 shadow-amber-500/25"
                  title="Copy Address"
                >
                  <FaCopy size={14} />
                </button>
              </div>
              {copySuccess && (
                <div className="absolute right-0 -top-2 px-3 py-1 text-xs text-white rounded-lg shadow-lg transform -translate-y-full bg-accent-500 animate-fade-in">
                  Copied!
                </div>
              )}
            </div>

            {/* Key Metrics Grid - Enhanced for wider layout */}
            <div className="grid grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-3">
              <div className="p-4 bg-gradient-to-br rounded-lg border from-primary-500/10 to-primary-600/5 border-primary-500/20">
                <div className="flex gap-2 items-center mb-2">
                  <FaEthereum className="text-primary-400" />
                  <label className="text-xs font-medium text-foreground-muted">Balance</label>
                </div>
                <p className="text-lg font-bold text-foreground">
                  {walletData.balance || '0.00'} ETH
                </p>
                <p className="text-sm text-foreground-muted">
                  ${mockData.usdValue.toLocaleString()}
                </p>
              </div>

              <div className="p-4 bg-gradient-to-br rounded-lg border from-secondary-500/10 to-secondary-600/5 border-secondary-500/20">
                <div className="flex gap-2 items-center mb-2">
                  <FaExchangeAlt className="text-secondary-400" />
                  <label className="text-xs font-medium text-foreground-muted">Transactions</label>
                </div>
                <p className="text-lg font-bold text-foreground">
                  {walletData.transactionCount?.toLocaleString() || '0'}
                </p>
                <p className="text-sm text-foreground-muted">
                  Total activity
                </p>
              </div>

              <div className="p-4 bg-gradient-to-br rounded-lg border from-accent-500/10 to-accent-600/5 border-accent-500/20">
                <div className="flex gap-2 items-center mb-2">
                  <FaPercent className="text-accent-400" />
                  <label className="text-xs font-medium text-foreground-muted">Share</label>
                </div>
                <p className="text-lg font-bold text-foreground">
                  {mockData.percentage}%
                </p>
                <p className="text-sm text-foreground-muted">
                  Of total supply
                </p>
              </div>

              <div className="p-4 bg-gradient-to-br rounded-lg border from-orange-500/10 to-orange-600/5 border-orange-500/20">
                <div className="flex gap-2 items-center mb-2">
                  <FaUsers className="text-orange-400" />
                  <label className="text-xs font-medium text-foreground-muted">Connections</label>
                </div>
                <p className="text-lg font-bold text-foreground">
                  {mockData.connections}
                </p>
                <p className="text-sm text-foreground-muted">
                  Direct links
                </p>
              </div>
            </div>

            {/* Risk Assessment */}
            <div className="p-4 bg-gradient-to-r rounded-lg border from-background-secondary to-background-tertiary border-border">
              <div className="flex justify-between items-center mb-3">
                <div className="flex gap-2 items-center">
                  <FaShieldAlt className="text-accent-400" />
                  <span className="text-sm font-medium text-foreground">Risk Assessment</span>
                </div>
                <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                  mockData.riskScore < 3
                    ? 'bg-secondary-500/20 text-secondary-400'
                    : mockData.riskScore < 7
                    ? 'bg-orange-500/20 text-orange-400'
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {mockData.riskScore < 3 ? 'Low Risk' : mockData.riskScore < 7 ? 'Medium Risk' : 'High Risk'}
                </span>
              </div>
              <div className="w-full h-2 rounded-full bg-background-secondary">
                <div
                  className={`h-2 rounded-full transition-all duration-500 ${
                    mockData.riskScore < 3
                      ? 'bg-gradient-to-r from-secondary-500 to-secondary-400'
                      : mockData.riskScore < 7
                      ? 'bg-gradient-to-r from-orange-500 to-orange-400'
                      : 'bg-gradient-to-r from-red-500 to-red-400'
                  }`}
                  style={{ width: `${(mockData.riskScore / 10) * 100}%` }}
                ></div>
              </div>
              <p className="mt-2 text-xs text-foreground-muted">
                Score: {mockData.riskScore}/10 based on transaction patterns
              </p>
            </div>

            {/* Social Connections Section */}
            <div className="p-4 bg-gradient-to-r rounded-lg border from-slate-700/50 to-slate-600/30 border-slate-600/50">
              <SocialConnections
                socialProfiles={mockSocialProfiles}
                hasVerifiedSocials={walletData.hasVerifiedSocials || true}
                socialScore={walletData.socialScore || 85}
              />
            </div>
          </>
        )}

        {activeTab === 'transactions' && (
          <div className="space-y-4">
            {/* Sub-tab Navigation */}
            <div className="flex gap-1 p-1 rounded-lg bg-slate-700/30">
              <button
                onClick={() => setTransactionSubTab('all')}
                className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-lg text-xs font-medium transition-all duration-300 ${
                  transactionSubTab === 'all'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-slate-600/50'
                }`}
              >
                <FaExchangeAlt size={12} />
                All Transactions
              </button>
              <button
                onClick={() => setTransactionSubTab('pairwise')}
                className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-lg text-xs font-medium transition-all duration-300 ${
                  transactionSubTab === 'pairwise'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                    : 'text-gray-400 hover:text-white hover:bg-slate-600/50'
                } ${!selectedWalletPair ? 'opacity-50 cursor-not-allowed' : ''}`}
                disabled={!selectedWalletPair}
              >
                <FaLink size={12} />
                Pairwise
              </button>
            </div>

            {/* All Transactions Tab */}
            {transactionSubTab === 'all' && (
              <div className="h-full">
                <WalletTransactionFlow
                  walletAddress={walletData.address}
                  onTransactionSelect={(transaction) => {
                    console.log('Transaction selected:', transaction);
                  }}
                />
              </div>
            )}

            {/* Pairwise Transactions Tab */}
            {transactionSubTab === 'pairwise' && (
              <div className="space-y-3">
                {selectedWalletPair ? (
                  <PairwiseTransactionView
                    sourceWallet={selectedWalletPair.sourceWallet}
                    targetWallet={selectedWalletPair.targetWallet}
                  />
                ) : (
                  <div className="text-center py-8">
                    <FaLink className="mx-auto mb-3 text-3xl text-gray-500" />
                    <p className="text-foreground-muted">Click on a connection between two wallets to view pairwise transactions</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {activeTab === 'tokens' && (
          <div className="space-y-3">
            <div className="mb-4 text-center">
              <FaEthereum className="mx-auto mb-3 text-4xl text-accent-400" />
              <p className="text-foreground-muted">Token Holdings</p>
            </div>

            {/* Demo token list for scroll testing */}
            {Array.from({ length: 12 }, (_, i) => {
              const tokens = ['USDC', 'USDT', 'DAI', 'WETH', 'UNI', 'LINK', 'AAVE', 'COMP', 'MKR', 'SNX', 'CRV', 'SUSHI'];
              const token = tokens[i % tokens.length];
              const balance = (Math.random() * 10000).toFixed(2);
              const value = (parseFloat(balance) * Math.random() * 2).toFixed(2);

              return (
                <div key={i} className="p-4 rounded-lg border transition-colors bg-background-secondary border-border hover:bg-background-tertiary">
                  <div className="flex justify-between items-center mb-2">
                    <div className="flex gap-3 items-center flex-1 min-w-0">
                      <div className="flex justify-center items-center w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex-shrink-0">
                        <span className="text-xs font-bold text-white">{token.substring(0, 2)}</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-foreground">{token}</span>
                          {/* Show percentage on wider screens */}
                          <span className="hidden lg:inline-block px-2 py-1 text-xs rounded-full bg-accent-500/20 text-accent-400">
                            {(Math.random() * 50).toFixed(1)}%
                          </span>
                        </div>
                        <p className="text-xs text-foreground-muted truncate">
                          {token === 'USDC' ? 'USD Coin' : token === 'USDT' ? 'Tether USD' : token === 'DAI' ? 'Dai Stablecoin' : `${token} Token`}
                        </p>
                        {/* Show contract address on extra wide screens */}
                        <p className="hidden xl:block text-xs text-foreground-muted font-mono mt-1">
                          0x{Math.random().toString(16).substring(2, 10)}...{Math.random().toString(16).substring(2, 6)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right ml-3 flex-shrink-0">
                      <span className="text-sm font-bold text-foreground">{balance}</span>
                      <p className="text-xs text-foreground-muted">${value}</p>
                      {/* Show 24h change on wider screens */}
                      <p className="hidden lg:block text-xs text-green-400">
                        +{(Math.random() * 10).toFixed(2)}%
                      </p>
                    </div>
                  </div>
                  <div className="w-full h-1 rounded-full bg-background-tertiary">
                    <div
                      className="h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                      style={{ width: `${Math.random() * 100}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Tags Section */}
        {walletData.tags && walletData.tags.length > 0 && (
          <div className="pt-4 border-t border-border">
            <label className="block mb-3 text-xs font-medium text-foreground-muted">Tags & Labels</label>
            <div className="flex flex-wrap gap-2">
              {walletData.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-3 py-1 text-xs font-medium bg-gradient-to-r rounded-full border from-accent-500/20 to-accent-600/20 text-accent-300 border-accent-500/30"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Action Links */}
        <div className="pt-4 mt-4 border-t border-slate-600">
          <div className="grid grid-cols-2 gap-3">
            <a
              href={`https://etherscan.io/address/${walletData.address}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex gap-2 justify-center items-center py-3 text-sm font-semibold text-white bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl shadow-lg transition-all duration-300 transform hover:from-blue-600 hover:to-purple-600 shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-105"
            >
              <FaExternalLinkAlt size={12} />
              <span>Etherscan</span>
            </a>
            <a
              href={`https://etherscan.io/txs?a=${walletData.address}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex gap-2 justify-center items-center py-3 text-sm font-semibold text-white bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl shadow-lg transition-all duration-300 transform hover:from-green-600 hover:to-emerald-600 shadow-green-500/25 hover:shadow-green-500/40 hover:scale-105"
            >
              <FaExchangeAlt size={12} />
              <span>Transactions</span>
            </a>
          </div>

          <div className="mt-3">
            <a
              href={`https://etherscan.io/tokenholdings?a=${walletData.address}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex gap-2 justify-center items-center py-3 w-full text-sm font-medium rounded-xl border transition-all duration-300 transform text-slate-300 border-slate-500 hover:border-amber-400 hover:bg-amber-500/10 hover:text-amber-400 hover:scale-105"
            >
              <FaEthereum size={12} />
              <span>View Token Holdings</span>
              <FaExternalLinkAlt size={10} />
            </a>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
};

export default WalletInfoPanel;