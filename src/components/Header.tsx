import Link from 'next/link';
import { FaBitcoin, FaInfoCircle, FaGithub, FaChartLine, FaNetworkWired } from 'react-icons/fa';
import SearchBar from './SearchBar';

interface HeaderProps {
  onSearch?: (address: string) => void;
  showSearch?: boolean;
}

const Header: React.FC<HeaderProps> = ({ onSearch, showSearch = false }) => {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl bg-background/90 border-b border-border-accent/50 transition-all duration-300">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between gap-4">
          {/* Compact Logo with enhanced gradient effect */}
          <Link href="/" className="flex items-center gap-2.5 group flex-shrink-0">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-primary-500 via-accent-500 to-secondary-500 rounded-xl blur-md opacity-60 group-hover:opacity-90 transition-all duration-300 scale-110"></div>
              <div className="relative bg-gradient-to-br from-primary-500 via-accent-500 to-secondary-500 p-2.5 rounded-xl shadow-lg">
                <FaNetworkWired className="text-white text-lg drop-shadow-sm" />
              </div>
            </div>
            <div className="flex flex-col">
              <span className="text-xl font-bold bg-gradient-to-r from-primary-400 via-accent-400 to-secondary-400 bg-clip-text text-transparent">
                BubbleMap
              </span>
              <span className="text-[10px] text-foreground-muted font-medium tracking-widest uppercase opacity-80">
                Crypto Analytics
              </span>
            </div>
          </Link>

          {/* Integrated Search Bar - Center */}
          {showSearch && onSearch && (
            <div className="flex-1 max-w-2xl mx-4">
              <SearchBar onSearch={onSearch} compact={true} />
            </div>
          )}

          {/* Compact Navigation with enhanced glass morphism */}
          <nav className="flex items-center gap-1 flex-shrink-0">
            <Link
              href="/map"
              className="group relative px-3 py-2 rounded-lg text-sm font-medium text-foreground-secondary hover:text-foreground transition-all duration-200 hover:bg-background-tertiary/50 backdrop-blur-sm"
            >
              <div className="flex items-center gap-2">
                <FaNetworkWired className="text-primary-400 group-hover:text-primary-300 transition-colors text-xs" />
                <span className="hidden sm:inline">Bubble Map</span>
              </div>
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary-500/0 via-primary-500/5 to-primary-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
            </Link>

            <Link
              href="/about"
              className="group relative px-3 py-2 rounded-lg text-sm font-medium text-foreground-secondary hover:text-foreground transition-all duration-200 hover:bg-background-tertiary/50 backdrop-blur-sm"
            >
              <div className="flex items-center gap-2">
                <FaInfoCircle className="text-accent-400 group-hover:text-accent-300 transition-colors text-xs" />
                <span className="hidden sm:inline">About</span>
              </div>
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-accent-500/0 via-accent-500/5 to-accent-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
            </Link>

            <a
              href="https://github.com/HoiAnHub/crypto-bubble-map"
              target="_blank"
              rel="noopener noreferrer"
              className="group relative px-3 py-2 rounded-lg text-sm font-medium text-foreground-secondary hover:text-foreground transition-all duration-200 hover:bg-background-tertiary/50 backdrop-blur-sm"
            >
              <div className="flex items-center gap-2">
                <FaGithub className="text-accent-400 group-hover:text-accent-300 transition-colors text-xs" />
                <span className="hidden sm:inline">GitHub</span>
              </div>
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-accent-500/0 via-accent-500/5 to-accent-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
            </a>

            {/* Compact version badge with enhanced styling */}
            <div className="hidden md:flex items-center gap-1.5 ml-3 px-2.5 py-1 bg-gradient-to-r from-accent-500/10 to-primary-500/10 border border-accent-500/20 rounded-full backdrop-blur-sm">
              <div className="w-1.5 h-1.5 bg-gradient-to-r from-accent-400 to-primary-400 rounded-full animate-pulse shadow-sm"></div>
              <span className="text-[10px] text-accent-400 font-semibold tracking-wide">v2.0</span>
            </div>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;