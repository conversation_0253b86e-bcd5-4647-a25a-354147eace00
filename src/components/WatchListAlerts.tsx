import React, { useState, useEffect } from 'react';
import {
  FaBell,
  FaBellSlash,
  FaExclamationTriangle,
  FaShieldAlt,
  FaExchangeAlt,
  FaEthereum,
  FaTimes,
  FaCheck,
  FaCheckDouble,
  FaEye,
  FaClock
} from 'react-icons/fa';
import { getWatchListService, WalletAlert } from '@/services/watchListService';

interface WatchListAlertsProps {
  className?: string;
  maxAlerts?: number;
  showOnlyUnacknowledged?: boolean;
}

const WatchListAlerts: React.FC<WatchListAlertsProps> = ({
  className = '',
  maxAlerts = 10,
  showOnlyUnacknowledged = true
}) => {
  const [alerts, setAlerts] = useState<WalletAlert[]>([]);
  const [watchedWallets, setWatchedWallets] = useState<any[]>([]);

  useEffect(() => {
    // Skip on server-side
    if (typeof window === 'undefined') return;

    try {
      const service = getWatchListService();

      // Load initial data
      setAlerts(service.getAlerts());
      setWatchedWallets(service.getWatchedWallets());

      // Subscribe to changes
      const unsubscribeAlerts = service.subscribeToAlerts((newAlerts) => {
        setAlerts(newAlerts);
      });

      const unsubscribeWallets = service.subscribe((wallets) => {
        setWatchedWallets(wallets);
      });

      return () => {
        unsubscribeAlerts();
        unsubscribeWallets();
      };
    } catch (error) {
      console.error('Failed to initialize watch list alerts:', error);
      // Keep empty state on error
    }
  }, []);

  // Filter alerts
  const filteredAlerts = alerts
    .filter(alert => !showOnlyUnacknowledged || !alert.acknowledged)
    .slice(0, maxAlerts);

  // Get wallet info for alert
  const getWalletInfo = (walletId: string) => {
    return watchedWallets.find(w => w.id === walletId);
  };

  // Get alert icon
  const getAlertIcon = (type: WalletAlert['type']) => {
    switch (type) {
      case 'balance_change':
        return <FaEthereum className="text-blue-400" size={14} />;
      case 'high_volume':
        return <FaExchangeAlt className="text-green-400" size={14} />;
      case 'risk_increase':
        return <FaShieldAlt className="text-red-400" size={14} />;
      case 'suspicious_activity':
        return <FaExclamationTriangle className="text-orange-400" size={14} />;
      case 'new_transaction':
        return <FaExchangeAlt className="text-cyan-400" size={14} />;
      default:
        return <FaBell className="text-slate-400" size={14} />;
    }
  };

  // Get severity color
  const getSeverityColor = (severity: WalletAlert['severity']) => {
    switch (severity) {
      case 'critical':
        return 'border-red-500/50 bg-red-500/10 text-red-400';
      case 'high':
        return 'border-orange-500/50 bg-orange-500/10 text-orange-400';
      case 'medium':
        return 'border-yellow-500/50 bg-yellow-500/10 text-yellow-400';
      case 'low':
        return 'border-blue-500/50 bg-blue-500/10 text-blue-400';
      default:
        return 'border-slate-600/50 bg-slate-700/50 text-slate-300';
    }
  };

  // Format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  // Acknowledge alert
  const acknowledgeAlert = (alertId: string) => {
    try {
      const service = getWatchListService();
      service.acknowledgeAlert(alertId);
    } catch (error) {
      console.error('Failed to acknowledge alert:', error);
    }
  };

  // Acknowledge all alerts
  const acknowledgeAllAlerts = () => {
    try {
      const service = getWatchListService();
      service.acknowledgeAllAlerts();
    } catch (error) {
      console.error('Failed to acknowledge all alerts:', error);
    }
  };

  if (filteredAlerts.length === 0) {
    return (
      <div className={`p-4 bg-slate-700/30 border border-slate-600/50 rounded-lg ${className}`}>
        <div className="flex items-center gap-2 mb-2">
          <FaBellSlash className="text-slate-400" size={16} />
          <span className="text-sm font-medium text-slate-300">No Active Alerts</span>
        </div>
        <p className="text-xs text-slate-500">
          All watched wallets are operating normally
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FaBell className="text-red-400" size={16} />
          <span className="text-sm font-medium text-white">
            Watch List Alerts ({filteredAlerts.length})
          </span>
        </div>

        {filteredAlerts.length > 0 && (
          <button
            onClick={acknowledgeAllAlerts}
            className="flex items-center gap-1 px-2 py-1 text-xs bg-slate-600 hover:bg-slate-500 rounded text-slate-300 hover:text-white transition-colors"
            title="Acknowledge all alerts"
          >
            <FaCheckDouble size={10} />
            <span>Clear All</span>
          </button>
        )}
      </div>

      {/* Alerts List */}
      <div className="space-y-2">
        {filteredAlerts.map(alert => {
          const wallet = getWalletInfo(alert.walletId);
          const severityColor = getSeverityColor(alert.severity);

          return (
            <div
              key={alert.id}
              className={`p-3 border rounded-lg ${severityColor} transition-all duration-200`}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  {getAlertIcon(alert.type)}
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium truncate">
                      {wallet?.label || 'Unknown Wallet'}
                    </h4>
                    <p className="text-xs opacity-75 font-mono truncate">
                      {wallet?.address || alert.walletId}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-1 ml-2">
                  <span className="text-xs opacity-75">
                    {formatTimeAgo(alert.timestamp)}
                  </span>
                  <button
                    onClick={() => acknowledgeAlert(alert.id)}
                    className="p-1 hover:bg-white/10 rounded transition-colors"
                    title="Acknowledge alert"
                  >
                    <FaCheck size={10} />
                  </button>
                </div>
              </div>

              <p className="text-sm mb-2">{alert.message}</p>

              {/* Alert Details */}
              {alert.details && (
                <div className="text-xs opacity-75 space-y-1">
                  {alert.type === 'balance_change' && alert.details.changePercent && (
                    <div className="flex justify-between">
                      <span>Change:</span>
                      <span className={alert.details.changePercent > 0 ? 'text-green-400' : 'text-red-400'}>
                        {alert.details.changePercent > 0 ? '+' : ''}{alert.details.changePercent.toFixed(1)}%
                      </span>
                    </div>
                  )}
                  {alert.type === 'risk_increase' && alert.details.increase && (
                    <div className="flex justify-between">
                      <span>Risk Increase:</span>
                      <span className="text-red-400">+{alert.details.increase} points</span>
                    </div>
                  )}
                </div>
              )}

              {/* Severity Badge */}
              <div className="flex items-center justify-between mt-2">
                <span className={`px-2 py-1 text-xs rounded-full border ${
                  alert.severity === 'critical' ? 'border-red-500/50 bg-red-500/20 text-red-300' :
                  alert.severity === 'high' ? 'border-orange-500/50 bg-orange-500/20 text-orange-300' :
                  alert.severity === 'medium' ? 'border-yellow-500/50 bg-yellow-500/20 text-yellow-300' :
                  'border-blue-500/50 bg-blue-500/20 text-blue-300'
                }`}>
                  {alert.severity.toUpperCase()}
                </span>

                <div className="flex items-center gap-1 text-xs opacity-50">
                  <FaClock size={8} />
                  <span>{alert.timestamp.toLocaleTimeString()}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* View All Link */}
      {alerts.length > maxAlerts && (
        <div className="text-center">
          <button className="text-xs text-blue-400 hover:text-blue-300 transition-colors">
            View all {alerts.length} alerts
          </button>
        </div>
      )}
    </div>
  );
};

export default WatchListAlerts;
