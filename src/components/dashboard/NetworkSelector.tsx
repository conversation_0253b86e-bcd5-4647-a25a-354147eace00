import React, { useState, useEffect, useRef } from 'react';
import {
  FaChevronDown,
  FaCheck,
  FaSearch,
  FaNetworkWired,
  FaLayerGroup,
  FaFilter
} from 'react-icons/fa';
import { networkService, NetworkInfo } from '@/services/networkService';
import { scrollToDropdown, scrollToTouchFixedBanner } from '@/utils/scrollUtils';

interface NetworkSelectorProps {
  selectedNetwork: string;
  onNetworkChange: (networkId: string) => void;
  className?: string;
}

const NetworkSelector: React.FC<NetworkSelectorProps> = ({
  selectedNetwork,
  onNetworkChange,
  className = ''
}) => {
  const [networks, setNetworks] = useState<NetworkInfo[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Load networks
    const allNetworks = networkService.getNetworks();
    setNetworks(allNetworks);

    // Subscribe to network updates
    const unsubscribe = networkService.subscribe((updatedNetworks) => {
      setNetworks(updatedNetworks);
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Auto scroll when dropdown opens
  useEffect(() => {
    if (isOpen && dropdownRef.current) {
      scrollToDropdown({
        element: dropdownRef.current,
        offset: 120, // Extra space above dropdown
        dropdownHeight: 420, // Approximate dropdown height (max-h-80 + padding + header)
        delay: 150 // Small delay to ensure dropdown is rendered
      });
    }
  }, [isOpen]);

  const selectedNetworkInfo = networks.find(n => n.id === selectedNetwork);

  const categories = [
    { id: 'all', label: 'All Networks', icon: FaNetworkWired },
    { id: 'layer1', label: 'Layer 1', icon: FaLayerGroup },
    { id: 'layer2', label: 'Layer 2', icon: FaLayerGroup },
    { id: 'sidechain', label: 'Sidechains', icon: FaLayerGroup }
  ];

  const filteredNetworks = networks.filter(network => {
    const matchesSearch = network.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         network.symbol.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || network.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const formatNumber = (num: number) => {
    if (num >= 1e9) return `$${(num / 1e9).toFixed(1)}B`;
    if (num >= 1e6) return `$${(num / 1e6).toFixed(1)}M`;
    if (num >= 1e3) return `$${(num / 1e3).toFixed(1)}K`;
    return `$${num.toFixed(0)}`;
  };

  const handleNetworkSelect = (networkId: string) => {
    onNetworkChange(networkId);
    setIsOpen(false);
    setSearchTerm('');

    // Scroll to position Network Selector just below the fixed banner
    const networkContainer = document.getElementById('network-selector-container');
    if (networkContainer) {
      // Add visual feedback class
      networkContainer.classList.add('network-selector-scrolling');

      scrollToTouchFixedBanner(networkContainer, 120, {
        delay: 400, // Wait for dropdown close animation
        extraOffset: 8 // Small gap for visual comfort
      }).then(() => {
        // Remove visual feedback class after scroll completes
        setTimeout(() => {
          networkContainer.classList.remove('network-selector-scrolling');
        }, 600);
      });
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Selected Network Display */}
      <button
        onClick={() => {
          setIsOpen(!isOpen);
        }}
        className="flex items-center gap-3 w-full px-4 py-3 text-left bg-background-secondary/50 hover:bg-background-secondary border border-border-secondary hover:border-border-accent rounded-xl transition-all duration-200 group"
      >
        {selectedNetworkInfo ? (
          <>
            <div
              className="flex items-center justify-center w-8 h-8 rounded-full text-lg"
              style={{
                background: `linear-gradient(135deg, ${selectedNetworkInfo.gradientFrom}, ${selectedNetworkInfo.gradientTo})`,
                color: 'white'
              }}
            >
              {selectedNetworkInfo.icon}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span className="font-semibold text-foreground">{selectedNetworkInfo.name}</span>
                <span className="px-2 py-0.5 text-xs font-medium bg-background-tertiary text-foreground-muted rounded-full">
                  {selectedNetworkInfo.symbol}
                </span>
              </div>
              <div className="text-sm text-foreground-muted truncate">
                {selectedNetworkInfo.description}
              </div>
            </div>
            <div className="flex items-center gap-2 text-foreground-muted">
              {selectedNetworkInfo.tvl && (
                <span className="text-xs font-medium">
                  TVL: {formatNumber(selectedNetworkInfo.tvl)}
                </span>
              )}
              <FaChevronDown
                className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
                size={14}
              />
            </div>
          </>
        ) : (
          <>
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-background-tertiary">
              <FaNetworkWired className="text-foreground-muted" />
            </div>
            <div className="flex-1">
              <span className="text-foreground-muted">Select Network</span>
            </div>
            <FaChevronDown
              className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
              size={14}
            />
          </>
        )}
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-background-secondary border border-border-secondary rounded-xl shadow-2xl z-50 overflow-hidden network-dropdown">
          {/* Search and Filter Header */}
          <div className="p-4 border-b border-border-secondary">
            {/* Search */}
            <div className="relative mb-3">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted" size={14} />
              <input
                type="text"
                placeholder="Search networks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background-tertiary border border-border-secondary rounded-lg text-foreground placeholder-foreground-muted focus:outline-none focus:border-border-accent transition-colors search-input"
              />
            </div>

            {/* Category Filter */}
            <div className="flex gap-1 overflow-x-auto">
              {categories.map(category => {
                const Icon = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 whitespace-nowrap ${
                      selectedCategory === category.id
                        ? 'bg-primary-500 text-white'
                        : 'bg-background-tertiary text-foreground-muted hover:bg-background-secondary hover:text-foreground'
                    }`}
                  >
                    <Icon size={12} />
                    {category.label}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Networks List */}
          <div className="max-h-80 overflow-y-auto">
            {filteredNetworks.length > 0 ? (
              filteredNetworks.map(network => (
                <button
                  key={network.id}
                  onClick={() => handleNetworkSelect(network.id)}
                  className="flex items-center gap-3 w-full px-4 py-3 text-left hover:bg-background-tertiary transition-colors group"
                >
                  <div
                    className="flex items-center justify-center w-8 h-8 rounded-full text-lg flex-shrink-0 network-icon"
                    style={{
                      background: `linear-gradient(135deg, ${network.gradientFrom}, ${network.gradientTo})`,
                      color: 'white'
                    }}
                  >
                    {network.icon}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-foreground">{network.name}</span>
                      <span className="px-2 py-0.5 text-xs font-medium bg-background-secondary text-foreground-muted rounded-full">
                        {network.symbol}
                      </span>
                      <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                        network.category === 'layer1' ? 'bg-blue-500/10 text-blue-400' :
                        network.category === 'layer2' ? 'bg-green-500/10 text-green-400' :
                        'bg-purple-500/10 text-purple-400'
                      }`}>
                        {network.category.toUpperCase()}
                      </span>
                    </div>

                    <div className="flex items-center gap-4 text-xs text-foreground-muted">
                      {network.tvl && (
                        <span>TVL: {formatNumber(network.tvl)}</span>
                      )}
                      {network.dailyTransactions && (
                        <span>Txs: {(network.dailyTransactions / 1000000).toFixed(1)}M/day</span>
                      )}
                      {network.tps && (
                        <span>TPS: {network.tps}</span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {selectedNetwork === network.id && (
                      <div className="flex items-center justify-center w-5 h-5 bg-primary-500 rounded-full">
                        <FaCheck className="text-white" size={10} />
                      </div>
                    )}
                  </div>
                </button>
              ))
            ) : (
              <div className="px-4 py-8 text-center text-foreground-muted">
                <FaFilter className="mx-auto mb-2" size={24} />
                <p>No networks found</p>
                <p className="text-xs">Try adjusting your search or filter</p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-3 border-t border-border-secondary bg-background-tertiary/50">
            <div className="flex items-center justify-between text-xs text-foreground-muted">
              <span>{filteredNetworks.length} networks available</span>
              <span>More networks coming soon</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NetworkSelector;
