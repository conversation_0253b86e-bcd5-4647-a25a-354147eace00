import React, { useState, useEffect } from 'react';
import {
  FaTrophy,
  FaShieldAlt,
  FaExclamationTriangle,
  FaChartLine,
  FaNetworkWired,
  FaEthereum,
  FaArrowUp,
  FaArrowDown,
  FaMinus,
  FaCopy,
  FaExternalLinkAlt
} from 'react-icons/fa';
import { walletRankingService, WalletRanking, WalletMetrics } from '@/services/walletRankingService';
import { truncateAddress } from '@/utils/graphUtils';

interface TopWalletRankingsProps {
  className?: string;
  selectedNetwork?: string;
}

const TopWalletRankings: React.FC<TopWalletRankingsProps> = ({
  className = '',
  selectedNetwork = 'ethereum'
}) => {
  const [selectedCategory, setSelectedCategory] = useState('quality');
  const [rankings, setRankings] = useState<WalletRanking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [showContent, setShowContent] = useState(false);
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);

  const categories = walletRankingService.getRankingCategories();

  useEffect(() => {
    loadRankings();

    // Subscribe to real-time updates
    const unsubscribe = walletRankingService.subscribe(() => {
      // Reload rankings when data updates with smooth transition
      handleDataUpdate();
    });

    return unsubscribe;
  }, [selectedCategory, selectedNetwork]);

  const loadRankings = () => {
    setIsLoading(true);
    setIsTransitioning(false);
    setShowContent(false);

    // Simulate realistic loading delay with smooth transition
    setTimeout(() => {
      const data = walletRankingService.getTopWallets(selectedCategory, 20);
      setRankings(data);

      // Start transition sequence
      setTimeout(() => {
        setIsLoading(false);
        setIsTransitioning(true);

        // Show content after a brief delay
        setTimeout(() => {
          setShowContent(true);
          setIsTransitioning(false);
        }, 300);
      }, 400);
    }, 1000);
  };

  const handleDataUpdate = () => {
    if (!isLoading) {
      // Smooth update for real-time data
      setIsTransitioning(true);

      setTimeout(() => {
        const data = walletRankingService.getTopWallets(selectedCategory, 20);
        setRankings(data);

        setTimeout(() => {
          setIsTransitioning(false);
        }, 400);
      }, 200);
    }
  };

  const getWalletTypeColor = (type: WalletMetrics['walletType']) => {
    switch (type) {
      case 'regular': return 'text-blue-400 bg-blue-500/10 border-blue-500/20';
      case 'exchange': return 'text-green-400 bg-green-500/10 border-green-500/20';
      case 'contract': return 'text-purple-400 bg-purple-500/10 border-purple-500/20';
      case 'whale': return 'text-cyan-400 bg-cyan-500/10 border-cyan-500/20';
      case 'defi': return 'text-pink-400 bg-pink-500/10 border-pink-500/20';
      case 'bridge': return 'text-orange-400 bg-orange-500/10 border-orange-500/20';
      case 'miner': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/20';
    }
  };

  const getRiskColor = (riskScore: number) => {
    if (riskScore < 25) return 'text-green-400';
    if (riskScore < 50) return 'text-yellow-400';
    if (riskScore < 75) return 'text-orange-400';
    return 'text-red-400';
  };

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <FaTrophy className="text-yellow-400" />;
    if (rank === 2) return <FaTrophy className="text-gray-300" />;
    if (rank === 3) return <FaTrophy className="text-orange-400" />;
    return <span className="text-foreground-muted font-bold">#{rank}</span>;
  };

  const getChangeIcon = (change?: number) => {
    if (!change || change === 0) return <FaMinus className="text-gray-400" size={12} />;
    if (change > 0) return <FaArrowUp className="text-green-400" size={12} />;
    return <FaArrowDown className="text-red-400" size={12} />;
  };

  const copyAddress = async (address: string) => {
    try {
      await navigator.clipboard.writeText(address);
      setCopiedAddress(address);
      setTimeout(() => setCopiedAddress(null), 2000);
    } catch (error) {
      console.error('Failed to copy address:', error);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(0);
  };

  const formatEth = (eth: number) => {
    if (eth >= 1000000) return `${(eth / 1000000).toFixed(1)}M ETH`;
    if (eth >= 1000) return `${(eth / 1000).toFixed(1)}K ETH`;
    return `${eth.toFixed(2)} ETH`;
  };

  // Loading Content Component
  const LoadingContent = () => (
    <div className="space-y-6">
      {/* Loading header with animated text */}
      <div className="text-center py-4">
        <div className="flex items-center justify-center gap-2 mb-3">
          <div className="flex gap-1">
            <div className="w-2 h-2 bg-primary-400 rounded-full loading-dots"></div>
            <div className="w-2 h-2 bg-accent-400 rounded-full loading-dots"></div>
            <div className="w-2 h-2 bg-secondary-400 rounded-full loading-dots"></div>
          </div>
        </div>
        <p className="text-sm text-foreground-muted animate-pulse">Loading wallet rankings...</p>
      </div>

      {/* Enhanced Category selector skeleton */}
      <div className="flex flex-wrap gap-2 mb-2">
        {[1, 2, 3, 4, 5].map(i => (
          <div
            key={i}
            className="relative h-10 w-20 sm:w-24 rounded-lg overflow-hidden bg-background-secondary/30 border border-border-secondary stagger-fade-in"
            style={{ animationDelay: `${i * 0.1}s` }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary-500/10 to-transparent animate-shimmer" />
            <div className="absolute inset-2 bg-background-tertiary/40 rounded animate-pulse" />
          </div>
        ))}
      </div>

      {/* Enhanced Rankings skeleton with staggered animation */}
      <div className="space-y-3">
        {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
          <div
            key={i}
            className="relative p-4 rounded-xl glass-card border border-border-secondary overflow-hidden stagger-fade-in"
            style={{ animationDelay: `${i * 0.1}s` }}
          >
            {/* Enhanced shimmer effect overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary-500/8 via-accent-500/8 to-transparent animate-shimmer"
                 style={{ animationDelay: `${i * 0.2}s`, animationDuration: '2.5s' }} />

            {/* Gradient pulse background */}
            <div className="absolute inset-0 gradient-pulse opacity-30" style={{ animationDelay: `${i * 0.3}s` }} />

            <div className="flex items-center gap-2 sm:gap-4 relative z-10">
              {/* Enhanced rank skeleton */}
              <div className="relative flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-background-tertiary/50 border border-border-secondary flex-shrink-0 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-accent-500/20 to-transparent animate-shimmer"
                     style={{ animationDelay: `${i * 0.15}s` }} />
                <div className="w-4 h-4 bg-background-tertiary/60 rounded-full skeleton-loading"
                     style={{ animationDelay: `${i * 0.2}s` }} />
              </div>

              {/* Enhanced wallet info skeleton */}
              <div className="flex-1 min-w-0 space-y-2">
                {/* Title and badges */}
                <div className="flex items-center gap-1 sm:gap-2 flex-wrap">
                  <div className="relative h-4 sm:h-5 rounded overflow-hidden bg-background-tertiary/40"
                       style={{ width: `${Math.random() * 40 + 60}%` }}>
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary-500/20 to-transparent animate-shimmer"
                         style={{ animationDelay: `${i * 0.1}s` }} />
                  </div>
                  <div className="relative h-5 w-12 rounded-full overflow-hidden bg-background-tertiary/30">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-accent-500/20 to-transparent animate-shimmer"
                         style={{ animationDelay: `${i * 0.15}s` }} />
                  </div>
                </div>

                {/* Address and metrics */}
                <div className="flex items-center gap-2 sm:gap-4 flex-wrap">
                  <div className="relative h-3 rounded overflow-hidden bg-background-tertiary/40"
                       style={{ width: `${Math.random() * 30 + 40}%` }}>
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-secondary-500/15 to-transparent animate-shimmer"
                         style={{ animationDelay: `${i * 0.12}s` }} />
                  </div>
                  <div className="relative h-3 w-16 rounded overflow-hidden bg-background-tertiary/30">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-green-500/15 to-transparent animate-shimmer"
                         style={{ animationDelay: `${i * 0.18}s` }} />
                  </div>
                  <div className="relative h-3 w-12 rounded overflow-hidden bg-background-tertiary/30">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/15 to-transparent animate-shimmer"
                         style={{ animationDelay: `${i * 0.25}s` }} />
                  </div>
                </div>
              </div>

              {/* Enhanced metrics skeleton */}
              <div className="flex items-center gap-2 sm:gap-6 flex-shrink-0">
                {/* Score */}
                <div className="text-center space-y-1">
                  <div className="relative h-5 sm:h-6 w-8 rounded overflow-hidden bg-background-tertiary/50">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary-500/25 to-transparent animate-shimmer"
                         style={{ animationDelay: `${i * 0.1}s` }} />
                  </div>
                  <div className="relative h-2 w-8 rounded overflow-hidden bg-background-tertiary/30 hidden sm:block">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary-500/15 to-transparent animate-shimmer"
                         style={{ animationDelay: `${i * 0.15}s` }} />
                  </div>
                </div>

                {/* Risk Score */}
                <div className="text-center space-y-1">
                  <div className="relative h-5 sm:h-6 w-6 rounded overflow-hidden bg-background-tertiary/50">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-red-500/20 to-transparent animate-shimmer"
                         style={{ animationDelay: `${i * 0.12}s` }} />
                  </div>
                  <div className="relative h-2 w-6 rounded overflow-hidden bg-background-tertiary/30 hidden sm:block">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-red-500/10 to-transparent animate-shimmer"
                         style={{ animationDelay: `${i * 0.18}s` }} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Enhanced Load More Button skeleton */}
      <div className="text-center pt-4">
        <div className="relative inline-block h-10 w-32 bg-background-secondary/50 border border-border-secondary rounded-lg overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-accent-500/10 to-transparent animate-shimmer" />
          <div className="absolute inset-2 bg-background-tertiary/30 rounded skeleton-loading" />
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <LoadingContent />
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Category Selector */}
      <div className={`flex flex-wrap gap-2 mb-2 transition-all duration-500 ${
        isTransitioning ? 'opacity-50 pointer-events-none' : 'opacity-100'
      }`}>
        {categories.map(category => (
          <button
            key={category.key}
            onClick={() => setSelectedCategory(category.key)}
            disabled={isLoading || isTransitioning}
            className={`px-3 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-300 transform hover:scale-105 disabled:cursor-not-allowed ${
              selectedCategory === category.key
                ? 'bg-gradient-to-r from-primary-500 to-accent-500 text-white shadow-glow scale-105'
                : 'bg-background-secondary/50 text-foreground-muted hover:bg-background-secondary hover:text-foreground border border-border-secondary hover:border-border-accent disabled:hover:scale-100'
            }`}
          >
            <span className="hidden sm:inline">{category.label}</span>
            <span className="sm:hidden">{category.label.split(' ')[0]}</span>
          </button>
        ))}
      </div>

      {/* Rankings List with Smooth Transition */}
      <div className="relative">
        {/* Transition Overlay for smooth crossfade */}
        {isTransitioning && (
          <div className="absolute inset-0 z-20 bg-gradient-to-b from-background/70 via-background/50 to-background/70 backdrop-blur-sm transition-all duration-700 ease-out breathing-overlay" />
        )}

        {/* Content State */}
        <div className={`space-y-3 transition-all duration-800 ease-out ${
          showContent
            ? 'opacity-100 scale-100 translate-y-0 blur-0'
            : 'opacity-0 scale-98 translate-y-6 blur-sm'
        }`} style={{
          transitionDelay: showContent ? '0.1s' : '0s'
        }}>
          {rankings.map((ranking, index) => (
            <div
              key={ranking.wallet.address}
              className={`group relative p-4 rounded-xl glass-card border border-border-secondary hover:border-border-accent transition-all duration-300 hover:shadow-glow hover:scale-[1.02] hover:-translate-y-1 ${
                showContent ? 'ranking-item-smooth' : 'opacity-0'
              }`}
              style={{
                animationDelay: showContent ? `${index * 0.08}s` : '0s',
                transitionDelay: showContent ? `${index * 0.05}s` : '0s'
              }}
            >
              <div className="flex items-center gap-2 sm:gap-4">
                {/* Rank */}
                <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-background-tertiary border border-border-secondary flex-shrink-0">
                  {getRankIcon(ranking.rank)}
                </div>

                {/* Wallet Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-1 sm:gap-2 mb-1 flex-wrap">
                    <h3 className="font-semibold text-foreground truncate text-sm sm:text-base">
                      {ranking.wallet.label || truncateAddress(ranking.wallet.address)}
                    </h3>

                    {/* Wallet Type Badge */}
                    <span className={`px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium rounded-full border ${getWalletTypeColor(ranking.wallet.walletType)}`}>
                      <span className="hidden sm:inline">{ranking.wallet.walletType}</span>
                      <span className="sm:hidden">{ranking.wallet.walletType.charAt(0).toUpperCase()}</span>
                    </span>

                    {/* Risk Indicators */}
                    <div className="flex items-center gap-1">
                      {ranking.wallet.isFlagged && (
                        <FaExclamationTriangle className="text-red-400" size={12} />
                      )}
                      {ranking.wallet.isWhitelisted && (
                        <FaShieldAlt className="text-green-400" size={12} />
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 sm:gap-4 text-xs sm:text-sm text-foreground-muted flex-wrap">
                    <button
                      onClick={() => copyAddress(ranking.wallet.address)}
                      className="flex items-center gap-1 hover:text-foreground transition-colors"
                    >
                      <span className="font-mono">
                        {truncateAddress(ranking.wallet.address)}
                      </span>
                      {copiedAddress === ranking.wallet.address ? (
                        <span className="text-green-400 text-xs">✓</span>
                      ) : (
                        <FaCopy size={10} />
                      )}
                    </button>

                    <div className="flex items-center gap-1">
                      <FaEthereum size={10} />
                      <span className="hidden sm:inline">{formatEth(ranking.wallet.transactionVolume)}</span>
                      <span className="sm:hidden">{formatEth(ranking.wallet.transactionVolume).replace(' ETH', '')}</span>
                    </div>

                    <div className="flex items-center gap-1">
                      <FaChartLine size={10} />
                      <span className="hidden sm:inline">{formatNumber(ranking.wallet.transactionCount)} txs</span>
                      <span className="sm:hidden">{formatNumber(ranking.wallet.transactionCount)}</span>
                    </div>
                  </div>
                </div>

                {/* Metrics */}
                <div className="flex items-center gap-2 sm:gap-6 text-xs sm:text-sm flex-shrink-0">
                  {/* Score */}
                  <div className="text-center">
                    <div className="text-sm sm:text-lg font-bold text-foreground">
                      {ranking.score.toFixed(0)}
                    </div>
                    <div className="text-xs text-foreground-muted hidden sm:block">Score</div>
                  </div>

                  {/* Risk Score */}
                  <div className="text-center">
                    <div className={`text-sm sm:text-lg font-bold ${getRiskColor(ranking.wallet.riskScore)}`}>
                      {ranking.wallet.riskScore}
                    </div>
                    <div className="text-xs text-foreground-muted hidden sm:block">Risk</div>
                  </div>

                  {/* Change - Hidden on mobile */}
                  <div className="hidden sm:flex items-center gap-1">
                    {getChangeIcon(ranking.change)}
                    {ranking.change && (
                      <span className={`text-xs ${
                        ranking.change > 0 ? 'text-green-400' :
                        ranking.change < 0 ? 'text-red-400' : 'text-gray-400'
                      }`}>
                        {Math.abs(ranking.change)}
                      </span>
                    )}
                  </div>

                  {/* Action Button - Hidden on mobile */}
                  <button className="hidden sm:block opacity-0 group-hover:opacity-100 transition-opacity p-2 rounded-lg hover:bg-background-tertiary">
                    <FaExternalLinkAlt size={14} className="text-foreground-muted" />
                  </button>
                </div>
              </div>

              {/* Risk Flags */}
              {ranking.wallet.riskFlags.length > 0 && (
                <div className="mt-3 pt-3 border-t border-border-secondary">
                  <div className="flex flex-wrap gap-1">
                    {ranking.wallet.riskFlags.map((flag, flagIndex) => (
                      <span
                        key={flagIndex}
                        className="px-2 py-1 text-xs bg-red-500/10 text-red-400 border border-red-500/20 rounded-full"
                      >
                        {flag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Load More Button */}
        <div className={`text-center pt-4 transition-all duration-700 ${
          showContent ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`} style={{ transitionDelay: showContent ? `${rankings.length * 0.05 + 0.2}s` : '0s' }}>
          <button
            disabled={isLoading || isTransitioning}
            className="px-6 py-3 text-sm font-medium text-foreground-muted hover:text-foreground bg-background-secondary hover:bg-background-tertiary border border-border-secondary hover:border-border-accent rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Load More Rankings
          </button>
        </div>
      </div>
    </div>
  );
};

export default TopWalletRankings;
