import React, { useState, useEffect } from 'react';
import {
  FaWallet,
  FaShieldAlt,
  FaExclamationTriangle,
  FaChartLine,
  FaNetworkWired,
  FaEthereum,
  FaClock,
  FaUsers,
  FaExchangeAlt,
  FaRobot,
  FaTools,
  FaWater,
  FaLink
} from 'react-icons/fa';
import { walletRankingService } from '@/services/walletRankingService';
import { getWatchListService } from '@/services/watchListService';
import { networkService, NetworkStats } from '@/services/networkService';

interface StatisticalDashboardProps {
  className?: string;
  selectedNetwork?: string;
}

interface StatCard {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  color: string;
  description?: string;
}

interface ChartData {
  label: string;
  value: number;
  color: string;
  percentage: number;
}

const StatisticalDashboard: React.FC<StatisticalDashboardProps> = ({
  className = '',
  selectedNetwork = 'ethereum'
}) => {
  const [stats, setStats] = useState<any>(null);
  const [networkStats, setNetworkStats] = useState<NetworkStats | null>(null);
  const [watchListStats, setWatchListStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadStatistics();

    // Subscribe to real-time updates
    const unsubscribe = walletRankingService.subscribe((updatedStats) => {
      setStats(updatedStats);
    });

    // Subscribe to watch list updates
    let watchListUnsubscribe: (() => void) | null = null;
    try {
      if (typeof window !== 'undefined') {
        const watchService = getWatchListService();
        watchListUnsubscribe = watchService.subscribe(() => {
          setWatchListStats(watchService.getStats());
        });
      }
    } catch (error) {
      console.error('Failed to subscribe to watch list updates:', error);
    }

    return () => {
      unsubscribe();
      if (watchListUnsubscribe) {
        watchListUnsubscribe();
      }
    };
  }, [selectedNetwork]);

  const loadStatistics = () => {
    setIsLoading(true);

    // Simulate loading delay
    setTimeout(() => {
      // Get network-specific stats if available
      const networkSpecificStats = networkService.getNetworkStats(selectedNetwork);
      if (networkSpecificStats) {
        setNetworkStats(networkSpecificStats);
        setStats(networkSpecificStats);
      } else {
        // Fallback to general ranking stats
        const rankingStats = walletRankingService.getStatistics();
        setStats(rankingStats);
      }

      // Get watch list stats if available
      try {
        if (typeof window !== 'undefined') {
          const watchService = getWatchListService();
          setWatchListStats(watchService.getStats());
        }
      } catch (error) {
        console.error('Failed to load watch list stats:', error);
      }

      setIsLoading(false);
    }, 800);
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toLocaleString();
  };

  const formatEth = (eth: number): string => {
    if (eth >= 1000000) return `${(eth / 1000000).toFixed(1)}M`;
    if (eth >= 1000) return `${(eth / 1000).toFixed(1)}K`;
    return eth.toFixed(0);
  };

  if (isLoading || !stats) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Stats Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="p-6 bg-background-secondary/30 rounded-xl animate-pulse">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-background-tertiary rounded-xl" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-background-tertiary rounded w-3/4" />
                  <div className="h-6 bg-background-tertiary rounded w-1/2" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Charts Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="p-6 bg-background-secondary/30 rounded-xl animate-pulse">
            <div className="h-4 bg-background-tertiary rounded w-1/3 mb-4" />
            <div className="h-48 bg-background-tertiary rounded" />
          </div>
          <div className="p-6 bg-background-secondary/30 rounded-xl animate-pulse">
            <div className="h-4 bg-background-tertiary rounded w-1/3 mb-4" />
            <div className="h-48 bg-background-tertiary rounded" />
          </div>
        </div>
      </div>
    );
  }

  const mainStats: StatCard[] = [
    {
      title: 'Total Wallets',
      value: formatNumber(stats.totalWallets),
      change: 12,
      icon: <FaWallet />,
      color: 'from-blue-500 to-blue-600',
      description: 'Analyzed wallet addresses'
    },
    {
      title: 'Total Volume',
      value: `${formatEth(stats.totalVolume)} ETH`,
      change: 8,
      icon: <FaEthereum />,
      color: 'from-green-500 to-green-600',
      description: 'Combined transaction volume'
    },
    {
      title: 'Flagged Wallets',
      value: formatNumber(stats.flaggedWallets),
      change: -3,
      icon: <FaExclamationTriangle />,
      color: 'from-red-500 to-red-600',
      description: 'High-risk addresses'
    },
    {
      title: 'Quality Score',
      value: `${stats.averageQualityScore.toFixed(1)}/100`,
      change: 2,
      icon: <FaShieldAlt />,
      color: 'from-purple-500 to-purple-600',
      description: 'Average wallet quality'
    }
  ];

  // Prepare wallet type distribution data
  const walletTypeData: ChartData[] = [
    {
      label: 'Regular',
      value: stats.walletTypes.regular,
      color: '#2563EB',
      percentage: (stats.walletTypes.regular / stats.totalWallets) * 100
    },
    {
      label: 'Exchange',
      value: stats.walletTypes.exchange,
      color: '#10B981',
      percentage: (stats.walletTypes.exchange / stats.totalWallets) * 100
    },
    {
      label: 'Contract',
      value: stats.walletTypes.contract,
      color: '#8B5CF6',
      percentage: (stats.walletTypes.contract / stats.totalWallets) * 100
    },
    {
      label: 'Whale',
      value: stats.walletTypes.whale,
      color: '#06B6D4',
      percentage: (stats.walletTypes.whale / stats.totalWallets) * 100
    },
    {
      label: 'DeFi',
      value: stats.walletTypes.defi,
      color: '#EC4899',
      percentage: (stats.walletTypes.defi / stats.totalWallets) * 100
    },
    {
      label: 'Bridge',
      value: stats.walletTypes.bridge,
      color: '#F59E0B',
      percentage: (stats.walletTypes.bridge / stats.totalWallets) * 100
    },
    {
      label: 'Miner',
      value: stats.walletTypes.miner,
      color: '#EF4444',
      percentage: (stats.walletTypes.miner / stats.totalWallets) * 100
    }
  ].filter(item => item.value > 0);

  // Prepare risk distribution data
  const riskData: ChartData[] = [
    {
      label: 'Low Risk',
      value: stats.riskDistribution.low,
      color: '#10B981',
      percentage: (stats.riskDistribution.low / stats.totalWallets) * 100
    },
    {
      label: 'Medium Risk',
      value: stats.riskDistribution.medium,
      color: '#F59E0B',
      percentage: (stats.riskDistribution.medium / stats.totalWallets) * 100
    },
    {
      label: 'High Risk',
      value: stats.riskDistribution.high,
      color: '#F97316',
      percentage: (stats.riskDistribution.high / stats.totalWallets) * 100
    },
    {
      label: 'Critical Risk',
      value: stats.riskDistribution.critical,
      color: '#EF4444',
      percentage: (stats.riskDistribution.critical / stats.totalWallets) * 100
    }
  ];

  const getWalletTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'regular': return <FaWallet />;
      case 'exchange': return <FaExchangeAlt />;
      case 'contract': return <FaRobot />;
      case 'whale': return <FaWater />;
      case 'defi': return <FaNetworkWired />;
      case 'bridge': return <FaLink />;
      case 'miner': return <FaTools />;
      default: return <FaWallet />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        {mainStats.map((stat, index) => (
          <div
            key={index}
            className="relative p-4 sm:p-6 rounded-xl glass-card border border-border-secondary hover:border-border-accent transition-all duration-300 group metric-card"
          >
            <div className="flex items-center gap-3 sm:gap-4">
              <div className={`p-2 sm:p-3 rounded-xl bg-gradient-to-br ${stat.color} shadow-lg flex-shrink-0`}>
                <div className="text-white text-lg sm:text-xl">
                  {stat.icon}
                </div>
              </div>

              <div className="flex-1 min-w-0">
                <p className="text-xs sm:text-sm text-foreground-muted mb-1 truncate">{stat.title}</p>
                <p className="text-xl sm:text-2xl font-bold text-foreground truncate">{stat.value}</p>

                {stat.change && (
                  <div className={`flex items-center gap-1 text-xs mt-1 ${
                    stat.change > 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    <span>{stat.change > 0 ? '+' : ''}{stat.change}%</span>
                    <span className="text-foreground-muted hidden sm:inline">vs last week</span>
                  </div>
                )}
              </div>
            </div>

            {stat.description && (
              <p className="text-xs text-foreground-muted mt-3 opacity-0 group-hover:opacity-100 transition-opacity hidden sm:block">
                {stat.description}
              </p>
            )}
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Wallet Type Distribution */}
        <div className="p-4 sm:p-6 rounded-xl glass-card border border-border-secondary">
          <div className="flex items-center gap-2 mb-4 sm:mb-6">
            <FaUsers className="text-primary-400 text-sm sm:text-base" />
            <h3 className="text-base sm:text-lg font-semibold text-foreground">Wallet Type Distribution</h3>
          </div>

          <div className="space-y-2 sm:space-y-3">
            {walletTypeData.map((item, index) => (
              <div key={index} className="flex items-center gap-2 sm:gap-3">
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <div className="text-foreground-muted text-sm">
                    {getWalletTypeIcon(item.label)}
                  </div>
                  <span className="text-xs sm:text-sm text-foreground-muted truncate">{item.label}</span>
                </div>

                <div className="flex items-center gap-2 sm:gap-3 flex-1">
                  <div className="flex-1 h-2 bg-background-tertiary rounded-full overflow-hidden">
                    <div
                      className="h-full rounded-full transition-all duration-500 chart-bar"
                      style={{
                        backgroundColor: item.color,
                        '--target-width': `${item.percentage}%`
                      } as any}
                    />
                  </div>
                  <span className="text-xs sm:text-sm font-medium text-foreground w-8 sm:w-12 text-right">
                    {item.value}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Risk Distribution */}
        <div className="p-4 sm:p-6 rounded-xl glass-card border border-border-secondary">
          <div className="flex items-center gap-2 mb-4 sm:mb-6">
            <FaShieldAlt className="text-accent-400 text-sm sm:text-base" />
            <h3 className="text-base sm:text-lg font-semibold text-foreground">Risk Distribution</h3>
          </div>

          <div className="space-y-2 sm:space-y-3">
            {riskData.map((item, index) => (
              <div key={index} className="flex items-center gap-2 sm:gap-3">
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <div
                    className="w-3 h-3 rounded-full flex-shrink-0"
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-xs sm:text-sm text-foreground-muted truncate">{item.label}</span>
                </div>

                <div className="flex items-center gap-2 sm:gap-3 flex-1">
                  <div className="flex-1 h-2 bg-background-tertiary rounded-full overflow-hidden">
                    <div
                      className="h-full rounded-full transition-all duration-500 chart-bar"
                      style={{
                        backgroundColor: item.color,
                        '--target-width': `${item.percentage}%`
                      } as any}
                    />
                  </div>
                  <span className="text-xs sm:text-sm font-medium text-foreground w-8 sm:w-12 text-right">
                    {item.value}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 rounded-xl glass-card border border-border-secondary text-center">
          <div className="text-2xl font-bold text-foreground mb-1">
            {formatNumber(stats.totalTransactions)}
          </div>
          <div className="text-sm text-foreground-muted">Total Transactions</div>
        </div>

        <div className="p-4 rounded-xl glass-card border border-border-secondary text-center">
          <div className="text-2xl font-bold text-green-400 mb-1">
            {stats.whitelistedWallets}
          </div>
          <div className="text-sm text-foreground-muted">Whitelisted Wallets</div>
        </div>

        <div className="p-4 rounded-xl glass-card border border-border-secondary text-center">
          <div className="text-2xl font-bold text-foreground-muted mb-1">
            <FaClock className="inline mr-2 animate-pulse text-green-400" />
            <span className="text-green-400">Live</span>
          </div>
          <div className="text-sm text-foreground-muted">
            Updated {new Date(stats.lastUpdate).toLocaleTimeString()}
          </div>
          <div className="text-xs text-green-400 mt-1">
            Real-time updates active
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatisticalDashboard;
