import React, { useState, useEffect, useRef } from 'react';
import {
  FaArrowRight, FaArrowLeft, FaExchangeAlt, FaFilter,
  FaClock, FaCoins, FaChartArea, FaDownload, FaPlay, FaPause, FaArrowCircleUp,
  FaExclamationTriangle
} from 'react-icons/fa';
import Tooltip from './Tooltip';

interface Transaction {
  id: string;
  from: string;
  to: string;
  value: number;
  timestamp: string;
  type: 'in' | 'out' | 'internal';
  risk: 'low' | 'medium' | 'high';
  token?: string;
  gasUsed: number;
  method?: string;
}

interface WalletTransactionFlowProps {
  walletAddress: string;
  onTransactionSelect?: (transaction: Transaction) => void;
}

const WalletTransactionFlow: React.FC<WalletTransactionFlowProps> = ({
  walletAddress,
  onTransactionSelect
}) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<'all' | 'in' | 'out' | 'high-risk'>('all');
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d' | 'all'>('7d');
  const [isRealTime, setIsRealTime] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);
  const transactionListRef = useRef<HTMLDivElement>(null);
  const modalContentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchTransactions();
  }, [walletAddress, filter, timeRange]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRealTime) {
      interval = setInterval(fetchTransactions, 5000); // Update every 5 seconds
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRealTime]);

  // Enhanced scroll event handler for scroll-to-top button
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          if (transactionListRef.current) {
            const scrollTop = transactionListRef.current.scrollTop;
            const shouldShow = scrollTop > 200;

            setScrollPosition(scrollTop);
            setShowScrollToTop(prev => prev !== shouldShow ? shouldShow : prev);
          }
          ticking = false;
        });
        ticking = true;
      }
    };

    const transactionList = transactionListRef.current;
    if (transactionList) {
      transactionList.addEventListener('scroll', handleScroll, { passive: true });
      handleScroll();

      return () => {
        transactionList.removeEventListener('scroll', handleScroll);
      };
    }
  }, [transactions]);

  // Restore scroll position when switching filters
  useEffect(() => {
    if (transactionListRef.current && scrollPosition > 0) {
      transactionListRef.current.scrollTop = scrollPosition;
    }
  }, [filter, timeRange]);

  // Handle scroll indicators for modal content
  useEffect(() => {
    const handleModalScroll = () => {
      if (modalContentRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = modalContentRef.current;
        const hasScrollTop = scrollTop > 10;
        const hasScrollBottom = scrollTop < scrollHeight - clientHeight - 10;

        modalContentRef.current.classList.toggle('has-scroll-top', hasScrollTop);
        modalContentRef.current.classList.toggle('has-scroll-bottom', hasScrollBottom);
      }
    };

    const modalContent = modalContentRef.current;
    if (modalContent) {
      modalContent.addEventListener('scroll', handleModalScroll);
      handleModalScroll();

      return () => modalContent.removeEventListener('scroll', handleModalScroll);
    }
  }, [selectedTransaction]);

  // Smooth scroll to selected transaction
  const scrollToTransaction = (transactionId: string) => {
    if (transactionListRef.current) {
      const transactionElement = transactionListRef.current.querySelector(`[data-transaction-id="${transactionId}"]`);
      if (transactionElement) {
        transactionElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }
  };

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate mock transaction data
      const mockTransactions: Transaction[] = Array.from({ length: 25 }, (_, i) => ({
        id: `tx_${i}`,
        from: i % 2 === 0 ? walletAddress : `0x${Math.random().toString(16).substr(2, 40)}`,
        to: i % 2 === 1 ? walletAddress : `0x${Math.random().toString(16).substr(2, 40)}`,
        value: Math.random() * 10,
        timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        type: i % 2 === 0 ? 'in' : 'out',
        risk: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
        token: Math.random() > 0.5 ? 'ETH' : 'USDC',
        gasUsed: Math.floor(Math.random() * 100000),
        method: ['Transfer', 'Swap', 'Deposit', 'Withdraw'][Math.floor(Math.random() * 4)]
      }));

      setTransactions(mockTransactions);
    } catch (error) {
      console.error('Error fetching transactions:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredTransactions = transactions.filter(tx => {
    if (filter === 'all') return true;
    if (filter === 'in') return tx.type === 'in';
    if (filter === 'out') return tx.type === 'out';
    if (filter === 'high-risk') return tx.risk === 'high';
    return true;
  });

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'text-red-400 bg-red-500/20';
      case 'medium': return 'text-orange-400 bg-orange-500/20';
      default: return 'text-green-400 bg-green-500/20';
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'in': return <FaArrowLeft className="text-green-400" />;
      case 'out': return <FaArrowRight className="text-red-400" />;
      default: return <FaExchangeAlt className="text-blue-400" />;
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const exportTransactions = () => {
    const csvContent = [
      ['Hash', 'From', 'To', 'Value', 'Token', 'Type', 'Risk', 'Method', 'Gas Used', 'Timestamp'],
      ...filteredTransactions.map(tx => [
        tx.id,
        tx.from,
        tx.to,
        tx.value.toString(),
        tx.token,
        tx.type,
        tx.risk,
        tx.method || 'Transfer',
        tx.gasUsed.toString(),
        tx.timestamp
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transactions-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="relative flex flex-col h-full">
      {/* Header - Optimized for wider layout */}
      <div className="flex items-center justify-between p-4 border-b border-border-accent bg-background-secondary/30">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gradient-brand">
            <FaChartArea className="text-sm text-white" />
          </div>
          <div>
            <h3 className="font-bold text-foreground">Transaction Flow Analysis</h3>
            <p className="text-xs text-foreground-muted">
              Real-time transaction monitoring and analysis
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Tooltip
            content={isRealTime ? "Pause real-time monitoring" : "Start real-time monitoring"}
            contentVi={isRealTime ? "Tạm dừng giám sát thời gian thực" : "Bắt đầu giám sát thời gian thực"}
            showShortcut="Space"
          >
            <button
              onClick={() => setIsRealTime(!isRealTime)}
              className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                isRealTime
                  ? 'bg-green-500/20 text-green-400 shadow-green-500/20'
                  : 'bg-background-tertiary text-foreground-muted hover:text-foreground'
              }`}
            >
              {isRealTime ? <FaPause size={14} /> : <FaPlay size={14} />}
            </button>
          </Tooltip>
          <Tooltip
            content="Export transaction data to CSV file"
            contentVi="Xuất dữ liệu giao dịch ra file CSV"
            showShortcut="Ctrl+E"
          >
            <button
              onClick={exportTransactions}
              className="p-2 transition-all duration-200 rounded-lg bg-background-tertiary hover:bg-background-secondary hover:scale-110"
            >
              <FaDownload size={14} />
            </button>
          </Tooltip>
        </div>
      </div>

      {/* Filters - Enhanced for wider layout */}
      <div className="p-4 space-y-3">
        <div className="grid grid-cols-2 gap-3">
          <div className="flex p-1 rounded-lg bg-background-tertiary">
            {['all', 'in', 'out', 'risk'].map(filterType => (
              <button
                key={filterType}
                onClick={() => setFilter(filterType === 'risk' ? 'high-risk' : filterType as any)}
                className={`flex-1 px-3 py-2 text-xs font-medium rounded-md transition-colors ${
                  (filter === filterType || (filterType === 'risk' && filter === 'high-risk'))
                    ? 'bg-accent-500 text-white'
                    : 'text-foreground-muted hover:text-foreground'
                }`}
              >
                {filterType.toUpperCase()}
              </button>
            ))}
          </div>

          <div className="flex p-1 rounded-lg bg-background-tertiary">
            {['24h', '7d', '30d', 'all'].map(range => (
              <button
                key={range}
                onClick={() => setTimeRange(range as any)}
                className={`flex-1 px-3 py-2 text-xs font-medium rounded-md transition-colors ${
                  timeRange === range
                    ? 'bg-accent-500 text-white'
                    : 'text-foreground-muted hover:text-foreground'
                }`}
              >
                {range.toUpperCase()}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Transaction List - Enhanced for wider layout */}
      <div className="relative flex-1 px-4 overflow-y-auto wallet-panel-container transaction-flow-list scrollable-container"
           ref={transactionListRef}
           style={{
             scrollBehavior: 'smooth',
             WebkitOverflowScrolling: 'touch',
             overscrollBehavior: 'contain',
             position: 'relative',
             isolation: 'isolate'
           }}>

        <div className="pb-4 space-y-3">
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="w-6 h-6 border-2 rounded-full animate-spin border-accent-400 border-t-transparent"></div>
          </div>
        ) : (
          filteredTransactions.map(tx => (
            <div
              key={tx.id}
              data-transaction-id={tx.id}
              onClick={() => {
                setSelectedTransaction(tx);
                onTransactionSelect?.(tx);
                scrollToTransaction(tx.id);
              }}
              className={`glass-card p-4 rounded-lg hover:bg-background-tertiary/50 cursor-pointer transition-colors transaction-item ${
                selectedTransaction?.id === tx.id ? 'selected' : ''
              }`}
              style={{
                scrollMarginTop: '0.75rem',
                scrollMarginBottom: '0.75rem'
              }}
            >
              {/* Enhanced layout for wider space */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-background-tertiary">
                    {getTransactionIcon(tx.type)}
                  </div>
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium text-foreground">
                        {tx.method || 'Transfer'}
                      </span>
                      <span className={`px-2 py-0.5 rounded-full text-xs ${getRiskColor(tx.risk)}`}>
                        {tx.risk.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-xs text-foreground-muted">
                      {formatAddress(tx.from)} → {formatAddress(tx.to)}
                    </div>
                  </div>
                </div>

                <div className="text-right">
                  <div className="text-sm font-bold text-foreground mb-1">
                    {tx.value.toFixed(4)} {tx.token}
                  </div>
                  <div className="text-xs text-foreground-muted">
                    {formatTime(tx.timestamp)}
                  </div>
                </div>
              </div>

              {/* Additional info for wider layout */}
              <div className="flex items-center justify-between text-xs text-foreground-muted pt-2 border-t border-border-accent/30">
                <span>Gas: {tx.gasUsed.toLocaleString()}</span>
                <span className={`px-2 py-1 rounded ${
                  tx.type === 'in' ? 'bg-green-500/10 text-green-400' :
                  tx.type === 'out' ? 'bg-red-500/10 text-red-400' :
                  'bg-blue-500/10 text-blue-400'
                }`}>
                  {tx.type.toUpperCase()}
                </span>
              </div>
            </div>
          ))
        )}
        </div>
      </div>

      {/* Fixed Scroll to Top Button */}
      <button
        onClick={() => {
          transactionListRef.current?.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        }}
        className={`absolute bottom-4 right-4 z-50 w-11 h-11 bg-gradient-to-br from-accent-500 to-accent-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center ${showScrollToTop ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'}`}
        style={{
          backdropFilter: 'blur(12px)',
          border: '1px solid rgba(139, 92, 246, 0.3)'
        }}
        aria-label="Scroll to top of transaction flow"
        aria-hidden={!showScrollToTop}
        tabIndex={showScrollToTop ? 0 : -1}
        title="Scroll to top"
      >
        <FaArrowCircleUp size={16} />
      </button>

      {/* Transaction Details Modal */}
      {selectedTransaction && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[9100] p-4">
          <div className="glass-card rounded-xl p-4 sm:p-6 max-w-lg w-full max-h-[90vh] flex flex-col"
               style={{
                 minHeight: '400px'
               }}>
            {/* Fixed Header */}
            <div className="flex items-center justify-between flex-shrink-0 mb-4">
              <h3 className="font-bold text-foreground">Transaction Details</h3>
              <button
                onClick={() => setSelectedTransaction(null)}
                className="p-1 rounded hover:bg-background-tertiary"
              >
                ×
              </button>
            </div>

            {/* Scrollable Content Area */}
            <div className="flex-1 overflow-hidden">
              <div className="h-full modal-content-scrollable" ref={modalContentRef} tabIndex={0} role="region" aria-label="Transaction details content">
                <div className="p-2 space-y-4">
                  {/* Transaction Summary */}
                  <div className={`p-3 rounded-lg border ${getRiskColor(selectedTransaction.risk)} bg-gradient-to-r from-transparent to-background-tertiary/20`}>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getTransactionIcon(selectedTransaction.type)}
                        <span className="text-sm font-semibold">
                          {selectedTransaction.method || 'Transfer'}
                        </span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs ${getRiskColor(selectedTransaction.risk)}`}>
                        {selectedTransaction.risk.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-lg font-bold text-foreground">
                      {selectedTransaction.value.toFixed(4)} {selectedTransaction.token}
                    </div>
                  </div>

                  {/* Transaction Details */}
                  <div className="modal-section-scrollable" tabIndex={0} role="region" aria-label="Transaction details">
                    <div className="space-y-3 text-sm">
                      <div className="flex items-start justify-between">
                        <span className="flex-shrink-0 font-medium text-foreground-muted">Hash:</span>
                        <div className="flex-1 ml-2 text-right">
                          <div className="p-2 font-mono text-xs break-all rounded text-foreground bg-background-tertiary">
                            {selectedTransaction.id}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start justify-between">
                        <span className="flex-shrink-0 font-medium text-foreground-muted">From:</span>
                        <div className="flex-1 ml-2 text-right">
                          <div className="p-2 font-mono text-xs break-all rounded text-foreground bg-background-tertiary">
                            {selectedTransaction.from}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start justify-between">
                        <span className="flex-shrink-0 font-medium text-foreground-muted">To:</span>
                        <div className="flex-1 ml-2 text-right">
                          <div className="p-2 font-mono text-xs break-all rounded text-foreground bg-background-tertiary">
                            {selectedTransaction.to}
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div className="p-2 rounded bg-background-tertiary">
                          <span className="block text-xs text-foreground-muted">Gas Used</span>
                          <span className="font-semibold text-foreground">
                            {selectedTransaction.gasUsed.toLocaleString()}
                          </span>
                        </div>
                        <div className="p-2 rounded bg-background-tertiary">
                          <span className="block text-xs text-foreground-muted">Timestamp</span>
                          <span className="text-xs font-semibold text-foreground">
                            {formatTime(selectedTransaction.timestamp)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Risk Analysis Section */}
                  {selectedTransaction.risk === 'high' && (
                    <div className="p-3 border rounded-lg bg-red-500/10 border-red-500/20">
                      <div className="flex items-center gap-2 mb-2">
                        <FaExclamationTriangle className="text-red-400" size={14} />
                        <span className="font-semibold text-red-400">High Risk Transaction</span>
                      </div>
                      <div className="modal-section-scrollable" tabIndex={0} role="region" aria-label="Risk analysis details">
                        <p className="text-sm leading-relaxed text-foreground-muted">
                          This transaction has been flagged as high risk. Possible reasons include:
                        </p>
                        <ul className="mt-2 space-y-1 text-sm text-foreground-muted">
                          <li>• Interaction with flagged addresses</li>
                          <li>• Unusual transaction patterns</li>
                          <li>• High gas usage or complex operations</li>
                          <li>• Connection to known suspicious activities</li>
                        </ul>
                      </div>
                    </div>
                  )}

                  {/* Additional Transaction Information */}
                  <div className="p-3 rounded-lg bg-background-secondary/30">
                    <h4 className="mb-2 text-sm font-semibold text-foreground">Transaction Flow</h4>
                    <div className="modal-section-scrollable" tabIndex={0} role="region" aria-label="Transaction flow visualization">
                      <div className="flex items-center justify-between text-xs">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <span className="text-foreground-muted">Sender</span>
                        </div>
                        <div className="flex-1 mx-3 border-t border-dashed border-foreground-muted/30"></div>
                        <div className="flex items-center gap-2">
                          <span className="text-foreground-muted">Receiver</span>
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        </div>
                      </div>
                      <div className="mt-2 text-xs text-center text-foreground-muted">
                        {selectedTransaction.type === 'in' ? 'Incoming Transaction' : 'Outgoing Transaction'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Fixed Footer */}
            <div className="flex flex-shrink-0 gap-2 pt-4 mt-4 border-t border-border">
              <button className="flex-1 py-2 text-xs btn-primary">
                View on Explorer
              </button>
              <button className="flex-1 py-2 text-xs btn-secondary">
                Add to Watch
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Summary Stats - Enhanced for wider layout */}
      <div className="p-4 border-t border-border-accent bg-background-secondary/30">
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-green-400">
              {filteredTransactions.filter(tx => tx.type === 'in').length}
            </div>
            <div className="text-xs text-foreground-muted">Incoming</div>
          </div>
          <div>
            <div className="text-lg font-bold text-red-400">
              {filteredTransactions.filter(tx => tx.type === 'out').length}
            </div>
            <div className="text-xs text-foreground-muted">Outgoing</div>
          </div>
          <div>
            <div className="text-lg font-bold text-orange-400">
              {filteredTransactions.filter(tx => tx.risk === 'high').length}
            </div>
            <div className="text-xs text-foreground-muted">High Risk</div>
          </div>
          <div>
            <div className="text-lg font-bold text-blue-400">
              {filteredTransactions.length}
            </div>
            <div className="text-xs text-foreground-muted">Total</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WalletTransactionFlow;
