import React, { useState, useEffect, useRef } from 'react';
import {
  FaFilter, FaShieldAlt, FaExclamationTriangle, FaEye, FaEyeSlash,
  FaCheckCircle, FaTimes, FaInfoCircle, FaDownload,
  FaRobot, FaFish, FaMoneyBillWave, FaBan, FaArrowUp, FaCog
} from 'react-icons/fa';

interface RiskFilterSettings {
  minRiskLevel: 'low' | 'medium' | 'high' | 'critical';
  showOnlyFlagged: boolean;
  hideWhitelisted: boolean;
  highlightSuspicious: boolean;
  autoHideClean: boolean;
  riskThreshold: number; // 0-100 scale
  filterTypes: {
    phishing: boolean;
    mev: boolean;
    laundering: boolean;
    sanctions: boolean;
    scam: boolean;
    suspicious: boolean;
  };
}

interface RiskFilterPanelProps {
  settings: RiskFilterSettings;
  onSettingsChange: (settings: RiskFilterSettings) => void;
  onApplyFilter: (settings: RiskFilterSettings) => void;
  onResetFilter: () => void;
  totalWallets: number;
  filteredWallets: number;
  flaggedWallets: number;
}

const RiskFilterPanel: React.FC<RiskFilterPanelProps> = ({
  settings,
  onSettingsChange,
  onApplyFilter,
  onResetFilter,
  totalWallets,
  filteredWallets,
  flaggedWallets
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const panelRef = useRef<HTMLDivElement>(null);

  // Scroll handling
  useEffect(() => {
    const handleScroll = () => {
      if (panelRef.current) {
        const scrollTop = panelRef.current.scrollTop;
        setShowScrollToTop(scrollTop > 200);
      }
    };

    const panel = panelRef.current;
    if (panel) {
      panel.addEventListener('scroll', handleScroll);
      return () => panel.removeEventListener('scroll', handleScroll);
    }
  }, []);

  const scrollToTop = () => {
    panelRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const updateSettings = (updates: Partial<RiskFilterSettings>) => {
    const newSettings = { ...settings, ...updates };
    onSettingsChange(newSettings);
  };

  const updateFilterTypes = (type: keyof RiskFilterSettings['filterTypes'], enabled: boolean) => {
    updateSettings({
      filterTypes: {
        ...settings.filterTypes,
        [type]: enabled
      }
    });
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'critical':
        return 'text-white bg-gradient-to-r from-red-500 to-pink-500 border-red-400 shadow-lg shadow-red-500/30';
      case 'high':
        return 'text-white bg-gradient-to-r from-orange-500 to-amber-500 border-orange-400 shadow-lg shadow-orange-500/30';
      case 'medium':
        return 'text-slate-900 bg-gradient-to-r from-yellow-400 to-amber-400 border-yellow-400 shadow-lg shadow-yellow-500/30';
      default:
        return 'text-white bg-gradient-to-r from-green-500 to-emerald-500 border-green-400 shadow-lg shadow-green-500/30';
    }
  };

  const getFilterTypeIcon = (type: string) => {
    switch (type) {
      case 'phishing': return <FaFish className="w-4 h-4" />;
      case 'mev': return <FaRobot className="w-4 h-4" />;
      case 'laundering': return <FaMoneyBillWave className="w-4 h-4" />;
      case 'sanctions': return <FaBan className="w-4 h-4" />;
      case 'scam': return <FaExclamationTriangle className="w-4 h-4" />;
      default: return <FaShieldAlt className="w-4 h-4" />;
    }
  };

  const getFilterTypeLabel = (type: string) => {
    switch (type) {
      case 'phishing': return 'Phishing';
      case 'mev': return 'MEV/Bot';
      case 'laundering': return 'Money Laundering';
      case 'sanctions': return 'Sanctions';
      case 'scam': return 'Scam/Fraud';
      case 'suspicious': return 'Suspicious Activity';
      default: return type;
    }
  };

  const getFilterTypeDescription = (type: string) => {
    switch (type) {
      case 'phishing': return 'Wallets involved in phishing attacks or fake websites';
      case 'mev': return 'MEV bots and automated trading systems';
      case 'laundering': return 'Potential money laundering activities';
      case 'sanctions': return 'Sanctioned addresses and entities';
      case 'scam': return 'Scam projects and fraudulent activities';
      case 'suspicious': return 'General suspicious behavioral patterns';
      default: return 'Unknown filter type';
    }
  };

  return (
    <div className="overflow-hidden border bg-slate-800 rounded-xl border-slate-600">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-600">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-yellow-500/20">
            <FaFilter className="w-4 h-4 text-yellow-400" />
          </div>
          <div>
            <h3 className="font-semibold text-white">Risk Filter</h3>
            <p className="text-xs text-gray-400">
              {filteredWallets} of {totalWallets} wallets shown
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`p-2 rounded-lg transition-colors ${
              showAdvanced ? 'bg-blue-500/20 text-blue-400' : 'hover:bg-slate-700'
            }`}
            title="Advanced filter settings"
          >
            <FaCog className="w-4 h-4" />
          </button>

          <button
            onClick={onResetFilter}
            className="p-2 text-white transition-colors rounded-lg hover:bg-slate-700"
            title="Reset all filters"
          >
            <FaTimes className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div
        ref={panelRef}
        className="overflow-y-auto"
        style={{ maxHeight: 'calc(70vh - 120px)' }}
      >
        <div className="p-4 space-y-4">
          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-2">
            <div className="p-2 text-center rounded-lg bg-slate-700/50">
              <div className="text-lg font-bold text-white">{totalWallets}</div>
              <div className="text-xs text-gray-400">Total</div>
            </div>
            <div className="p-2 text-center rounded-lg bg-slate-700/50">
              <div className="text-lg font-bold text-yellow-400">{filteredWallets}</div>
              <div className="text-xs text-gray-400">Filtered</div>
            </div>
            <div className="p-2 text-center rounded-lg bg-slate-700/50">
              <div className="text-lg font-bold text-red-400">{flaggedWallets}</div>
              <div className="text-xs text-gray-400">Flagged</div>
            </div>
          </div>

          {/* Risk Level Filter */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">Minimum Risk Level</label>
            <div className="grid grid-cols-2 gap-2">
              {(['low', 'medium', 'high', 'critical'] as const).map(level => (
                <button
                  key={level}
                  onClick={() => updateSettings({ minRiskLevel: level })}
                  className={`p-3 rounded-xl border text-sm font-semibold transition-all duration-300 transform hover:scale-105 ${
                    settings.minRiskLevel === level
                      ? getRiskLevelColor(level)
                      : 'border-slate-600 hover:border-slate-500 hover:bg-slate-700/50 text-slate-300 hover:text-white shadow-md'
                  }`}
                >
                  {level.toUpperCase()}
                </button>
              ))}
            </div>
          </div>

          {/* Risk Threshold Slider */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-white">Risk Threshold</label>
              <span className={`text-sm font-bold px-3 py-1 rounded-lg ${
                settings.riskThreshold <= 25 ? 'text-green-400 bg-green-500/20' :
                settings.riskThreshold <= 50 ? 'text-yellow-400 bg-yellow-500/20' :
                settings.riskThreshold <= 75 ? 'text-orange-400 bg-orange-500/20' :
                'text-red-400 bg-red-500/20'
              }`}>
                {settings.riskThreshold}%
              </span>
            </div>

            {/* Custom Slider Container */}
            <div className="relative">
              <input
                type="range"
                min="0"
                max="100"
                value={settings.riskThreshold}
                onChange={(e) => updateSettings({ riskThreshold: parseInt(e.target.value) })}
                className={`w-full h-3 rounded-lg appearance-none cursor-pointer bg-slate-600 focus:outline-none focus:ring-2 ${
                  settings.riskThreshold <= 25 ? 'focus:ring-green-500/50' :
                  settings.riskThreshold <= 50 ? 'focus:ring-yellow-500/50' :
                  settings.riskThreshold <= 75 ? 'focus:ring-orange-500/50' :
                  'focus:ring-red-500/50'
                }`}
                style={{
                  background: `linear-gradient(to right,
                    ${settings.riskThreshold <= 25 ? '#10b981' :
                      settings.riskThreshold <= 50 ? '#eab308' :
                      settings.riskThreshold <= 75 ? '#f97316' : '#ef4444'} 0%,
                    ${settings.riskThreshold <= 25 ? '#10b981' :
                      settings.riskThreshold <= 50 ? '#eab308' :
                      settings.riskThreshold <= 75 ? '#f97316' : '#ef4444'} ${settings.riskThreshold}%,
                    #475569 ${settings.riskThreshold}%,
                    #475569 100%)`
                }}
              />

              {/* Custom Thumb Styling with Dynamic Colors */}
              <style jsx>{`
                input[type="range"]::-webkit-slider-thumb {
                  appearance: none;
                  width: 22px;
                  height: 22px;
                  border-radius: 50%;
                  background: ${
                    settings.riskThreshold <= 25
                      ? 'linear-gradient(135deg, #34d399 0%, #10b981 100%)' :
                    settings.riskThreshold <= 50
                      ? 'linear-gradient(135deg, #fbbf24 0%, #eab308 100%)' :
                    settings.riskThreshold <= 75
                      ? 'linear-gradient(135deg, #fb923c 0%, #f97316 100%)' :
                      'linear-gradient(135deg, #f87171 0%, #ef4444 100%)'
                  };
                  border: 3px solid ${
                    settings.riskThreshold <= 25 ? '#34d399' :
                    settings.riskThreshold <= 50 ? '#fbbf24' :
                    settings.riskThreshold <= 75 ? '#fb923c' : '#f87171'
                  };
                  cursor: pointer;
                  box-shadow: 0 3px 10px ${
                    settings.riskThreshold <= 25 ? 'rgba(52, 211, 153, 0.4)' :
                    settings.riskThreshold <= 50 ? 'rgba(251, 191, 36, 0.4)' :
                    settings.riskThreshold <= 75 ? 'rgba(251, 146, 60, 0.4)' : 'rgba(248, 113, 113, 0.4)'
                  };
                  transition: all 0.3s ease-in-out;
                }

                input[type="range"]::-webkit-slider-thumb:hover {
                  background: ${
                    settings.riskThreshold <= 25
                      ? 'linear-gradient(135deg, #34d399 0%, #059669 100%)' :
                    settings.riskThreshold <= 50
                      ? 'linear-gradient(135deg, #fbbf24 0%, #d97706 100%)' :
                    settings.riskThreshold <= 75
                      ? 'linear-gradient(135deg, #fb923c 0%, #ea580c 100%)' :
                      'linear-gradient(135deg, #f87171 0%, #dc2626 100%)'
                  };
                  border-color: ${
                    settings.riskThreshold <= 25 ? '#34d399' :
                    settings.riskThreshold <= 50 ? '#fbbf24' :
                    settings.riskThreshold <= 75 ? '#fb923c' : '#f87171'
                  };
                  box-shadow: 0 5px 15px ${
                    settings.riskThreshold <= 25 ? 'rgba(52, 211, 153, 0.6)' :
                    settings.riskThreshold <= 50 ? 'rgba(251, 191, 36, 0.6)' :
                    settings.riskThreshold <= 75 ? 'rgba(251, 146, 60, 0.6)' : 'rgba(248, 113, 113, 0.6)'
                  };
                  transform: scale(1.15);
                }

                input[type="range"]::-moz-range-thumb {
                  width: 22px;
                  height: 22px;
                  border-radius: 50%;
                  background: ${
                    settings.riskThreshold <= 25
                      ? 'linear-gradient(135deg, #34d399 0%, #10b981 100%)' :
                    settings.riskThreshold <= 50
                      ? 'linear-gradient(135deg, #fbbf24 0%, #eab308 100%)' :
                    settings.riskThreshold <= 75
                      ? 'linear-gradient(135deg, #fb923c 0%, #f97316 100%)' :
                      'linear-gradient(135deg, #f87171 0%, #ef4444 100%)'
                  };
                  border: 3px solid ${
                    settings.riskThreshold <= 25 ? '#34d399' :
                    settings.riskThreshold <= 50 ? '#fbbf24' :
                    settings.riskThreshold <= 75 ? '#fb923c' : '#f87171'
                  };
                  cursor: pointer;
                  box-shadow: 0 3px 10px ${
                    settings.riskThreshold <= 25 ? 'rgba(52, 211, 153, 0.4)' :
                    settings.riskThreshold <= 50 ? 'rgba(251, 191, 36, 0.4)' :
                    settings.riskThreshold <= 75 ? 'rgba(251, 146, 60, 0.4)' : 'rgba(248, 113, 113, 0.4)'
                  };
                  transition: all 0.3s ease-in-out;
                }

                input[type="range"]::-moz-range-track {
                  height: 12px;
                  border-radius: 6px;
                  background: transparent;
                }
              `}</style>
            </div>

            {/* Threshold Labels with Risk Level Indicators */}
            <div className="flex justify-between items-center text-xs">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-green-400">0% Safe</span>
              </div>

              <div className="flex items-center gap-2">
                <span className={`font-medium ${
                  settings.riskThreshold <= 25 ? 'text-green-400' :
                  settings.riskThreshold <= 50 ? 'text-yellow-400' :
                  settings.riskThreshold <= 75 ? 'text-orange-400' : 'text-red-400'
                }`}>
                  {settings.riskThreshold <= 25 ? 'Low Risk' :
                   settings.riskThreshold <= 50 ? 'Medium Risk' :
                   settings.riskThreshold <= 75 ? 'High Risk' : 'Critical Risk'}
                </span>
                <span className="text-gray-400">({settings.riskThreshold}%)</span>
              </div>

              <div className="flex items-center gap-1">
                <span className="text-red-400">100% Critical</span>
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              </div>
            </div>

            {/* Risk Level Description */}
            <div className={`text-xs p-2 rounded-lg border ${
              settings.riskThreshold <= 25 ? 'bg-green-500/10 border-green-500/30 text-green-300' :
              settings.riskThreshold <= 50 ? 'bg-yellow-500/10 border-yellow-500/30 text-yellow-300' :
              settings.riskThreshold <= 75 ? 'bg-orange-500/10 border-orange-500/30 text-orange-300' :
              'bg-red-500/10 border-red-500/30 text-red-300'
            }`}>
              {settings.riskThreshold <= 25 ?
                '🟢 Low Risk: Showing wallets with minimal suspicious activity' :
               settings.riskThreshold <= 50 ?
                '🟡 Medium Risk: Showing wallets with moderate risk indicators' :
               settings.riskThreshold <= 75 ?
                '🟠 High Risk: Showing wallets with significant risk factors' :
                '🔴 Critical Risk: Showing only the most dangerous wallets'
              }
            </div>
          </div>

          {/* Filter Types */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">Threat Types</label>
            <div className="space-y-2">
              {Object.entries(settings.filterTypes).map(([type, enabled]) => (
                <div key={type} className="flex items-center justify-between p-2 rounded-lg hover:bg-slate-700/50">
                  <div className="flex items-center gap-3">
                    <div className={`p-1 rounded ${enabled ? 'text-blue-400' : 'text-gray-400'}`}>
                      {getFilterTypeIcon(type)}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-white">
                        {getFilterTypeLabel(type)}
                      </div>
                      <div className="text-xs text-gray-400">
                        {getFilterTypeDescription(type)}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => updateFilterTypes(type as keyof RiskFilterSettings['filterTypes'], !enabled)}
                    className={`w-12 h-7 rounded-full transition-all duration-300 ${
                      enabled
                        ? 'bg-gradient-to-r from-purple-500 to-blue-500 shadow-lg shadow-purple-500/25'
                        : 'bg-slate-600 hover:bg-slate-500'
                    }`}
                  >
                    <div className={`w-5 h-5 bg-white rounded-full transition-all duration-300 shadow-md ${
                      enabled ? 'translate-x-6 shadow-purple-500/30' : 'translate-x-1'
                    }`} />
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Advanced Settings */}
          {showAdvanced && (
            <div className="p-3 space-y-3 border rounded-lg bg-slate-700/30 border-slate-600">
              <h4 className="flex items-center gap-2 text-sm font-medium text-white">
                <FaCog className="w-4 h-4" />
                Advanced Settings
              </h4>

              {/* Toggle Options */}
              {[
                { key: 'showOnlyFlagged', label: 'Show Only Flagged', desc: 'Hide all non-flagged wallets' },
                { key: 'hideWhitelisted', label: 'Hide Whitelisted', desc: 'Hide known safe addresses' },
                { key: 'highlightSuspicious', label: 'Highlight Suspicious', desc: 'Visual emphasis on risky wallets' },
                { key: 'autoHideClean', label: 'Auto-hide Clean', desc: 'Automatically hide low-risk wallets' }
              ].map(({ key, label, desc }) => (
                <div key={key} className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-white">{label}</div>
                    <div className="text-xs text-gray-400">{desc}</div>
                  </div>
                  <button
                    onClick={() => updateSettings({ [key]: !settings[key as keyof RiskFilterSettings] })}
                    className={`w-12 h-7 rounded-full transition-all duration-300 ${
                      settings[key as keyof RiskFilterSettings]
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500 shadow-lg shadow-green-500/25'
                        : 'bg-slate-600 hover:bg-slate-500'
                    }`}
                  >
                    <div className={`w-5 h-5 bg-white rounded-full transition-all duration-300 shadow-md ${
                      settings[key as keyof RiskFilterSettings] ? 'translate-x-6 shadow-green-500/30' : 'translate-x-1'
                    }`} />
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <button
              onClick={() => onApplyFilter(settings)}
              className="flex-1 px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white rounded-xl font-semibold transition-all duration-300 shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 hover:scale-105 transform"
            >
              Apply Filter
            </button>
            <button
              onClick={onResetFilter}
              className="px-6 py-3 border border-slate-500 hover:border-amber-400 hover:bg-amber-500/10 text-slate-300 hover:text-amber-400 rounded-xl font-medium transition-all duration-300 hover:scale-105 transform"
            >
              Reset
            </button>
          </div>
        </div>
      </div>

      {/* Scroll to Top Button */}
      {showScrollToTop && (
        <button
          onClick={scrollToTop}
          className="fixed z-10 p-3 text-white transition-all duration-300 bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 rounded-full shadow-lg shadow-amber-500/25 hover:shadow-amber-500/40 bottom-4 right-4 hover:scale-110 transform"
          style={{ transform: 'translateY(-60px)' }}
        >
          <FaArrowUp className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};

export default RiskFilterPanel;
