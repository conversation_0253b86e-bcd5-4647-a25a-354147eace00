import { useEffect, useState, useRef } from 'react';
import B<PERSON>bleMap, { BubbleMapRefHandle } from '@/components/BubbleMap';
import Header from '@/components/Header';
import { generateMockData } from '@/utils/graphUtils';
import { GraphData, Node } from '@/services/neo4jService';
import { FaPlus, FaMinus, FaHome, FaInfoCircle } from 'react-icons/fa';

export default function TestPage() {
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], links: [] });
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [currentZoom, setCurrentZoom] = useState<number>(1);
  const containerRef = useRef<HTMLDivElement>(null);
  const bubbleMapRef = useRef<BubbleMapRefHandle>(null);

  useEffect(() => {
    // Generate mock data
    const mockData = generateMockData(20);
    console.log('Generated mock data:', mockData);

    // Center the graph in the container
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;

      // Adjust node positions to center in container
      mockData.nodes.forEach(node => {
        if (node.x !== undefined && node.y !== undefined) {
          node.x += centerX;
          node.y += centerY;
          node.targetX = node.x;
          node.targetY = node.y;
        }
      });
    }

    setGraphData(mockData);
  }, []);

  // Update current zoom periodically
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (bubbleMapRef.current) {
        setCurrentZoom(bubbleMapRef.current.getCurrentZoom());
      }
    }, 500);

    return () => clearInterval(intervalId);
  }, []);

  const handleNodeClick = (node: Node) => {
    console.log('Node clicked:', node);
    setSelectedNode(node);
  };

  // Zoom controls
  const zoomIn = () => {
    if (bubbleMapRef.current) {
      bubbleMapRef.current.zoomIn();
    }
  };

  const zoomOut = () => {
    if (bubbleMapRef.current) {
      bubbleMapRef.current.zoomOut();
    }
  };

  const resetZoom = () => {
    if (bubbleMapRef.current) {
      bubbleMapRef.current.setZoom(1);
    }
  };

  return (
    <main className="min-h-screen bg-background text-foreground">
      <Header />
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">Test Bubble Map</h1>
        <p className="mb-4 text-accent bg-accent/10 p-3 rounded-lg">
          Thử nghiệm các tính năng mới: Kéo thả các node, kéo thả map, và zoom in/out.
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          <div
            className="lg:col-span-3 bg-background/60 backdrop-blur-sm rounded-lg border border-accent/20 h-[600px] relative"
            ref={containerRef}
          >
            {/* Controls */}
            <div className="absolute top-3 right-3 z-10 flex flex-col gap-2">
              <button
                className="bg-primary hover:bg-primary/80 text-white p-2 rounded-lg transition-colors flex items-center justify-center"
                onClick={zoomIn}
                title="Zoom In"
              >
                <FaPlus />
              </button>
              <button
                className="bg-primary hover:bg-primary/80 text-white p-2 rounded-lg transition-colors flex items-center justify-center"
                onClick={zoomOut}
                title="Zoom Out"
              >
                <FaMinus />
              </button>
              <button
                className="bg-primary hover:bg-primary/80 text-white p-2 rounded-lg transition-colors flex items-center justify-center"
                onClick={resetZoom}
                title="Reset Zoom"
              >
                <FaHome />
              </button>
            </div>

            {/* Zoom level indicator */}
            <div className="absolute bottom-3 right-3 z-10 bg-background/70 backdrop-blur-sm px-3 py-1 rounded-lg text-xs">
              Zoom: {Math.round(currentZoom * 100)}%
            </div>

            {/* Help tooltip */}
            <div className="absolute bottom-3 left-3 z-10">
              <div className="relative group">
                <button className="bg-primary hover:bg-primary/80 text-white p-2 rounded-full transition-colors flex items-center justify-center">
                  <FaInfoCircle />
                </button>
                <div className="absolute bottom-full left-0 mb-2 hidden group-hover:block bg-background/90 backdrop-blur-sm p-3 rounded-lg border border-accent/20 w-64">
                  <h3 className="font-bold text-sm mb-1">Hướng dẫn:</h3>
                  <ul className="text-xs space-y-1">
                    <li>• Kéo node: Click vào node và di chuyển</li>
                    <li>• Di chuyển map: Click vào khoảng trống và kéo</li>
                    <li>• Zoom: Cuộn chuột hoặc dùng nút +/-</li>
                    <li>• Reset: Nhấn nút Home</li>
                  </ul>
                </div>
              </div>
            </div>

            {graphData.nodes.length > 0 ? (
              <BubbleMap
                graphData={graphData}
                onNodeClick={handleNodeClick}
                ref={bubbleMapRef}
              />
            ) : (
              <div className="flex justify-center items-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent mb-4"></div>
                <p>Loading data...</p>
              </div>
            )}
          </div>

          <div className="lg:col-span-1">
            <div className="bg-background/60 backdrop-blur-sm p-4 rounded-lg border border-accent/20">
              <h2 className="text-xl font-bold mb-2">Selected Node</h2>
              {selectedNode ? (
                <div>
                  <p><span className="font-semibold">ID:</span> {selectedNode.id}</p>
                  <p><span className="font-semibold">Address:</span> {selectedNode.address}</p>
                  {selectedNode.label && (
                    <p><span className="font-semibold">Label:</span> {selectedNode.label}</p>
                  )}
                  {selectedNode.balance && (
                    <p><span className="font-semibold">Balance:</span> {selectedNode.balance}</p>
                  )}
                  {selectedNode.transactionCount !== undefined && (
                    <p><span className="font-semibold">Transactions:</span> {selectedNode.transactionCount}</p>
                  )}
                  {selectedNode.tags && selectedNode.tags.length > 0 && (
                    <div>
                      <span className="font-semibold">Tags:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {selectedNode.tags.map((tag, index) => (
                          <span key={index} className="bg-accent/20 text-accent px-2 py-0.5 rounded-full text-xs">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  <div className="mt-3 pt-3 border-t border-accent/20">
                    <p><span className="font-semibold">Position:</span> x={Math.round(selectedNode.x || 0)}, y={Math.round(selectedNode.y || 0)}</p>
                  </div>
                </div>
              ) : (
                <p className="text-foreground/70">Click on a node to see details</p>
              )}
            </div>

            <div className="bg-background/60 backdrop-blur-sm p-4 rounded-lg border border-accent/20 mt-4">
              <h2 className="text-xl font-bold mb-2">Graph Info</h2>
              <p><span className="font-semibold">Nodes:</span> {graphData.nodes.length}</p>
              <p><span className="font-semibold">Links:</span> {graphData.links.length}</p>
              <p className="mt-3 text-accent">
                💡 Tip: Kéo thả các node để di chuyển chúng!
              </p>
              <p className="text-accent">
                💡 Tip: Click vào vùng trống để di chuyển map!
              </p>
              <p className="text-accent">
                💡 Tip: Sử dụng cuộn chuột để zoom in/out!
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}