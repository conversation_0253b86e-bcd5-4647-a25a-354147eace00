import { useState, useEffect } from 'react';
import Head from 'next/head';
import Header from '@/components/Header';
import TopWalletRankings from '@/components/dashboard/TopWalletRankings';
import StatisticalDashboard from '@/components/dashboard/StatisticalDashboard';
import NetworkSelector from '@/components/dashboard/NetworkSelector';

import { FaChartLine, FaTrophy, FaShieldAlt, FaNetworkWired, FaGlobe } from 'react-icons/fa';

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);
  const [selectedNetwork, setSelectedNetwork] = useState('ethereum');
  const [isNetworkChanging, setIsNetworkChanging] = useState(false);

  useEffect(() => {
    // Simulate initial data loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleNetworkChange = (networkId: string, scrollToAnalysis: boolean = false) => {
    setIsNetworkChanging(true);

    // Add a small delay to show transition effect
    setTimeout(() => {
      setSelectedNetwork(networkId);

      // Scroll to analysis section if requested
      if (scrollToAnalysis) {
        setTimeout(() => {
          const analysisSection = document.getElementById('network-analysis');
          if (analysisSection) {
            analysisSection.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            });
          }
        }, 400);
      }

      // Reset transition state after content updates
      setTimeout(() => {
        setIsNetworkChanging(false);
      }, 300);
    }, 150);
  };

  if (isLoading) {
    return (
      <>
        <Head>
          <title>Crypto Bubble Map - Analytics Dashboard</title>
          <meta name="description" content="Comprehensive crypto wallet analytics dashboard with real-time insights" />
        </Head>
        <main className="min-h-screen bg-background">
          <Header />
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <div className="relative mb-6">
                <div className="absolute inset-0 rounded-full bg-gradient-brand blur-xl opacity-20"></div>
                <div className="relative flex items-center justify-center w-16 h-16 border rounded-full bg-gradient-to-br from-background-secondary to-background-tertiary border-border-accent">
                  <FaChartLine className="text-2xl text-accent-400 animate-pulse" />
                </div>
              </div>
              <h2 className="text-xl font-semibold gradient-text mb-2">Loading Dashboard</h2>
              <p className="text-foreground-muted">Analyzing wallet data...</p>
            </div>
          </div>
        </main>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Crypto Bubble Map - Analytics Dashboard</title>
        <meta name="description" content="Comprehensive crypto wallet analytics dashboard with top wallet rankings and statistical insights" />
      </Head>

      <main className="min-h-screen bg-background">
        {/* Header */}
        <Header />

        {/* Top Banner CTA */}
        <div className="fixed top-20 left-0 right-0 z-40 bg-gradient-to-r from-primary-500/10 via-accent-500/10 to-secondary-500/10 backdrop-blur-sm border-b border-border-secondary/50 slide-down">
          <div className="container mx-auto px-4 py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm">
                <FaNetworkWired className="text-accent-400 bounce-subtle" size={14} />
                <span className="text-foreground-muted">
                  <span className="hidden sm:inline">Explore wallet relationships with our </span>
                  <span className="text-accent-400 font-medium">Interactive Bubble Map</span>
                </span>
              </div>
              <a
                href="/map"
                className="flex items-center gap-1 px-3 py-1 text-xs font-medium text-white bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 rounded-full transition-all duration-200 hover:scale-105 cta-button"
              >
                <span>Try Now</span>
                <FaNetworkWired size={10} />
              </a>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="pt-32 pb-8">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">

            {/* Hero Section */}
            <div className="mb-8 sm:mb-12 text-center">
              <div className="relative mb-4 sm:mb-6">
                <div className="absolute inset-0 rounded-full bg-gradient-brand blur-2xl opacity-30 scale-150"></div>
                <div className="relative flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 mx-auto border rounded-full bg-gradient-to-br from-background-secondary to-background-tertiary border-border-accent">
                  <FaNetworkWired className="text-2xl sm:text-3xl text-accent-400" />
                </div>
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold gradient-text mb-3 sm:mb-4 px-4">
                Crypto Analytics Dashboard
              </h1>
              <p className="text-lg sm:text-xl text-foreground-muted max-w-3xl mx-auto leading-relaxed px-4 mb-6 sm:mb-8">
                Comprehensive insights into cryptocurrency wallet behavior, risk analysis, and network activity patterns
              </p>

              {/* Prominent CTA Button */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 via-accent-500/20 to-secondary-500/20 blur-xl rounded-2xl cta-glow"></div>
                <a
                  href="/map"
                  className="relative inline-flex items-center gap-3 px-8 py-4 text-lg font-semibold text-white transition-all duration-300 rounded-xl bg-gradient-to-r from-primary-500 via-accent-500 to-secondary-500 hover:from-primary-600 hover:via-accent-600 hover:to-secondary-600 shadow-2xl hover:shadow-accent-500/25 hover:scale-105 transform group cta-button magnetic-hover"
                >
                  <FaNetworkWired className="text-xl group-hover:rotate-12 transition-transform duration-300" />
                  <span>Explore Interactive Bubble Map</span>
                  <div className="flex items-center gap-1 opacity-75 group-hover:opacity-100 transition-opacity">
                    <div className="w-1 h-1 bg-white rounded-full animate-pulse"></div>
                    <div className="w-1 h-1 bg-white rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-1 h-1 bg-white rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </a>
              </div>
            </div>



            {/* Network Selector with Analyzing Indicator */}
            <div id="network-analysis" className="mb-6 sm:mb-8">
              {/* Centered Network Selector and Analyzing Indicator */}
              <div className="flex flex-col items-center justify-center gap-4 sm:gap-6 layout-transition">
                {/* Network Selector - Compact Size */}
                <div id="network-selector-container" className="w-full max-w-xs sm:max-w-sm">
                  <NetworkSelector
                    selectedNetwork={selectedNetwork}
                    onNetworkChange={handleNetworkChange}
                  />
                </div>

                {/* Analyzing Network Indicator - Centered */}
                <div className={`flex justify-center transition-all duration-500 ${isNetworkChanging ? 'opacity-50 scale-95' : 'opacity-100 scale-100'}`}>
                  <div className="network-indicator">
                    <div className="flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2 sm:py-3 bg-gradient-to-r from-background-secondary/80 to-background-tertiary/80 backdrop-blur-sm border border-border-accent rounded-full network-glow network-glow-enhanced network-indicator-compact">
                      <div className="flex items-center gap-1 sm:gap-2">
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="text-xs sm:text-sm text-foreground-muted hidden sm:inline">Analyzing:</span>
                        <span className="text-xs text-foreground-muted sm:hidden">Current:</span>
                      </div>
                      <div className="flex items-center gap-1 sm:gap-2">
                        <div
                          className="network-icon flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 rounded-full text-xs sm:text-sm layout-transition-fast"
                          style={{
                            background: selectedNetwork === 'ethereum' ? 'linear-gradient(135deg, #627EEA, #8B9DC3)' :
                                       selectedNetwork === 'bsc' ? 'linear-gradient(135deg, #F3BA2F, #FDD835)' :
                                       selectedNetwork === 'solana' ? 'linear-gradient(135deg, #9945FF, #14F195)' :
                                       selectedNetwork === 'polygon' ? 'linear-gradient(135deg, #8247E5, #A855F7)' :
                                       selectedNetwork === 'near' ? 'linear-gradient(135deg, #00C08B, #58E6D9)' :
                                       selectedNetwork === 'aptos' ? 'linear-gradient(135deg, #FF5F5F, #FF8A80)' :
                                       selectedNetwork === 'sui' ? 'linear-gradient(135deg, #4DA2FF, #7DD3FC)' :
                                       selectedNetwork === 'arbitrum' ? 'linear-gradient(135deg, #28A0F0, #4FC3F7)' :
                                       selectedNetwork === 'optimism' ? 'linear-gradient(135deg, #FF0420, #FF6B6B)' :
                                       'linear-gradient(135deg, #627EEA, #8B9DC3)',
                            color: 'white'
                          }}
                        >
                          {selectedNetwork === 'ethereum' ? '🔷' :
                           selectedNetwork === 'bsc' ? '🟡' :
                           selectedNetwork === 'solana' ? '🟣' :
                           selectedNetwork === 'polygon' ? '🟪' :
                           selectedNetwork === 'near' ? '🌈' :
                           selectedNetwork === 'aptos' ? '🔴' :
                           selectedNetwork === 'sui' ? '💧' :
                           selectedNetwork === 'arbitrum' ? '🔵' :
                           selectedNetwork === 'optimism' ? '🔴' : '🔷'}
                        </div>
                        <span className="network-name font-semibold text-foreground text-xs sm:text-sm xl:text-base whitespace-nowrap layout-transition-fast">
                          {selectedNetwork === 'bsc' ? 'BNB Chain' :
                           selectedNetwork === 'near' ? 'NEAR' :
                           selectedNetwork === 'arbitrum' ? 'Arbitrum' :
                           selectedNetwork === 'optimism' ? 'Optimism' :
                           selectedNetwork.charAt(0).toUpperCase() + selectedNetwork.slice(1)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Statistical Dashboard - Moved closer to Network Selector */}
            <div className={`mb-6 sm:mb-8 transition-all duration-700 section-fade-in ${isNetworkChanging ? 'opacity-30 translate-y-4' : 'opacity-100 translate-y-0'}`}>
              <StatisticalDashboard selectedNetwork={selectedNetwork} />
            </div>

            {/* Subtle Visual Separator */}
            <div className="flex justify-center mb-8 sm:mb-12 mt-8 sm:mt-12">
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-border-accent to-transparent subtle-separator"></div>
            </div>

            {/* Top Wallet Rankings */}
            <div className={`mb-8 sm:mb-12 transition-all duration-700 section-fade-in ${isNetworkChanging ? 'opacity-30 translate-y-4' : 'opacity-100 translate-y-0'}`}>
              <div className="flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6 px-2 sm:px-0">
                <div className="relative">
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-accent-500/20 to-secondary-500/20 blur-md"></div>
                  <div className="relative p-1.5 sm:p-2 rounded-lg bg-gradient-to-r from-accent-500/10 to-secondary-500/10 border border-border-accent">
                    <FaTrophy className="text-base sm:text-lg text-accent-400" />
                  </div>
                </div>
                <div className="flex-1">
                  <h2 className="text-xl sm:text-2xl font-bold text-foreground">Top Wallet Rankings</h2>
                  <p className="text-sm text-foreground-muted mt-1">
                    Highest quality wallets on the selected network
                  </p>
                </div>
              </div>
              <TopWalletRankings selectedNetwork={selectedNetwork} />
            </div>

            {/* Additional Information Section */}
            <div className="text-center">
              <div className="relative p-8 rounded-2xl glass-card border border-border-accent">
                <div className="absolute inset-0 rounded-2xl bg-gradient-brand opacity-5"></div>
                <div className="relative">
                  <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 border rounded-full bg-gradient-to-br from-background-secondary to-background-tertiary border-border-accent">
                    <FaShieldAlt className="text-2xl text-accent-400" />
                  </div>
                  <h3 className="text-2xl font-bold gradient-text mb-4">
                    Advanced Analytics
                  </h3>
                  <p className="text-foreground-muted max-w-2xl mx-auto">
                    Our platform provides comprehensive wallet analysis, risk scoring, and network visualization tools to help you make informed decisions in the crypto space.
                  </p>
                </div>
              </div>
            </div>

          </div>
        </div>

        {/* Floating Action Button */}
        <div className="fixed bottom-6 right-6 z-50 float-animation">
          <a
            href="/map"
            className="group relative flex items-center gap-3 px-4 py-3 text-white transition-all duration-300 rounded-full bg-gradient-to-r from-primary-500 to-accent-500 hover:from-primary-600 hover:to-accent-600 shadow-2xl hover:shadow-accent-500/30 hover:scale-110 transform cta-button magnetic-hover"
          >
            {/* Floating button content */}
            <div className="flex items-center gap-2">
              <FaNetworkWired className="text-lg group-hover:rotate-12 transition-transform duration-300" />
              <span className="hidden sm:inline text-sm font-medium">Bubble Map</span>
            </div>

            {/* Animated ring */}
            <div className="absolute inset-0 rounded-full border-2 border-white/20 animate-ping"></div>

            {/* Tooltip for mobile */}
            <div className="absolute bottom-full right-0 mb-2 px-2 py-1 text-xs text-white bg-black/80 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap sm:hidden">
              Explore Bubble Map
            </div>
          </a>
        </div>


      </main>
    </>
  );
}