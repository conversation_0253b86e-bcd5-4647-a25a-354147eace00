import type { NextApiRequest, NextApiResponse } from 'next';
import neo4jService, { Node } from '@/services/neo4jService';
import { generateMockData } from '@/utils/graphUtils';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { q } = req.query;

  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  if (!q || typeof q !== 'string') {
    return res.status(400).json({ message: 'Search query is required' });
  }

  try {
    // For now, we'll use mock data for search
    // In a real application, you would implement a search function in the Neo4j service
    const mockData = generateMockData(10);

    // Filter mock data based on the search query
    const searchQuery = q.toLowerCase();
    const results: Node[] = mockData.nodes
      .filter(node =>
        node.address.toLowerCase().includes(searchQuery) ||
        (node.label && node.label.toLowerCase().includes(searchQuery))
      )
      .map(node => ({
        id: node.id,
        address: node.address,
        label: node.label,
        balance: node.balance,
        transactionCount: node.transactionCount,
        tags: node.tags
      }));

    return res.status(200).json(results);
  } catch (error) {
    console.error('Error searching wallets:', error);
    return res.status(500).json({ message: 'Error searching wallet data' });
  }
}