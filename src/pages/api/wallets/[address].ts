import type { NextApiRequest, NextApiResponse } from 'next';
import neo4jService from '@/services/neo4jService';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { address } = req.query;

  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  if (!address || typeof address !== 'string') {
    return res.status(400).json({ message: 'Valid wallet address is required' });
  }

  try {
    const walletDetails = await neo4jService.getWalletDetails(address);

    if (!walletDetails) {
      return res.status(404).json({ message: 'Wallet not found' });
    }

    return res.status(200).json(walletDetails);
  } catch (error) {
    console.error('Error fetching wallet details:', error);
    return res.status(500).json({ message: 'Error fetching wallet details' });
  } finally {
    // Close the Neo4j connection
    await neo4jService.close();
  }
}