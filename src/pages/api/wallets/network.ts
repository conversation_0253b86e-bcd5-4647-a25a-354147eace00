import type { NextApiRequest, NextApiResponse } from 'next';
import neo4jService from '@/services/neo4jService';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { address, depth } = req.query;

  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  if (!address || typeof address !== 'string') {
    return res.status(400).json({ message: 'Valid wallet address is required' });
  }

  // Parse depth parameter or use default value
  const depthValue = typeof depth === 'string' ? parseInt(depth, 10) : 2;

  // Limit depth to prevent excessive queries
  const safeDepth = Math.min(Math.max(1, depthValue), 3);

  try {
    const networkData = await neo4jService.getWalletNetwork(address, safeDepth);

    return res.status(200).json(networkData);
  } catch (error) {
    console.error('Error fetching wallet network:', error);
    return res.status(500).json({ message: 'Error fetching wallet network data' });
  } finally {
    // Close the Neo4j connection
    await neo4jService.close();
  }
}