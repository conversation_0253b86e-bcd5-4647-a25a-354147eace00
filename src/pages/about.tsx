import Header from '@/components/Header';
import { FaNetworkWired, FaWallet, FaSearch, FaBitcoin } from 'react-icons/fa';

export default function About() {
  return (
    <main className="min-h-screen bg-background">
      <Header showSearch={false} />
      {/* Add top padding to account for fixed header */}
      <div className="pt-20 container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-6 text-primary">About Crypto Bubble Map</h1>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
              <FaNetworkWired className="text-accent" />
              What is Crypto Bubble Map?
            </h2>
            <p className="mb-4">
              Crypto Bubble Map is a visualization tool that displays the relationships between cryptocurrency wallet addresses on the blockchain.
              It represents wallets as bubbles of different sizes and colors, connected by lines that represent transactions between them.
            </p>
            <p>
              This application helps you explore and understand the network of transactions between wallets,
              making it easier to analyze crypto flows, identify exchange wallets, and discover patterns in blockchain data.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
              <FaSearch className="text-accent" />
              How to Use
            </h2>
            <div className="bg-background/60 backdrop-blur-sm p-6 rounded-lg border border-accent/20 mb-4">
              <h3 className="text-xl font-medium mb-2">Search for a Wallet</h3>
              <p>
                Enter a wallet address in the search bar to visualize its transaction network.
                The application will show the wallet and its connections to other wallets.
              </p>
            </div>

            <div className="bg-background/60 backdrop-blur-sm p-6 rounded-lg border border-accent/20 mb-4">
              <h3 className="text-xl font-medium mb-2">Explore the Network</h3>
              <p>
                Click on bubbles to see details about each wallet. Zoom in and out to explore different parts of the network.
                The size of each bubble represents the transaction volume or balance of the wallet.
              </p>
            </div>

            <div className="bg-background/60 backdrop-blur-sm p-6 rounded-lg border border-accent/20">
              <h3 className="text-xl font-medium mb-2">Analyze Connections</h3>
              <p>
                The lines between bubbles represent transactions. Thicker lines indicate higher transaction volumes.
                Different colors represent different types of wallets, such as exchanges, contracts, or individual wallets.
              </p>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
              <FaBitcoin className="text-accent" />
              Technical Details
            </h2>
            <p className="mb-4">
              Crypto Bubble Map is built using:
            </p>
            <ul className="list-disc pl-6 space-y-2">
              <li><strong>Front-end:</strong> React, Next.js, D3.js, React Force Graph</li>
              <li><strong>Back-end:</strong> Neo4j graph database for storing wallet relationships</li>
              <li><strong>Data Processing:</strong> Custom crawler that extracts transaction data from the blockchain</li>
            </ul>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4 flex items-center gap-2">
              <FaWallet className="text-accent" />
              Privacy and Security
            </h2>
            <p>
              Crypto Bubble Map only displays publicly available blockchain data. We do not collect or store any personal information about users.
              All wallet addresses and transaction data are obtained from public blockchain explorers and APIs.
            </p>
          </section>
        </div>
      </div>
    </main>
  );
}