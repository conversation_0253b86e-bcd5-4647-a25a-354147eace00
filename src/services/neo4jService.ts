import neo4j, { Driver, Session } from 'neo4j-driver';

// Social media profile interface
export interface SocialProfiles {
  twitter?: string;
  discord?: string;
  telegram?: string;
  github?: string;
  website?: string;
  linkedin?: string;
  medium?: string;
  reddit?: string;
}

// Types for graph data
export interface Node {
  id: string;
  address: string;
  label?: string;
  balance?: string;
  transactionCount?: number;
  tags?: string[];
  color?: string;
  size?: number;
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
  targetX?: number;
  targetY?: number;
  highlighted?: boolean;
  // Image support for visual representation
  imageUrl?: string;
  avatar?: string;
  hasImage?: boolean;
  imageLoaded?: boolean;
  imageError?: boolean;
  // Social media connectivity
  socialProfiles?: SocialProfiles;
  hasVerifiedSocials?: boolean;
  socialScore?: number; // 0-100 based on social presence
}

export interface Link {
  source: string;
  target: string;
  value: number;
  type?: string;
  timestamp?: number;
}

// Interface for detailed transaction between two specific wallets
export interface PairwiseTransaction {
  id: string;
  hash: string;
  from: string;
  to: string;
  value: number;
  token: string;
  tokenSymbol: string;
  tokenDecimals: number;
  usdValue?: number;
  timestamp: string;
  blockNumber: number;
  gasUsed: number;
  gasPrice: number;
  gasFee: number;
  transactionType: 'transfer' | 'swap' | 'deposit' | 'withdraw' | 'contract_call' | 'nft_transfer';
  method?: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  riskFactors?: string[];
  status: 'success' | 'failed' | 'pending';
  direction: 'incoming' | 'outgoing';
  isInternal?: boolean;
  contractAddress?: string;
  logs?: TransactionLog[];
}

export interface TransactionLog {
  address: string;
  topics: string[];
  data: string;
  decoded?: {
    name: string;
    params: { [key: string]: any };
  };
}

// Interface for pairwise transaction summary
export interface PairwiseTransactionSummary {
  walletA: string;
  walletB: string;
  totalTransactions: number;
  totalVolume: number;
  totalVolumeUSD: number;
  firstTransaction: string;
  lastTransaction: string;
  topTokens: Array<{
    symbol: string;
    volume: number;
    volumeUSD: number;
    transactionCount: number;
  }>;
  riskDistribution: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  transactionTypes: {
    [key: string]: number;
  };
}

export interface GraphData {
  nodes: Node[];
  links: Link[];
}

class Neo4jService {
  private driver: Driver | null = null;
  private session: Session | null = null;

  constructor() {
    // Initialize in the connect method to avoid issues with SSR
  }

  // Connect to Neo4j database
  public async connect() {
    try {
      if (!this.driver) {
        // Get connection details from environment variables
        const uri = process.env.NEXT_PUBLIC_NEO4J_URI || 'neo4j://localhost:7687';
        const user = process.env.NEXT_PUBLIC_NEO4J_USER || 'neo4j';
        const password = process.env.NEXT_PUBLIC_NEO4J_PASSWORD || 'password';

        this.driver = neo4j.driver(uri, neo4j.auth.basic(user, password));
        this.session = this.driver.session();
      }

      return true;
    } catch (error) {
      console.error('Failed to connect to Neo4j:', error);
      return false;
    }
  }

  // Close the Neo4j connection
  public async close() {
    try {
      if (this.session) {
        await this.session.close();
      }
      if (this.driver) {
        await this.driver.close();
      }
      this.session = null;
      this.driver = null;
    } catch (error) {
      console.error('Error closing Neo4j connection:', error);
    }
  }

  // Get wallet network data for a specific address
  public async getWalletNetwork(address: string, depth: number = 2): Promise<GraphData> {
    try {
      await this.connect();

      if (!this.session) {
        throw new Error('No active Neo4j session');
      }

      // Cypher query to get nodes and relationships up to a certain depth
      const result = await this.session.run(
        `
        MATCH path = (source:Wallet {address: $address})-[r:TRANSFERS*1..${depth}]-(target:Wallet)
        WITH COLLECT(path) as paths
        CALL apoc.graph.fromPaths(paths, "", {}) YIELD graph
        RETURN graph
        `,
        { address }
      );

      if (result.records.length === 0) {
        return { nodes: [], links: [] };
      }

      const graph = result.records[0].get('graph');

      // Process and transform Neo4j result to our graph format
      const nodes: Node[] = graph.nodes.map((node: any) => ({
        id: node.id,
        address: node.properties.address,
        label: node.properties.label || undefined,
        balance: node.properties.balance?.toString() || undefined,
        transactionCount: node.properties.txCount?.toNumber() || 0,
        tags: node.properties.tags || [],
        // Calculate size based on transaction count or balance
        size: calculateNodeSize(node.properties.txCount?.toNumber() || 0),
        // Assign colors based on node properties
        color: assignNodeColor(node.properties)
      }));

      const links: Link[] = graph.relationships.map((rel: any) => ({
        source: rel.startNodeId,
        target: rel.endNodeId,
        value: rel.properties.value?.toNumber() || 1,
        type: rel.properties.type || 'transfer',
        timestamp: rel.properties.timestamp?.toNumber() || Date.now()
      }));

      return { nodes, links };
    } catch (error) {
      console.error('Error fetching wallet network:', error);
      return { nodes: [], links: [] };
    }
  }

  // Get details of a specific wallet
  public async getWalletDetails(address: string): Promise<Node | null> {
    try {
      await this.connect();

      if (!this.session) {
        throw new Error('No active Neo4j session');
      }

      const result = await this.session.run(
        `
        MATCH (w:Wallet {address: $address})
        RETURN w
        `,
        { address }
      );

      if (result.records.length === 0) {
        return null;
      }

      const wallet = result.records[0].get('w');

      return {
        id: wallet.identity.toString(),
        address: wallet.properties.address,
        label: wallet.properties.label || undefined,
        balance: wallet.properties.balance?.toString() || undefined,
        transactionCount: wallet.properties.txCount?.toNumber() || 0,
        tags: wallet.properties.tags || []
      };
    } catch (error) {
      console.error('Error fetching wallet details:', error);
      return null;
    }
  }
}

// Helper function to calculate node size based on transaction count
function calculateNodeSize(txCount: number): number {
  // Min size is 5, max size is 30
  const minSize = 5;
  const maxSize = 30;

  // Log scale for better visualization
  if (txCount === 0) return minSize;
  const logSize = Math.log10(txCount + 1) * 5;

  return Math.max(minSize, Math.min(maxSize, logSize));
}

// Helper function to assign colors based on node properties
function assignNodeColor(properties: any): string {
  // Default color
  let color = '#3B82F6'; // primary blue

  // Check if it's a contract
  if (properties.isContract) {
    return '#8B5CF6'; // accent purple
  }

  // Check if it has a label (might be an exchange or known entity)
  if (properties.label) {
    return '#10B981'; // secondary green
  }

  // Check if it's a high-value wallet
  if (properties.balance && parseFloat(properties.balance) > 100) {
    return '#F59E0B'; // amber
  }

  return color;
}

// Singleton instance
const neo4jService = new Neo4jService();

export default neo4jService;