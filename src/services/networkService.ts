export interface NetworkInfo {
  id: string;
  name: string;
  symbol: string;
  chainId: number;
  icon: string;
  color: string;
  gradientFrom: string;
  gradientTo: string;
  explorerUrl: string;
  rpcUrl: string;
  isMainnet: boolean;
  category: 'layer1' | 'layer2' | 'sidechain';
  description: string;
  tvl?: number; // Total Value Locked
  marketCap?: number;
  dailyTransactions?: number;
  activeWallets?: number;
  avgGasPrice?: number;
  blockTime?: number; // in seconds
  tps?: number; // transactions per second
}

export interface NetworkStats {
  networkId: string;
  totalWallets: number;
  totalVolume: number;
  totalTransactions: number;
  averageQualityScore: number;
  flaggedWallets: number;
  whitelistedWallets: number;
  walletTypes: {
    regular: number;
    exchange: number;
    contract: number;
    whale: number;
    defi: number;
    bridge: number;
    miner: number;
  };
  riskDistribution: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  topTokens?: string[];
  lastUpdate: Date;
}

export interface NetworkRanking {
  rank: number;
  network: NetworkInfo;
  score: number;
  change?: number;
  metrics: {
    tvl: number;
    marketCap: number;
    dailyVolume: number;
    activeUsers: number;
    developerActivity: number;
    ecosystemGrowth: number;
  };
}

class NetworkService {
  private networks: Map<string, NetworkInfo> = new Map();
  private networkStats: Map<string, NetworkStats> = new Map();
  private rankings: NetworkRanking[] = [];
  private listeners: ((networks: NetworkInfo[]) => void)[] = [];

  constructor() {
    this.initializeNetworks();
    this.generateMockStats();
    this.calculateRankings();
  }

  private initializeNetworks() {
    const networksData: NetworkInfo[] = [
      {
        id: 'ethereum',
        name: 'Ethereum',
        symbol: 'ETH',
        chainId: 1,
        icon: '🔷',
        color: '#627EEA',
        gradientFrom: '#627EEA',
        gradientTo: '#8B9DC3',
        explorerUrl: 'https://etherscan.io',
        rpcUrl: 'https://mainnet.infura.io/v3/',
        isMainnet: true,
        category: 'layer1',
        description: 'The world\'s programmable blockchain',
        tvl: 25000000000,
        marketCap: 280000000000,
        dailyTransactions: 1200000,
        activeWallets: 650000,
        avgGasPrice: 25,
        blockTime: 12,
        tps: 15
      },
      {
        id: 'bsc',
        name: 'BNB Smart Chain',
        symbol: 'BNB',
        chainId: 56,
        icon: '🟡',
        color: '#F3BA2F',
        gradientFrom: '#F3BA2F',
        gradientTo: '#FDD835',
        explorerUrl: 'https://bscscan.com',
        rpcUrl: 'https://bsc-dataseed.binance.org/',
        isMainnet: true,
        category: 'sidechain',
        description: 'Fast and low-cost blockchain for DeFi',
        tvl: 8500000000,
        marketCap: 45000000000,
        dailyTransactions: 3500000,
        activeWallets: 1200000,
        avgGasPrice: 5,
        blockTime: 3,
        tps: 65
      },
      {
        id: 'solana',
        name: 'Solana',
        symbol: 'SOL',
        chainId: 101,
        icon: '🟣',
        color: '#9945FF',
        gradientFrom: '#9945FF',
        gradientTo: '#14F195',
        explorerUrl: 'https://explorer.solana.com',
        rpcUrl: 'https://api.mainnet-beta.solana.com',
        isMainnet: true,
        category: 'layer1',
        description: 'High-performance blockchain for Web3',
        tvl: 1200000000,
        marketCap: 15000000000,
        dailyTransactions: 25000000,
        activeWallets: 450000,
        avgGasPrice: 0.00025,
        blockTime: 0.4,
        tps: 3000
      },
      {
        id: 'polygon',
        name: 'Polygon',
        symbol: 'MATIC',
        chainId: 137,
        icon: '🟪',
        color: '#8247E5',
        gradientFrom: '#8247E5',
        gradientTo: '#A855F7',
        explorerUrl: 'https://polygonscan.com',
        rpcUrl: 'https://polygon-rpc.com/',
        isMainnet: true,
        category: 'layer2',
        description: 'Ethereum scaling solution',
        tvl: 1800000000,
        marketCap: 8500000000,
        dailyTransactions: 2800000,
        activeWallets: 380000,
        avgGasPrice: 30,
        blockTime: 2,
        tps: 65
      },
      {
        id: 'near',
        name: 'NEAR Protocol',
        symbol: 'NEAR',
        chainId: 0,
        icon: '🌈',
        color: '#00C08B',
        gradientFrom: '#00C08B',
        gradientTo: '#58E6D9',
        explorerUrl: 'https://explorer.near.org',
        rpcUrl: 'https://rpc.mainnet.near.org',
        isMainnet: true,
        category: 'layer1',
        description: 'User-friendly blockchain for everyone',
        tvl: 150000000,
        marketCap: 2800000000,
        dailyTransactions: 850000,
        activeWallets: 125000,
        avgGasPrice: 0.0001,
        blockTime: 1,
        tps: 100
      },
      {
        id: 'aptos',
        name: 'Aptos',
        symbol: 'APT',
        chainId: 1,
        icon: '🔴',
        color: '#FF5F5F',
        gradientFrom: '#FF5F5F',
        gradientTo: '#FF8A80',
        explorerUrl: 'https://explorer.aptoslabs.com',
        rpcUrl: 'https://fullnode.mainnet.aptoslabs.com/v1',
        isMainnet: true,
        category: 'layer1',
        description: 'Safe and scalable Layer 1 blockchain',
        tvl: 85000000,
        marketCap: 3200000000,
        dailyTransactions: 450000,
        activeWallets: 95000,
        avgGasPrice: 0.0008,
        blockTime: 4,
        tps: 160
      },
      {
        id: 'sui',
        name: 'Sui',
        symbol: 'SUI',
        chainId: 101,
        icon: '💧',
        color: '#4DA2FF',
        gradientFrom: '#4DA2FF',
        gradientTo: '#7DD3FC',
        explorerUrl: 'https://explorer.sui.io',
        rpcUrl: 'https://fullnode.mainnet.sui.io:443',
        isMainnet: true,
        category: 'layer1',
        description: 'Next-generation smart contract platform',
        tvl: 65000000,
        marketCap: 1800000000,
        dailyTransactions: 320000,
        activeWallets: 75000,
        avgGasPrice: 0.001,
        blockTime: 2.5,
        tps: 120
      },
      {
        id: 'arbitrum',
        name: 'Arbitrum One',
        symbol: 'ARB',
        chainId: 42161,
        icon: '🔵',
        color: '#28A0F0',
        gradientFrom: '#28A0F0',
        gradientTo: '#4FC3F7',
        explorerUrl: 'https://arbiscan.io',
        rpcUrl: 'https://arb1.arbitrum.io/rpc',
        isMainnet: true,
        category: 'layer2',
        description: 'Optimistic rollup for Ethereum',
        tvl: 2200000000,
        marketCap: 2100000000,
        dailyTransactions: 850000,
        activeWallets: 185000,
        avgGasPrice: 0.1,
        blockTime: 0.25,
        tps: 40
      },
      {
        id: 'optimism',
        name: 'Optimism',
        symbol: 'OP',
        chainId: 10,
        icon: '🔴',
        color: '#FF0420',
        gradientFrom: '#FF0420',
        gradientTo: '#FF6B6B',
        explorerUrl: 'https://optimistic.etherscan.io',
        rpcUrl: 'https://mainnet.optimism.io',
        isMainnet: true,
        category: 'layer2',
        description: 'Optimistic rollup scaling Ethereum',
        tvl: 950000000,
        marketCap: 1650000000,
        dailyTransactions: 520000,
        activeWallets: 145000,
        avgGasPrice: 0.001,
        blockTime: 2,
        tps: 35
      }
    ];

    networksData.forEach(network => {
      this.networks.set(network.id, network);
    });
  }

  private generateMockStats() {
    this.networks.forEach((network, networkId) => {
      const stats: NetworkStats = {
        networkId,
        totalWallets: Math.floor(Math.random() * 1000000) + 100000,
        totalVolume: Math.floor(Math.random() * 10000000000) + 1000000000,
        totalTransactions: Math.floor(Math.random() * 100000000) + 10000000,
        averageQualityScore: Math.floor(Math.random() * 40) + 60,
        flaggedWallets: Math.floor(Math.random() * 5000) + 100,
        whitelistedWallets: Math.floor(Math.random() * 10000) + 1000,
        walletTypes: {
          regular: Math.floor(Math.random() * 800000) + 200000,
          exchange: Math.floor(Math.random() * 50000) + 5000,
          contract: Math.floor(Math.random() * 100000) + 10000,
          whale: Math.floor(Math.random() * 5000) + 500,
          defi: Math.floor(Math.random() * 30000) + 3000,
          bridge: Math.floor(Math.random() * 2000) + 200,
          miner: Math.floor(Math.random() * 1000) + 100
        },
        riskDistribution: {
          low: Math.floor(Math.random() * 600000) + 400000,
          medium: Math.floor(Math.random() * 300000) + 100000,
          high: Math.floor(Math.random() * 80000) + 20000,
          critical: Math.floor(Math.random() * 10000) + 1000
        },
        topTokens: this.generateTopTokens(networkId),
        lastUpdate: new Date()
      };

      this.networkStats.set(networkId, stats);
    });
  }

  private generateTopTokens(networkId: string): string[] {
    const tokensByNetwork: Record<string, string[]> = {
      ethereum: ['USDC', 'USDT', 'WETH', 'DAI', 'LINK'],
      bsc: ['BUSD', 'CAKE', 'USDT', 'WBNB', 'XVS'],
      solana: ['USDC', 'RAY', 'SRM', 'FIDA', 'COPE'],
      polygon: ['USDC', 'WMATIC', 'USDT', 'DAI', 'AAVE'],
      near: ['USDC', 'WETH', 'DAI', 'AURORA', 'PLY'],
      aptos: ['USDC', 'WETH', 'BTC', 'USDT', 'APT'],
      sui: ['USDC', 'WETH', 'SUI', 'USDT', 'CETUS'],
      arbitrum: ['USDC', 'WETH', 'ARB', 'USDT', 'GMX'],
      optimism: ['USDC', 'WETH', 'OP', 'USDT', 'SNX']
    };

    return tokensByNetwork[networkId] || ['USDC', 'WETH', 'USDT', 'DAI', 'LINK'];
  }

  private calculateRankings() {
    const networks = Array.from(this.networks.values());
    
    this.rankings = networks.map((network, index) => {
      const stats = this.networkStats.get(network.id);
      
      // Calculate composite score based on multiple factors
      const tvlScore = Math.min((network.tvl || 0) / 1000000000 * 20, 25);
      const marketCapScore = Math.min((network.marketCap || 0) / 10000000000 * 20, 25);
      const activityScore = Math.min((network.dailyTransactions || 0) / 1000000 * 15, 20);
      const userScore = Math.min((network.activeWallets || 0) / 100000 * 15, 15);
      const techScore = Math.min((network.tps || 0) / 100 * 10, 10);
      const qualityScore = (stats?.averageQualityScore || 50) / 100 * 5;

      const totalScore = tvlScore + marketCapScore + activityScore + userScore + techScore + qualityScore;

      return {
        rank: index + 1,
        network,
        score: totalScore,
        change: Math.floor(Math.random() * 6) - 3, // -3 to +3
        metrics: {
          tvl: network.tvl || 0,
          marketCap: network.marketCap || 0,
          dailyVolume: (network.dailyTransactions || 0) * (network.avgGasPrice || 0),
          activeUsers: network.activeWallets || 0,
          developerActivity: Math.floor(Math.random() * 100),
          ecosystemGrowth: Math.floor(Math.random() * 100)
        }
      };
    });

    // Sort by score (highest first)
    this.rankings.sort((a, b) => b.score - a.score);
    
    // Update ranks
    this.rankings.forEach((ranking, index) => {
      ranking.rank = index + 1;
    });
  }

  /**
   * Get all available networks
   */
  getNetworks(): NetworkInfo[] {
    return Array.from(this.networks.values());
  }

  /**
   * Get network by ID
   */
  getNetwork(networkId: string): NetworkInfo | undefined {
    return this.networks.get(networkId);
  }

  /**
   * Get network statistics
   */
  getNetworkStats(networkId: string): NetworkStats | undefined {
    return this.networkStats.get(networkId);
  }

  /**
   * Get network rankings
   */
  getNetworkRankings(): NetworkRanking[] {
    return this.rankings;
  }

  /**
   * Get networks by category
   */
  getNetworksByCategory(category: NetworkInfo['category']): NetworkInfo[] {
    return Array.from(this.networks.values()).filter(network => network.category === category);
  }

  /**
   * Add new network (for future expansion)
   */
  addNetwork(network: NetworkInfo): void {
    this.networks.set(network.id, network);
    this.generateStatsForNetwork(network.id);
    this.calculateRankings();
    this.notifyListeners();
  }

  /**
   * Update network information
   */
  updateNetwork(networkId: string, updates: Partial<NetworkInfo>): void {
    const existing = this.networks.get(networkId);
    if (existing) {
      this.networks.set(networkId, { ...existing, ...updates });
      this.calculateRankings();
      this.notifyListeners();
    }
  }

  /**
   * Generate stats for a specific network
   */
  private generateStatsForNetwork(networkId: string): void {
    const network = this.networks.get(networkId);
    if (!network) return;

    const stats: NetworkStats = {
      networkId,
      totalWallets: Math.floor(Math.random() * 1000000) + 100000,
      totalVolume: Math.floor(Math.random() * 10000000000) + 1000000000,
      totalTransactions: Math.floor(Math.random() * 100000000) + 10000000,
      averageQualityScore: Math.floor(Math.random() * 40) + 60,
      flaggedWallets: Math.floor(Math.random() * 5000) + 100,
      whitelistedWallets: Math.floor(Math.random() * 10000) + 1000,
      walletTypes: {
        regular: Math.floor(Math.random() * 800000) + 200000,
        exchange: Math.floor(Math.random() * 50000) + 5000,
        contract: Math.floor(Math.random() * 100000) + 10000,
        whale: Math.floor(Math.random() * 5000) + 500,
        defi: Math.floor(Math.random() * 30000) + 3000,
        bridge: Math.floor(Math.random() * 2000) + 200,
        miner: Math.floor(Math.random() * 1000) + 100
      },
      riskDistribution: {
        low: Math.floor(Math.random() * 600000) + 400000,
        medium: Math.floor(Math.random() * 300000) + 100000,
        high: Math.floor(Math.random() * 80000) + 20000,
        critical: Math.floor(Math.random() * 10000) + 1000
      },
      topTokens: this.generateTopTokens(networkId),
      lastUpdate: new Date()
    };

    this.networkStats.set(networkId, stats);
  }

  /**
   * Subscribe to network updates
   */
  subscribe(callback: (networks: NetworkInfo[]) => void): () => void {
    this.listeners.push(callback);
    
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(): void {
    const networks = this.getNetworks();
    this.listeners.forEach(callback => {
      try {
        callback(networks);
      } catch (error) {
        console.error('Error notifying network listener:', error);
      }
    });
  }

  /**
   * Refresh all data
   */
  refresh(): void {
    this.generateMockStats();
    this.calculateRankings();
    this.notifyListeners();
  }
}

// Export singleton instance
export const networkService = new NetworkService();
export default networkService;
