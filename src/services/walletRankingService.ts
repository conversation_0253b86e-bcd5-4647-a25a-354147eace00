import { Node } from './neo4jService';
import { RiskScore, riskScoringService } from './riskScoringService';
import { getWatchListService } from './watchListService';
import apiService from './apiService';

export interface WalletMetrics {
  address: string;
  label?: string;
  // Quality metrics
  qualityScore: number; // 0-100 overall quality score
  riskScore: number; // 0-100 risk score (lower is better)
  reputationScore: number; // 0-100 reputation score

  // Activity metrics
  transactionCount: number;
  transactionVolume: number; // in ETH
  averageTransactionSize: number;
  activityFrequency: number; // transactions per day

  // Age and history
  walletAge: number; // days since first transaction
  firstTransactionDate: Date;
  lastTransactionDate: Date;

  // Network metrics
  connectionCount: number;
  uniqueCounterparties: number;
  networkInfluence: number; // 0-100 based on connections

  // Risk indicators
  riskFlags: string[];
  isWhitelisted: boolean;
  isFlagged: boolean;

  // Wallet type classification
  walletType: 'regular' | 'exchange' | 'contract' | 'whale' | 'miner' | 'defi' | 'bridge';

  // Performance indicators
  profitabilityScore?: number; // if available
  liquidityScore?: number; // based on transaction patterns

  // Social indicators
  hasVerifiedSocials?: boolean;
  socialScore?: number;
}

export interface RankingCriteria {
  metric: 'quality' | 'reputation' | 'volume' | 'activity' | 'age' | 'network' | 'safety';
  direction: 'asc' | 'desc';
  weight: number;
}

export interface WalletRanking {
  rank: number;
  wallet: WalletMetrics;
  score: number;
  change?: number; // position change from previous ranking
}

class WalletRankingService {
  private walletMetrics: Map<string, WalletMetrics> = new Map();
  private rankings: Map<string, WalletRanking[]> = new Map();
  private lastUpdate: Date = new Date();
  private updateInterval: NodeJS.Timeout | null = null;
  private listeners: ((stats: any) => void)[] = [];

  constructor() {
    this.generateMockData();
    this.startPeriodicUpdates();
  }

  /**
   * Generate mock wallet data for demonstration
   */
  private generateMockData() {
    const mockWallets: WalletMetrics[] = [
      {
        address: '******************************************',
        label: 'Ethereum Foundation',
        qualityScore: 95,
        riskScore: 5,
        reputationScore: 98,
        transactionCount: 15420,
        transactionVolume: 125000,
        averageTransactionSize: 8.1,
        activityFrequency: 12.5,
        walletAge: 2847,
        firstTransactionDate: new Date('2016-07-30'),
        lastTransactionDate: new Date('2024-06-19'),
        connectionCount: 8934,
        uniqueCounterparties: 3421,
        networkInfluence: 92,
        riskFlags: [],
        isWhitelisted: true,
        isFlagged: false,
        walletType: 'regular',
        profitabilityScore: 88,
        liquidityScore: 95,
        hasVerifiedSocials: true,
        socialScore: 96
      },
      {
        address: '0x8ba1f109551bD432803012645Hac136c22C57592',
        label: 'Binance Hot Wallet',
        qualityScore: 88,
        riskScore: 15,
        reputationScore: 85,
        transactionCount: 892341,
        transactionVolume: 2850000,
        averageTransactionSize: 3.2,
        activityFrequency: 1247.8,
        walletAge: 2156,
        firstTransactionDate: new Date('2018-06-15'),
        lastTransactionDate: new Date('2024-06-20'),
        connectionCount: 45672,
        uniqueCounterparties: 28934,
        networkInfluence: 98,
        riskFlags: [],
        isWhitelisted: true,
        isFlagged: false,
        walletType: 'exchange',
        profitabilityScore: 75,
        liquidityScore: 99,
        hasVerifiedSocials: true,
        socialScore: 82
      },
      {
        address: '******************************************',
        label: 'Uniswap V3 Router',
        qualityScore: 92,
        riskScore: 8,
        reputationScore: 94,
        transactionCount: 1245678,
        transactionVolume: 1850000,
        averageTransactionSize: 1.5,
        activityFrequency: 2156.3,
        walletAge: 1456,
        firstTransactionDate: new Date('2020-05-05'),
        lastTransactionDate: new Date('2024-06-20'),
        connectionCount: 67834,
        uniqueCounterparties: 45123,
        networkInfluence: 96,
        riskFlags: [],
        isWhitelisted: true,
        isFlagged: false,
        walletType: 'contract',
        liquidityScore: 98,
        hasVerifiedSocials: true,
        socialScore: 89
      },
      {
        address: '******************************************',
        label: 'Suspicious Mixer',
        qualityScore: 12,
        riskScore: 89,
        reputationScore: 8,
        transactionCount: 23456,
        transactionVolume: 45000,
        averageTransactionSize: 1.9,
        activityFrequency: 156.7,
        walletAge: 892,
        firstTransactionDate: new Date('2021-12-10'),
        lastTransactionDate: new Date('2024-06-18'),
        connectionCount: 12456,
        uniqueCounterparties: 8934,
        networkInfluence: 45,
        riskFlags: ['Money Laundering', 'Mixer Service', 'High Risk'],
        isWhitelisted: false,
        isFlagged: true,
        walletType: 'regular',
        profitabilityScore: 25,
        liquidityScore: 67
      },
      {
        address: '******************************************',
        label: 'Vitalik Buterin',
        qualityScore: 96,
        riskScore: 3,
        reputationScore: 99,
        transactionCount: 8934,
        transactionVolume: 85000,
        averageTransactionSize: 9.5,
        activityFrequency: 4.2,
        walletAge: 3124,
        firstTransactionDate: new Date('2015-08-07'),
        lastTransactionDate: new Date('2024-06-15'),
        connectionCount: 5678,
        uniqueCounterparties: 2341,
        networkInfluence: 87,
        riskFlags: [],
        isWhitelisted: true,
        isFlagged: false,
        walletType: 'whale',
        profitabilityScore: 94,
        liquidityScore: 78,
        hasVerifiedSocials: true,
        socialScore: 100
      }
    ];

    // Add more mock wallets with varying metrics
    for (let i = 0; i < 45; i++) {
      const mockWallet: WalletMetrics = {
        address: `0x${Math.random().toString(16).substr(2, 40)}`,
        label: Math.random() > 0.7 ? `Wallet ${i + 6}` : undefined,
        qualityScore: Math.floor(Math.random() * 100),
        riskScore: Math.floor(Math.random() * 100),
        reputationScore: Math.floor(Math.random() * 100),
        transactionCount: Math.floor(Math.random() * 100000),
        transactionVolume: Math.floor(Math.random() * 500000),
        averageTransactionSize: Math.random() * 50,
        activityFrequency: Math.random() * 1000,
        walletAge: Math.floor(Math.random() * 3000),
        firstTransactionDate: new Date(Date.now() - Math.random() * 3000 * 24 * 60 * 60 * 1000),
        lastTransactionDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        connectionCount: Math.floor(Math.random() * 50000),
        uniqueCounterparties: Math.floor(Math.random() * 20000),
        networkInfluence: Math.floor(Math.random() * 100),
        riskFlags: Math.random() > 0.8 ? ['Suspicious Activity'] : [],
        isWhitelisted: Math.random() > 0.9,
        isFlagged: Math.random() > 0.85,
        walletType: ['regular', 'exchange', 'contract', 'whale', 'miner', 'defi', 'bridge'][Math.floor(Math.random() * 7)] as any,
        profitabilityScore: Math.random() > 0.3 ? Math.floor(Math.random() * 100) : undefined,
        liquidityScore: Math.random() > 0.2 ? Math.floor(Math.random() * 100) : undefined,
        hasVerifiedSocials: Math.random() > 0.7,
        socialScore: Math.random() > 0.7 ? Math.floor(Math.random() * 100) : undefined
      };
      mockWallets.push(mockWallet);
    }

    // Store mock data
    mockWallets.forEach(wallet => {
      this.walletMetrics.set(wallet.address, wallet);
    });

    // Generate initial rankings
    this.updateRankings();
  }

  /**
   * Get top wallets by specific criteria
   */
  getTopWallets(criteria: string = 'quality', limit: number = 20): WalletRanking[] {
    const rankings = this.rankings.get(criteria) || [];
    return rankings.slice(0, limit);
  }

  /**
   * Get wallet metrics by address
   */
  getWalletMetrics(address: string): WalletMetrics | undefined {
    return this.walletMetrics.get(address);
  }

  /**
   * Update rankings based on current metrics
   */
  private updateRankings() {
    const wallets = Array.from(this.walletMetrics.values());

    // Quality ranking (best overall wallets)
    const qualityRanking = this.rankWallets(wallets, [
      { metric: 'quality', direction: 'desc', weight: 0.4 },
      { metric: 'reputation', direction: 'desc', weight: 0.3 },
      { metric: 'safety', direction: 'desc', weight: 0.3 }
    ]);
    this.rankings.set('quality', qualityRanking);

    // Volume ranking (highest transaction volume)
    const volumeRanking = this.rankWallets(wallets, [
      { metric: 'volume', direction: 'desc', weight: 1.0 }
    ]);
    this.rankings.set('volume', volumeRanking);

    // Activity ranking (most active wallets)
    const activityRanking = this.rankWallets(wallets, [
      { metric: 'activity', direction: 'desc', weight: 0.6 },
      { metric: 'volume', direction: 'desc', weight: 0.4 }
    ]);
    this.rankings.set('activity', activityRanking);

    // Safety ranking (lowest risk wallets)
    const safetyRanking = this.rankWallets(wallets, [
      { metric: 'safety', direction: 'desc', weight: 0.7 },
      { metric: 'reputation', direction: 'desc', weight: 0.3 }
    ]);
    this.rankings.set('safety', safetyRanking);

    // Network influence ranking
    const networkRanking = this.rankWallets(wallets, [
      { metric: 'network', direction: 'desc', weight: 1.0 }
    ]);
    this.rankings.set('network', networkRanking);

    this.lastUpdate = new Date();
  }

  /**
   * Rank wallets based on multiple criteria
   */
  private rankWallets(wallets: WalletMetrics[], criteria: RankingCriteria[]): WalletRanking[] {
    const scoredWallets = wallets.map(wallet => {
      let totalScore = 0;
      let totalWeight = 0;

      criteria.forEach(criterion => {
        const score = this.getMetricScore(wallet, criterion.metric);
        const adjustedScore = criterion.direction === 'desc' ? score : (100 - score);
        totalScore += adjustedScore * criterion.weight;
        totalWeight += criterion.weight;
      });

      return {
        wallet,
        score: totalWeight > 0 ? totalScore / totalWeight : 0
      };
    });

    // Sort by score (highest first)
    scoredWallets.sort((a, b) => b.score - a.score);

    // Create rankings with positions
    return scoredWallets.map((item, index) => ({
      rank: index + 1,
      wallet: item.wallet,
      score: item.score,
      change: Math.floor(Math.random() * 10) - 5 // Mock change data
    }));
  }

  /**
   * Get normalized score for a specific metric
   */
  private getMetricScore(wallet: WalletMetrics, metric: string): number {
    switch (metric) {
      case 'quality':
        return wallet.qualityScore;
      case 'reputation':
        return wallet.reputationScore;
      case 'volume':
        // Normalize volume to 0-100 scale
        return Math.min(wallet.transactionVolume / 10000, 100);
      case 'activity':
        return Math.min(wallet.activityFrequency / 10, 100);
      case 'age':
        return Math.min(wallet.walletAge / 30, 100);
      case 'network':
        return wallet.networkInfluence;
      case 'safety':
        return 100 - wallet.riskScore; // Invert risk score for safety
      default:
        return 0;
    }
  }

  /**
   * Get summary statistics
   */
  getStatistics() {
    const wallets = Array.from(this.walletMetrics.values());

    return {
      totalWallets: wallets.length,
      walletTypes: {
        regular: wallets.filter(w => w.walletType === 'regular').length,
        exchange: wallets.filter(w => w.walletType === 'exchange').length,
        contract: wallets.filter(w => w.walletType === 'contract').length,
        whale: wallets.filter(w => w.walletType === 'whale').length,
        defi: wallets.filter(w => w.walletType === 'defi').length,
        bridge: wallets.filter(w => w.walletType === 'bridge').length,
        miner: wallets.filter(w => w.walletType === 'miner').length
      },
      riskDistribution: {
        low: wallets.filter(w => w.riskScore < 25).length,
        medium: wallets.filter(w => w.riskScore >= 25 && w.riskScore < 50).length,
        high: wallets.filter(w => w.riskScore >= 50 && w.riskScore < 75).length,
        critical: wallets.filter(w => w.riskScore >= 75).length
      },
      flaggedWallets: wallets.filter(w => w.isFlagged).length,
      whitelistedWallets: wallets.filter(w => w.isWhitelisted).length,
      totalVolume: wallets.reduce((sum, w) => sum + w.transactionVolume, 0),
      totalTransactions: wallets.reduce((sum, w) => sum + w.transactionCount, 0),
      averageQualityScore: wallets.reduce((sum, w) => sum + w.qualityScore, 0) / wallets.length,
      lastUpdate: this.lastUpdate
    };
  }

  /**
   * Get available ranking categories
   */
  getRankingCategories() {
    return [
      { key: 'quality', label: 'Overall Quality', description: 'Best performing wallets overall' },
      { key: 'volume', label: 'Transaction Volume', description: 'Highest transaction volumes' },
      { key: 'activity', label: 'Activity Level', description: 'Most active wallets' },
      { key: 'safety', label: 'Safety Score', description: 'Lowest risk wallets' },
      { key: 'network', label: 'Network Influence', description: 'Most connected wallets' }
    ];
  }

  /**
   * Start periodic updates for real-time data
   */
  private startPeriodicUpdates() {
    // Update every 30 seconds
    this.updateInterval = setInterval(() => {
      this.updateRealTimeData();
    }, 30000);
  }

  /**
   * Update real-time data from various sources
   */
  private async updateRealTimeData() {
    try {
      // Integrate with watch list service
      if (typeof window !== 'undefined') {
        const watchListService = getWatchListService();
        const watchedWallets = watchListService.getWallets();

        // Update metrics for watched wallets
        watchedWallets.forEach(wallet => {
          const existing = this.walletMetrics.get(wallet.address);
          if (existing) {
            // Update with latest data from watch list
            existing.riskScore = wallet.riskScore || existing.riskScore;
            existing.lastTransactionDate = wallet.lastActivity || existing.lastTransactionDate;
            existing.balance = wallet.balance || existing.balance;
            existing.transactionCount = wallet.transactionCount || existing.transactionCount;
          }
        });
      }

      // Simulate some real-time updates
      this.simulateRealTimeUpdates();

      // Regenerate rankings
      this.updateRankings();

      // Notify listeners
      this.notifyListeners();

    } catch (error) {
      console.error('Failed to update real-time data:', error);
    }
  }

  /**
   * Simulate real-time updates for demonstration
   */
  private simulateRealTimeUpdates() {
    const wallets = Array.from(this.walletMetrics.values());

    // Randomly update a few wallets
    const walletsToUpdate = wallets.slice(0, Math.floor(Math.random() * 5) + 1);

    walletsToUpdate.forEach(wallet => {
      // Small random changes to simulate real activity
      wallet.transactionCount += Math.floor(Math.random() * 3);
      wallet.transactionVolume += Math.random() * 100;
      wallet.activityFrequency = Math.max(0, wallet.activityFrequency + (Math.random() - 0.5) * 2);
      wallet.lastTransactionDate = new Date();

      // Occasionally update risk scores
      if (Math.random() > 0.8) {
        wallet.riskScore = Math.max(0, Math.min(100, wallet.riskScore + (Math.random() - 0.5) * 10));
      }
    });
  }

  /**
   * Subscribe to real-time updates
   */
  subscribe(callback: (stats: any) => void): () => void {
    this.listeners.push(callback);

    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners of updates
   */
  private notifyListeners() {
    const stats = this.getStatistics();
    this.listeners.forEach(callback => {
      try {
        callback(stats);
      } catch (error) {
        console.error('Error notifying listener:', error);
      }
    });
  }

  /**
   * Integrate with existing wallet data
   */
  async integrateWalletData(walletAddress: string): Promise<WalletMetrics | null> {
    try {
      // Try to get wallet details from API
      const walletDetails = await apiService.getWalletDetails(walletAddress);

      if (walletDetails) {
        // Convert API data to our metrics format
        const metrics: WalletMetrics = {
          address: walletDetails.address,
          label: walletDetails.label,
          qualityScore: this.calculateQualityScore(walletDetails),
          riskScore: walletDetails.riskScore || 0,
          reputationScore: this.calculateReputationScore(walletDetails),
          transactionCount: walletDetails.transactionCount || 0,
          transactionVolume: parseFloat(walletDetails.balance || '0'),
          averageTransactionSize: walletDetails.transactionCount > 0 ?
            parseFloat(walletDetails.balance || '0') / walletDetails.transactionCount : 0,
          activityFrequency: this.calculateActivityFrequency(walletDetails),
          walletAge: this.calculateWalletAge(walletDetails),
          firstTransactionDate: new Date(walletDetails.firstTransaction || Date.now()),
          lastTransactionDate: new Date(walletDetails.lastTransaction || Date.now()),
          connectionCount: walletDetails.connections || 0,
          uniqueCounterparties: walletDetails.uniqueCounterparties || 0,
          networkInfluence: this.calculateNetworkInfluence(walletDetails),
          riskFlags: walletDetails.riskFlags || [],
          isWhitelisted: walletDetails.isWhitelisted || false,
          isFlagged: walletDetails.isFlagged || false,
          walletType: this.determineWalletType(walletDetails),
          hasVerifiedSocials: walletDetails.hasVerifiedSocials,
          socialScore: walletDetails.socialScore
        };

        this.walletMetrics.set(walletAddress, metrics);
        return metrics;
      }
    } catch (error) {
      console.error('Failed to integrate wallet data:', error);
    }

    return null;
  }

  /**
   * Helper methods for data conversion
   */
  private calculateQualityScore(walletDetails: any): number {
    // Simple quality calculation based on available data
    let score = 50; // Base score

    if (walletDetails.transactionCount > 1000) score += 20;
    if (walletDetails.balance && parseFloat(walletDetails.balance) > 100) score += 15;
    if (walletDetails.isWhitelisted) score += 15;
    if (walletDetails.hasVerifiedSocials) score += 10;
    if (walletDetails.riskScore && walletDetails.riskScore < 25) score += 10;

    return Math.min(100, score);
  }

  private calculateReputationScore(walletDetails: any): number {
    let score = 50;

    if (walletDetails.isWhitelisted) score += 30;
    if (walletDetails.hasVerifiedSocials) score += 20;
    if (walletDetails.riskScore && walletDetails.riskScore < 10) score += 20;
    if (walletDetails.isFlagged) score -= 40;

    return Math.max(0, Math.min(100, score));
  }

  private calculateActivityFrequency(walletDetails: any): number {
    // Estimate transactions per day
    const txCount = walletDetails.transactionCount || 0;
    const ageInDays = this.calculateWalletAge(walletDetails);
    return ageInDays > 0 ? txCount / ageInDays : 0;
  }

  private calculateWalletAge(walletDetails: any): number {
    if (walletDetails.firstTransaction) {
      const firstTx = new Date(walletDetails.firstTransaction);
      const now = new Date();
      return Math.floor((now.getTime() - firstTx.getTime()) / (1000 * 60 * 60 * 24));
    }
    return 0;
  }

  private calculateNetworkInfluence(walletDetails: any): number {
    const connections = walletDetails.connections || 0;
    const uniqueCounterparties = walletDetails.uniqueCounterparties || 0;

    // Simple influence calculation
    return Math.min(100, (connections / 100) * 50 + (uniqueCounterparties / 50) * 50);
  }

  private determineWalletType(walletDetails: any): WalletMetrics['walletType'] {
    if (walletDetails.tags?.includes('exchange')) return 'exchange';
    if (walletDetails.tags?.includes('contract')) return 'contract';
    if (walletDetails.tags?.includes('whale')) return 'whale';
    if (walletDetails.tags?.includes('defi')) return 'defi';
    if (walletDetails.tags?.includes('bridge')) return 'bridge';
    if (walletDetails.tags?.includes('miner')) return 'miner';

    // Determine by balance and activity
    const balance = parseFloat(walletDetails.balance || '0');
    if (balance > 10000) return 'whale';

    return 'regular';
  }

  /**
   * Cleanup method
   */
  destroy() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    this.listeners = [];
  }
}

// Export singleton instance
export const walletRankingService = new WalletRankingService();
export default walletRankingService;
