import { Node } from './neo4jService';

export interface WatchedWallet {
  id: string;
  address: string;
  label?: string;
  tags: string[];
  addedAt: Date;
  lastActivity?: Date;
  balance?: string;
  transactionCount?: number;
  riskScore?: number;
  alertsEnabled: boolean;
  customThresholds?: {
    balanceChange: number; // percentage
    transactionVolume: number; // ETH
    riskScoreIncrease: number; // points
  };
  notes?: string;
  lastChecked?: Date;
  alertHistory: WalletAlert[];
}

export interface WalletAlert {
  id: string;
  walletId: string;
  type: 'balance_change' | 'high_volume' | 'risk_increase' | 'suspicious_activity' | 'new_transaction';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  details?: any;
  timestamp: Date;
  acknowledged: boolean;
}

export interface WatchListStats {
  totalWallets: number;
  activeAlerts: number;
  highRiskWallets: number;
  totalValue: number; // in ETH
  recentActivity: number; // wallets with activity in last 24h
}

class WatchListService {
  private readonly STORAGE_KEY = 'crypto-bubble-watchlist';
  private readonly ALERTS_KEY = 'crypto-bubble-watchlist-alerts';
  private watchedWallets: WatchedWallet[] = [];
  private alerts: WalletAlert[] = [];
  private listeners: ((wallets: WatchedWallet[]) => void)[] = [];
  private alertListeners: ((alerts: WalletAlert[]) => void)[] = [];

  constructor() {
    this.loadFromStorage();
  }

  // Load data from localStorage
  private loadFromStorage() {
    // Skip on server-side
    if (typeof window === 'undefined') return;

    try {
      const savedWallets = localStorage.getItem(this.STORAGE_KEY);
      if (savedWallets) {
        this.watchedWallets = JSON.parse(savedWallets).map((w: any) => ({
          ...w,
          addedAt: new Date(w.addedAt),
          lastActivity: w.lastActivity ? new Date(w.lastActivity) : undefined,
          lastChecked: w.lastChecked ? new Date(w.lastChecked) : undefined,
          alertHistory: (w.alertHistory || []).map((a: any) => ({
            ...a,
            timestamp: new Date(a.timestamp)
          }))
        }));
      }

      const savedAlerts = localStorage.getItem(this.ALERTS_KEY);
      if (savedAlerts) {
        this.alerts = JSON.parse(savedAlerts).map((a: any) => ({
          ...a,
          timestamp: new Date(a.timestamp)
        }));
      }
    } catch (error) {
      console.error('Failed to load watch list from storage:', error);
    }
  }

  // Save data to localStorage
  private saveToStorage() {
    // Skip on server-side
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.watchedWallets));
      localStorage.setItem(this.ALERTS_KEY, JSON.stringify(this.alerts));
    } catch (error) {
      console.error('Failed to save watch list to storage:', error);
    }
  }

  // Notify listeners
  private notifyListeners() {
    this.listeners.forEach(listener => listener([...this.watchedWallets]));
  }

  private notifyAlertListeners() {
    this.alertListeners.forEach(listener => listener([...this.alerts]));
  }

  // Subscribe to watch list changes
  subscribe(listener: (wallets: WatchedWallet[]) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // Subscribe to alert changes
  subscribeToAlerts(listener: (alerts: WalletAlert[]) => void) {
    this.alertListeners.push(listener);
    return () => {
      this.alertListeners = this.alertListeners.filter(l => l !== listener);
    };
  }

  // Get all watched wallets
  getWatchedWallets(): WatchedWallet[] {
    return [...this.watchedWallets];
  }

  // Check if a wallet is being watched
  isWatched(address: string): boolean {
    return this.watchedWallets.some(wallet =>
      wallet.address.toLowerCase() === address.toLowerCase()
    );
  }

  // Get wallet by address
  getWalletByAddress(address: string): WatchedWallet | undefined {
    return this.watchedWallets.find(w => w.address.toLowerCase() === address.toLowerCase());
  }

  // Check if wallet is being watched
  isWatched(address: string): boolean {
    return this.watchedWallets.some(w => w.address.toLowerCase() === address.toLowerCase());
  }

  // Add wallet to watch list
  addWallet(walletData: Partial<WatchedWallet> & { address: string }): WatchedWallet {
    // Check if already exists
    const existing = this.getWalletByAddress(walletData.address);
    if (existing) {
      throw new Error('Wallet is already in watch list');
    }

    const newWallet: WatchedWallet = {
      id: Date.now().toString(),
      address: walletData.address,
      label: walletData.label,
      tags: walletData.tags || [],
      addedAt: new Date(),
      alertsEnabled: walletData.alertsEnabled ?? true,
      customThresholds: walletData.customThresholds || {
        balanceChange: 10, // 10% change
        transactionVolume: 100, // 100 ETH
        riskScoreIncrease: 20 // 20 points
      },
      notes: walletData.notes,
      alertHistory: []
    };

    this.watchedWallets.push(newWallet);
    this.saveToStorage();
    this.notifyListeners();

    return newWallet;
  }

  // Update wallet data
  updateWallet(id: string, updates: Partial<WatchedWallet>): WatchedWallet | null {
    const index = this.watchedWallets.findIndex(w => w.id === id);
    if (index === -1) return null;

    this.watchedWallets[index] = {
      ...this.watchedWallets[index],
      ...updates
    };

    this.saveToStorage();
    this.notifyListeners();

    return this.watchedWallets[index];
  }

  // Remove wallet from watch list
  removeWallet(id: string): boolean {
    const index = this.watchedWallets.findIndex(w => w.id === id);
    if (index === -1) return false;

    this.watchedWallets.splice(index, 1);

    // Remove related alerts
    this.alerts = this.alerts.filter(a => a.walletId !== id);

    this.saveToStorage();
    this.notifyListeners();
    this.notifyAlertListeners();

    return true;
  }

  // Toggle alerts for wallet
  toggleAlerts(id: string): boolean {
    const wallet = this.watchedWallets.find(w => w.id === id);
    if (!wallet) return false;

    wallet.alertsEnabled = !wallet.alertsEnabled;
    this.saveToStorage();
    this.notifyListeners();

    return wallet.alertsEnabled;
  }

  // Update wallet activity data (called when new data is available)
  updateWalletData(address: string, data: {
    balance?: string;
    transactionCount?: number;
    riskScore?: number;
    lastActivity?: Date;
  }): void {
    const wallet = this.getWalletByAddress(address);
    if (!wallet || !wallet.alertsEnabled) return;

    const oldBalance = parseFloat(wallet.balance || '0');
    const newBalance = parseFloat(data.balance || '0');
    const oldRiskScore = wallet.riskScore || 0;
    const newRiskScore = data.riskScore || 0;

    // Check for balance change alert
    if (wallet.balance && data.balance) {
      const balanceChange = Math.abs((newBalance - oldBalance) / oldBalance) * 100;
      if (balanceChange >= (wallet.customThresholds?.balanceChange || 10)) {
        this.createAlert(wallet.id, {
          type: 'balance_change',
          severity: balanceChange >= 50 ? 'high' : balanceChange >= 25 ? 'medium' : 'low',
          message: `Balance changed by ${balanceChange.toFixed(1)}%`,
          details: { oldBalance, newBalance, changePercent: balanceChange }
        });
      }
    }

    // Check for risk score increase
    if (newRiskScore > oldRiskScore) {
      const riskIncrease = newRiskScore - oldRiskScore;
      if (riskIncrease >= (wallet.customThresholds?.riskScoreIncrease || 20)) {
        this.createAlert(wallet.id, {
          type: 'risk_increase',
          severity: newRiskScore >= 70 ? 'critical' : newRiskScore >= 50 ? 'high' : 'medium',
          message: `Risk score increased by ${riskIncrease} points`,
          details: { oldRiskScore, newRiskScore, increase: riskIncrease }
        });
      }
    }

    // Update wallet data
    this.updateWallet(wallet.id, {
      ...data,
      lastChecked: new Date()
    });
  }

  // Create alert
  private createAlert(walletId: string, alertData: {
    type: WalletAlert['type'];
    severity: WalletAlert['severity'];
    message: string;
    details?: any;
  }): void {
    const alert: WalletAlert = {
      id: Date.now().toString(),
      walletId,
      ...alertData,
      timestamp: new Date(),
      acknowledged: false
    };

    this.alerts.unshift(alert); // Add to beginning

    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(0, 100);
    }

    // Add to wallet's alert history
    const wallet = this.watchedWallets.find(w => w.id === walletId);
    if (wallet) {
      wallet.alertHistory.unshift(alert);
      if (wallet.alertHistory.length > 20) {
        wallet.alertHistory = wallet.alertHistory.slice(0, 20);
      }
    }

    this.saveToStorage();
    this.notifyAlertListeners();
  }

  // Get all alerts
  getAlerts(): WalletAlert[] {
    return [...this.alerts];
  }

  // Get unacknowledged alerts
  getUnacknowledgedAlerts(): WalletAlert[] {
    return this.alerts.filter(a => !a.acknowledged);
  }

  // Acknowledge alert
  acknowledgeAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.saveToStorage();
      this.notifyAlertListeners();
    }
  }

  // Acknowledge all alerts
  acknowledgeAllAlerts(): void {
    this.alerts.forEach(alert => alert.acknowledged = true);
    this.saveToStorage();
    this.notifyAlertListeners();
  }

  // Get watch list statistics
  getStats(): WatchListStats {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    return {
      totalWallets: this.watchedWallets.length,
      activeAlerts: this.getUnacknowledgedAlerts().length,
      highRiskWallets: this.watchedWallets.filter(w => (w.riskScore || 0) >= 50).length,
      totalValue: this.watchedWallets.reduce((sum, w) => sum + parseFloat(w.balance || '0'), 0),
      recentActivity: this.watchedWallets.filter(w =>
        w.lastActivity && w.lastActivity > yesterday
      ).length
    };
  }

  // Export watch list data
  exportData(): string {
    return JSON.stringify({
      wallets: this.watchedWallets,
      alerts: this.alerts,
      exportedAt: new Date().toISOString()
    }, null, 2);
  }

  // Import watch list data
  importData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);

      if (data.wallets && Array.isArray(data.wallets)) {
        this.watchedWallets = data.wallets.map((w: any) => ({
          ...w,
          addedAt: new Date(w.addedAt),
          lastActivity: w.lastActivity ? new Date(w.lastActivity) : undefined,
          lastChecked: w.lastChecked ? new Date(w.lastChecked) : undefined,
          alertHistory: (w.alertHistory || []).map((a: any) => ({
            ...a,
            timestamp: new Date(a.timestamp)
          }))
        }));
      }

      if (data.alerts && Array.isArray(data.alerts)) {
        this.alerts = data.alerts.map((a: any) => ({
          ...a,
          timestamp: new Date(a.timestamp)
        }));
      }

      this.saveToStorage();
      this.notifyListeners();
      this.notifyAlertListeners();

      return true;
    } catch (error) {
      console.error('Failed to import watch list data:', error);
      return false;
    }
  }

  // Clear all data
  clearAll(): void {
    this.watchedWallets = [];
    this.alerts = [];
    this.saveToStorage();
    this.notifyListeners();
    this.notifyAlertListeners();
  }
}

// Lazy initialization to avoid SSR issues
let watchListServiceInstance: WatchListService | null = null;

export const getWatchListService = (): WatchListService => {
  // Only initialize on client-side
  if (typeof window === 'undefined') {
    // Return a mock service for SSR
    return {
      getWatchedWallets: () => [],
      getStats: () => ({
        totalWallets: 0,
        activeAlerts: 0,
        highRiskWallets: 0,
        totalValue: 0,
        recentActivity: 0
      }),
      subscribe: () => () => {},
      subscribeToAlerts: () => () => {},
      addWallet: () => ({ id: '', address: '', tags: [], addedAt: new Date(), alertsEnabled: false, alertHistory: [] }),
      removeWallet: () => false,
      toggleAlerts: () => false,
      getAlerts: () => [],
      acknowledgeAlert: () => {},
      acknowledgeAllAlerts: () => {}
    } as any;
  }

  if (!watchListServiceInstance) {
    watchListServiceInstance = new WatchListService();
  }
  return watchListServiceInstance;
};

// Export singleton instance - will be mock on server, real on client
export const watchListService = getWatchListService();
export default watchListService;
