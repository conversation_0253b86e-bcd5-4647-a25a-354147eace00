import axios, { AxiosRequestConfig, CancelTokenSource } from 'axios';
import { GraphData, Node } from './neo4jService';

// API base URL - can be set in environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// Create axios instance with default configuration
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// Service to interact with our back-end API
class ApiService {
  // Get wallet network data from the API with enhanced cancellation support
  async getWalletNetwork(address: string, depth: number = 2, abortSignal?: AbortSignal): Promise<GraphData> {
    console.log('🌐 ApiService.getWalletNetwork called with:', { address, depth, hasAbortSignal: !!abortSignal });

    try {
      // Create request config with proper AbortSignal support
      const config: AxiosRequestConfig = {
        params: { address, depth },
        timeout: 30000,
      };

      // Add AbortSignal if provided (for newer axios versions)
      if (abortSignal) {
        config.signal = abortSignal;
        console.log('🛡️ AbortSignal attached to request, aborted:', abortSignal.aborted);
      }

      console.log('📡 Making API request to:', `/wallets/network`);
      const response = await axiosInstance.get('/wallets/network', config);

      console.log('✅ API request completed successfully, data:', {
        nodeCount: response.data?.nodes?.length || 0,
        linkCount: response.data?.links?.length || 0
      });

      return response.data;
    } catch (error: any) {
      console.log('❌ API request failed:', {
        message: error.message,
        code: error.code,
        isCancel: axios.isCancel(error),
        isAborted: abortSignal?.aborted
      });

      if (axios.isCancel(error) || abortSignal?.aborted) {
        console.log('🛑 Request was cancelled');
        throw new Error('CANCELLED');
      }

      // If backend is not available, use mock data for testing cancellation
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        console.log('🔄 Backend not available, using mock data with simulated delay...');
        return this.getMockWalletNetwork(address, depth, abortSignal);
      }

      console.error('💥 Real API error:', error);
      throw error;
    }
  }

  // Mock data method with proper cancellation support for testing
  private async getMockWalletNetwork(address: string, depth: number = 2, abortSignal?: AbortSignal): Promise<GraphData> {
    console.log('🎭 Generating mock data with cancellation support...');

    // Simulate network delay with cancellation checks
    for (let i = 0; i < 30; i++) { // 3 second delay in 100ms chunks
      if (abortSignal?.aborted) {
        console.log('🛑 Mock request cancelled during delay');
        throw new Error('CANCELLED');
      }
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Final cancellation check before returning data
    if (abortSignal?.aborted) {
      console.log('🛑 Mock request cancelled before returning data');
      throw new Error('CANCELLED');
    }

    console.log('✅ Mock data generation completed');

    // Mock image generation helper
    const generateMockImage = (nodeId: string, nodeType: string): string | undefined => {
      const seed = nodeId.split('x')[1]?.substring(0, 8) || '0';
      const seedNum = parseInt(seed, 16) || 0;

      // Exchange logos for exchange nodes (using CoinGecko assets)
      const exchangeLogos = [
        'https://assets.coingecko.com/coins/images/825/small/bnb-icon2_2x.png',
        'https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png',
        'https://assets.coingecko.com/coins/images/9576/small/BUSD.png',
        'https://assets.coingecko.com/coins/images/2518/small/weth.png',
        'https://assets.coingecko.com/coins/images/1481/small/cosmos_hub.png',
        'https://assets.coingecko.com/coins/images/4128/small/coinbase-coin.png',
        'https://assets.coingecko.com/coins/images/3406/small/kucoin-shares.png',
        'https://assets.coingecko.com/coins/images/7598/small/wrapped_bitcoin_wbtc.png',
        'https://assets.coingecko.com/coins/images/4001/small/Fantom.png',
        'https://assets.coingecko.com/coins/images/5992/small/1inch-token.png'
      ];

      // Contract logos for contract nodes (using CoinGecko assets)
      const contractLogos = [
        'https://assets.coingecko.com/coins/images/12504/small/uniswap-uni.png',
        'https://assets.coingecko.com/coins/images/10775/small/COMP.png',
        'https://assets.coingecko.com/coins/images/12645/small/AAVE.png',
        'https://assets.coingecko.com/coins/images/1364/small/Mark_Maker.png',
        'https://assets.coingecko.com/coins/images/3406/small/SNX.png'
      ];

      // For exchanges, always use exchange logos (100% chance)
      if (nodeType === 'exchange') {
        return exchangeLogos[seedNum % exchangeLogos.length];
      }

      // For contracts, high chance of using contract logos
      if (nodeType === 'contract' && seedNum % 2 === 0) {
        return contractLogos[seedNum % contractLogos.length];
      }

      // For regular wallets, 60% chance of having an image
      if (nodeType === 'wallet' && Math.random() > 0.6) return undefined;

      // Avatar services for variety
      const avatarServices = [
        `https://api.dicebear.com/7.x/identicon/svg?seed=${nodeId}&backgroundColor=random`,
        `https://api.dicebear.com/7.x/shapes/svg?seed=${nodeId}&backgroundColor=random`,
        `https://robohash.org/${nodeId}?set=set1&size=100x100`,
        `https://robohash.org/${nodeId}?set=set2&size=100x100`
      ];

      return avatarServices[seedNum % avatarServices.length];
    };

    // Mock social profiles generation helper
    const generateMockSocials = (nodeId: string, nodeType: string): any => {
      const seed = nodeId.split('x')[1]?.substring(0, 8) || '0';
      const seedNum = parseInt(seed, 16) || 0;

      // 50% chance of having social profiles
      if (Math.random() > 0.5) return undefined;

      const mockUsernames = [
        'crypto_trader', 'defi_explorer', 'nft_collector', 'blockchain_dev', 'web3_builder',
        'ethereum_whale', 'bitcoin_hodler', 'dao_member', 'yield_farmer', 'metaverse_user'
      ];

      const username = mockUsernames[seedNum % mockUsernames.length];
      const profiles: any = {};

      if (seedNum % 2 === 0) profiles.twitter = `@${username}`;
      if (seedNum % 3 === 0) profiles.discord = `${username}#${String(seedNum % 9999).padStart(4, '0')}`;
      if (seedNum % 4 === 0) profiles.telegram = `@${username}`;
      if (seedNum % 5 === 0 && nodeType === 'contract') profiles.github = `github.com/${username}`;
      if (seedNum % 6 === 0) profiles.website = `https://${username}.crypto`;

      return Object.keys(profiles).length > 0 ? profiles : undefined;
    };

    // Generate mock network data
    const targetImageUrl = generateMockImage(address, 'wallet');
    const targetSocials = generateMockSocials(address, 'wallet');

    const nodes: Node[] = [
      {
        id: address,
        address: address,
        label: 'Search Target',
        balance: (Math.random() * 1000).toString(),
        transactionCount: Math.floor(Math.random() * 100),
        tags: ['Wallet'],
        x: 0,
        y: 0,
        imageUrl: targetImageUrl,
        hasImage: !!targetImageUrl,
        socialProfiles: targetSocials,
        hasVerifiedSocials: !!targetSocials,
        socialScore: targetSocials ? Object.keys(targetSocials).length * 20 : 0
      }
    ];

    // Add connected nodes
    for (let i = 0; i < 5 + Math.floor(Math.random() * 10); i++) {
      const connectedAddress = `0x${Math.random().toString(16).substr(2, 40)}`;
      const nodeType = i % 3 === 0 ? 'exchange' : i % 4 === 0 ? 'contract' : 'wallet';
      const tags = nodeType === 'exchange' ? ['Exchange'] :
                   nodeType === 'contract' ? ['Contract'] : ['Wallet'];
      const imageUrl = generateMockImage(connectedAddress, nodeType);
      const socialProfiles = generateMockSocials(connectedAddress, nodeType);
      const socialScore = socialProfiles ? Object.keys(socialProfiles).length * 15 : 0;

      nodes.push({
        id: connectedAddress,
        address: connectedAddress,
        label: `Connected ${i + 1}`,
        balance: (Math.random() * 500).toString(),
        transactionCount: Math.floor(Math.random() * 50),
        tags: tags,
        x: Math.random() * 400 - 200,
        y: Math.random() * 400 - 200,
        imageUrl: imageUrl,
        hasImage: !!imageUrl,
        socialProfiles: socialProfiles,
        hasVerifiedSocials: !!socialProfiles && socialScore > 20,
        socialScore: socialScore
      });
    }

    const links = nodes.slice(1).map((node, index) => ({
      source: address,
      target: node.id,
      value: Math.random() * 10,
      type: 'transaction'
    }));

    return { nodes, links };
  }

  // Get wallet details from the API with cancellation support
  async getWalletDetails(address: string, abortSignal?: AbortSignal): Promise<Node | null> {
    try {
      const response = await axios.get(`${API_BASE_URL}/wallets/${address}`, {
        signal: abortSignal,
        timeout: 15000 // 15 second timeout
      });
      return response.data;
    } catch (error) {
      if (axios.isCancel(error)) {
        console.log('Request cancelled:', error.message);
        throw new Error('CANCELLED');
      }
      console.error('Error fetching wallet details from API:', error);
      return null;
    }
  }

  // Search for wallets by partial address or label
  async searchWallets(query: string): Promise<Node[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/wallets/search`, {
        params: { q: query }
      });

      return response.data;
    } catch (error) {
      console.error('Error searching wallets from API:', error);
      return [];
    }
  }

  // Get wallet transaction history
  async getWalletTransactions(address: string, limit: number = 10): Promise<any[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/wallets/${address}/transactions`, {
        params: { limit }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching wallet transactions from API:', error);
      return [];
    }
  }
}

// Create a singleton instance
const apiService = new ApiService();

export default apiService;