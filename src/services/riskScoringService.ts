import { Node, <PERSON> } from './neo4jService';

export interface RiskScore {
  address: string;
  totalScore: number; // 0-100
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  factors: {
    phishing: number;
    mev: number;
    laundering: number;
    sanctions: number;
    scam: number;
    suspicious: number;
  };
  flags: string[];
  lastUpdated: Date;
}

export interface RiskFilterSettings {
  minRiskLevel: 'low' | 'medium' | 'high' | 'critical';
  showOnlyFlagged: boolean;
  hideWhitelisted: boolean;
  highlightSuspicious: boolean;
  autoHideClean: boolean;
  riskThreshold: number;
  filterTypes: {
    phishing: boolean;
    mev: boolean;
    laundering: boolean;
    sanctions: boolean;
    scam: boolean;
    suspicious: boolean;
  };
}

// Known risk patterns and indicators
const RISK_PATTERNS = {
  // Phishing indicators
  phishing: {
    patterns: [
      /fake.*wallet/i,
      /phish/i,
      /scam.*site/i,
      /fake.*exchange/i
    ],
    addressPatterns: [
      // Common phishing address patterns
      /^0x000000/,
      /^0x111111/,
      /^0xffffff/
    ]
  },

  // MEV bot indicators
  mev: {
    patterns: [
      /mev.*bot/i,
      /flashloan/i,
      /arbitrage/i,
      /sandwich/i
    ],
    transactionPatterns: {
      highFrequency: 100, // transactions per hour
      gasPrice: 1000000000000 // very high gas prices
    }
  },

  // Money laundering indicators
  laundering: {
    patterns: [
      /mixer/i,
      /tumbler/i,
      /tornado/i,
      /privacy.*coin/i
    ],
    behaviorPatterns: {
      rapidTransfers: 10, // many transfers in short time
      roundNumbers: true, // transfers in round numbers
      chainHopping: 3 // transfers across multiple chains
    }
  },

  // Sanctions indicators
  sanctions: {
    // OFAC sanctioned addresses (sample - in real app would be from official list)
    addresses: new Set([
      '0x8576acc5c05d6ce88f4e49bf65bdf0c62f91353c',
      '0xd882cfc20f52f2599d84b8e8d58c7fb62cfe344b'
    ])
  },

  // Scam indicators
  scam: {
    patterns: [
      /ponzi/i,
      /pyramid/i,
      /rug.*pull/i,
      /exit.*scam/i,
      /fake.*token/i
    ]
  }
};

// Whitelist of known safe addresses
const WHITELIST = new Set([
  '******************************************', // Example: Uniswap
  '******************************************', // Example: Compound
  // Add more known safe addresses
]);

export class RiskScoringService {
  private riskScores: Map<string, RiskScore> = new Map();
  private lastUpdate: Date = new Date();

  /**
   * Calculate risk score for a wallet address
   */
  calculateRiskScore(node: Node, edges: Link[] = []): RiskScore {
    const address = node.address;

    // Check if whitelisted
    if (WHITELIST.has(address)) {
      return this.createLowRiskScore(address);
    }

    // Initialize risk factors
    const factors = {
      phishing: 0,
      mev: 0,
      laundering: 0,
      sanctions: 0,
      scam: 0,
      suspicious: 0
    };

    const flags: string[] = [];

    // Check sanctions list
    if (RISK_PATTERNS.sanctions.addresses.has(address)) {
      factors.sanctions = 100;
      flags.push('OFAC Sanctioned Address');
    }

    // Analyze address patterns
    this.analyzeAddressPatterns(address, factors, flags);

    // Analyze node properties
    this.analyzeNodeProperties(node, factors, flags);

    // Analyze transaction patterns
    this.analyzeTransactionPatterns(node, edges, factors, flags);

    // Calculate total score (weighted average)
    const totalScore = this.calculateTotalScore(factors);
    const riskLevel = this.determineRiskLevel(totalScore);

    const riskScore: RiskScore = {
      address,
      totalScore,
      riskLevel,
      factors,
      flags,
      lastUpdated: new Date()
    };

    this.riskScores.set(address, riskScore);
    return riskScore;
  }

  /**
   * Analyze address patterns for risk indicators
   */
  private analyzeAddressPatterns(address: string, factors: RiskScore['factors'], flags: string[]) {
    // Check phishing address patterns
    for (const pattern of RISK_PATTERNS.phishing.addressPatterns) {
      if (pattern.test(address)) {
        factors.phishing += 30;
        flags.push('Suspicious address pattern');
        break;
      }
    }

    // Check for vanity addresses (could be impersonation)
    if (this.isVanityAddress(address)) {
      factors.suspicious += 20;
      flags.push('Vanity address (potential impersonation)');
    }
  }

  /**
   * Analyze node properties for risk indicators
   */
  private analyzeNodeProperties(node: Node, factors: RiskScore['factors'], flags: string[]) {
    const label = node.label?.toLowerCase() || '';

    // Check against known risk patterns
    Object.entries(RISK_PATTERNS).forEach(([riskType, config]) => {
      if ('patterns' in config) {
        for (const pattern of config.patterns) {
          if (pattern.test(label)) {
            factors[riskType as keyof RiskScore['factors']] += 40;
            flags.push(`${riskType.charAt(0).toUpperCase() + riskType.slice(1)} indicator in label`);
            break;
          }
        }
      }
    });

    // Analyze balance patterns
    if (node.balance) {
      const balance = parseFloat(node.balance);

      // Suspiciously round numbers
      if (this.isRoundNumber(balance)) {
        factors.laundering += 10;
        flags.push('Round number balance (potential structuring)');
      }

      // Unusually high balance for new address
      if (balance > 1000 && this.isNewAddress(node)) {
        factors.suspicious += 15;
        flags.push('High balance on new address');
      }
    }
  }

  /**
   * Analyze transaction patterns for risk indicators
   */
  private analyzeTransactionPatterns(node: Node, edges: Link[], factors: RiskScore['factors'], flags: string[]) {
    if (edges.length === 0) return;

    // High frequency trading (MEV bot indicator)
    const recentTransactions = edges.filter(edge =>
      this.isRecentTransaction(edge.timestamp)
    );

    if (recentTransactions.length > RISK_PATTERNS.mev.transactionPatterns.highFrequency) {
      factors.mev += 50;
      flags.push('High frequency trading pattern');
    }

    // Rapid sequential transfers (laundering indicator)
    const rapidTransfers = this.detectRapidTransfers(edges);
    if (rapidTransfers > RISK_PATTERNS.laundering.behaviorPatterns.rapidTransfers) {
      factors.laundering += 30;
      flags.push('Rapid sequential transfers detected');
    }

    // Interaction with known risky addresses
    const riskyInteractions = this.countRiskyInteractions(edges);
    if (riskyInteractions > 0) {
      factors.suspicious += Math.min(riskyInteractions * 10, 40);
      flags.push(`${riskyInteractions} interactions with flagged addresses`);
    }
  }

  /**
   * Calculate weighted total risk score
   */
  private calculateTotalScore(factors: RiskScore['factors']): number {
    const weights = {
      sanctions: 0.3,    // Highest weight
      phishing: 0.2,
      laundering: 0.2,
      scam: 0.15,
      mev: 0.1,
      suspicious: 0.05   // Lowest weight
    };

    let totalScore = 0;
    Object.entries(factors).forEach(([factor, score]) => {
      totalScore += score * weights[factor as keyof typeof weights];
    });

    return Math.min(Math.round(totalScore), 100);
  }

  /**
   * Determine risk level based on total score
   */
  private determineRiskLevel(totalScore: number): RiskScore['riskLevel'] {
    if (totalScore >= 75) return 'critical';
    if (totalScore >= 50) return 'high';
    if (totalScore >= 25) return 'medium';
    return 'low';
  }

  /**
   * Create a low-risk score for whitelisted addresses
   */
  private createLowRiskScore(address: string): RiskScore {
    return {
      address,
      totalScore: 0,
      riskLevel: 'low',
      factors: {
        phishing: 0,
        mev: 0,
        laundering: 0,
        sanctions: 0,
        scam: 0,
        suspicious: 0
      },
      flags: ['Whitelisted address'],
      lastUpdated: new Date()
    };
  }

  /**
   * Helper methods
   */
  private isVanityAddress(address: string): boolean {
    // Check for repeated patterns or obvious vanity
    const cleanAddress = address.toLowerCase().replace('0x', '');
    return /(.)\1{4,}/.test(cleanAddress) || // 5+ repeated characters
           /^(0{8,}|f{8,}|1{8,})/.test(cleanAddress); // Long sequences
  }

  private isRoundNumber(balance: number): boolean {
    return balance % 1000 === 0 && balance > 0;
  }

  private isNewAddress(node: Node): boolean {
    // Simple heuristic - in real app would check creation date
    return !node.label || node.label.includes('new') || node.label.includes('recent');
  }

  private isRecentTransaction(timestamp?: number): boolean {
    if (!timestamp) return false;
    const txTime = new Date(timestamp);
    const hourAgo = new Date(Date.now() - 60 * 60 * 1000);
    return txTime > hourAgo;
  }

  private detectRapidTransfers(edges: Link[]): number {
    // Count transfers within 10 minutes of each other
    const sortedEdges = edges.sort((a, b) =>
      (a.timestamp || 0) - (b.timestamp || 0)
    );

    let rapidCount = 0;
    for (let i = 1; i < sortedEdges.length; i++) {
      const timeDiff = (sortedEdges[i].timestamp || 0) - (sortedEdges[i-1].timestamp || 0);
      if (timeDiff < 10 * 60 * 1000) { // 10 minutes
        rapidCount++;
      }
    }

    return rapidCount;
  }

  private countRiskyInteractions(edges: Link[]): number {
    // Count interactions with addresses that have high risk scores
    return edges.filter(edge => {
      const targetAddress = edge.target;
      const targetRisk = this.riskScores.get(targetAddress);
      return targetRisk && targetRisk.totalScore > 50;
    }).length;
  }

  /**
   * Filter nodes based on risk settings
   */
  filterNodesByRisk(nodes: Node[], settings: RiskFilterSettings): Node[] {
    return nodes.filter(node => {
      const riskScore = this.riskScores.get(node.address);

      if (!riskScore) {
        // If no risk score, calculate it
        this.calculateRiskScore(node);
        return !settings.autoHideClean;
      }

      // Apply filters
      if (settings.hideWhitelisted && riskScore.flags.includes('Whitelisted address')) {
        return false;
      }

      if (settings.showOnlyFlagged && riskScore.flags.length === 0) {
        return false;
      }

      if (riskScore.totalScore < settings.riskThreshold) {
        return !settings.autoHideClean;
      }

      // Check risk level filter
      const riskLevels = ['low', 'medium', 'high', 'critical'];
      const minLevelIndex = riskLevels.indexOf(settings.minRiskLevel);
      const nodeLevelIndex = riskLevels.indexOf(riskScore.riskLevel);

      if (nodeLevelIndex < minLevelIndex) {
        return false;
      }

      // Check filter types
      const hasActiveRiskType = Object.entries(settings.filterTypes).some(([type, enabled]) => {
        if (!enabled) return false;
        return riskScore.factors[type as keyof RiskScore['factors']] > 0;
      });

      return hasActiveRiskType || riskScore.totalScore === 0;
    });
  }

  /**
   * Get risk score for an address
   */
  getRiskScore(address: string): RiskScore | undefined {
    return this.riskScores.get(address);
  }

  /**
   * Get all risk scores
   */
  getAllRiskScores(): Map<string, RiskScore> {
    return new Map(this.riskScores);
  }

  /**
   * Clear all risk scores (for reset)
   */
  clearRiskScores(): void {
    this.riskScores.clear();
  }
}

// Export singleton instance
export const riskScoringService = new RiskScoringService();
