import { Node, Link, GraphData } from '@/services/neo4jService';
import * as d3 from 'd3';

// Mock image URLs for different node types
const MOCK_IMAGES = {
  exchange: [
    'https://assets.coingecko.com/coins/images/825/small/bnb-icon2_2x.png',
    'https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png',
    'https://assets.coingecko.com/coins/images/9576/small/BUSD.png',
    'https://assets.coingecko.com/coins/images/2518/small/weth.png',
    'https://assets.coingecko.com/coins/images/1481/small/cosmos_hub.png',
    'https://assets.coingecko.com/coins/images/4128/small/coinbase-coin.png',
    'https://assets.coingecko.com/coins/images/3406/small/kucoin-shares.png',
    'https://assets.coingecko.com/coins/images/7598/small/wrapped_bitcoin_wbtc.png',
    'https://assets.coingecko.com/coins/images/4001/small/Fantom.png',
    'https://assets.coingecko.com/coins/images/5992/small/1inch-token.png'
  ],
  contract: [
    'https://assets.coingecko.com/coins/images/12504/small/uniswap-uni.png',
    'https://assets.coingecko.com/coins/images/10775/small/COMP.png',
    'https://assets.coingecko.com/coins/images/12645/small/AAVE.png',
    'https://assets.coingecko.com/coins/images/1364/small/Mark_Maker.png',
    'https://assets.coingecko.com/coins/images/3406/small/SNX.png'
  ],
  wallet: [
    'https://assets.coingecko.com/coins/images/279/small/ethereum.png',
    'https://assets.coingecko.com/coins/images/1/small/bitcoin.png',
    'https://assets.coingecko.com/coins/images/975/small/cardano.png',
    'https://assets.coingecko.com/coins/images/12171/small/polkadot.png',
    'https://assets.coingecko.com/coins/images/877/small/chainlink-new-logo.png'
  ],
  defi: [
    'https://assets.coingecko.com/coins/images/12632/small/pancakeswap-cake-logo_.png',
    'https://assets.coingecko.com/coins/images/12271/small/512x512_Logo_no_chop.png',
    'https://assets.coingecko.com/coins/images/12124/small/Curve.png',
    'https://assets.coingecko.com/coins/images/11849/small/yfi-192x192.png',
    'https://assets.coingecko.com/coins/images/13469/small/1inch-token.png'
  ]
};

// Generate mock avatar URLs using placeholder services
function generateMockAvatar(nodeId: string, nodeType: string): string | undefined {
  const seed = nodeId.split('-')[1] || '0';
  const seedNum = parseInt(seed) || 0;

  // For exchanges, always use exchange logos (100% chance)
  if (nodeType === 'exchange') {
    const exchangeImages = MOCK_IMAGES.exchange;
    return exchangeImages[seedNum % exchangeImages.length];
  }

  // For other specific node types, high chance of using appropriate logos
  if (nodeType in MOCK_IMAGES) {
    const typeImages = MOCK_IMAGES[nodeType as keyof typeof MOCK_IMAGES];
    if (seedNum % 2 === 0) { // 50% chance to use crypto logos for other typed nodes
      return typeImages[seedNum % typeImages.length];
    }
  }

  // For regular wallets, 70% chance of having an image
  if (nodeType === 'wallet' && Math.random() > 0.7) return undefined;

  // Use different avatar services for variety
  const avatarServices = [
    `https://api.dicebear.com/7.x/identicon/svg?seed=${nodeId}&backgroundColor=random`,
    `https://api.dicebear.com/7.x/shapes/svg?seed=${nodeId}&backgroundColor=random`,
    `https://api.dicebear.com/7.x/bottts/svg?seed=${nodeId}&backgroundColor=random`,
    `https://robohash.org/${nodeId}?set=set1&size=100x100`,
    `https://robohash.org/${nodeId}?set=set2&size=100x100`,
    `https://robohash.org/${nodeId}?set=set3&size=100x100`
  ];

  // Use generated avatars for wallets without specific logos
  return avatarServices[seedNum % avatarServices.length];
}

// Generate mock social media profiles
function generateMockSocialProfiles(nodeId: string, nodeType: string): any {
  const seed = nodeId.split('-')[1] || '0';
  const seedNum = parseInt(seed) || 0;

  // 60% chance of having social profiles
  if (Math.random() > 0.6) return undefined;

  const mockUsernames = [
    'cryptowhale', 'defimaster', 'blockchaindev', 'nftcollector', 'tradingpro',
    'ethereumfan', 'bitcoinhodler', 'web3builder', 'daocontributor', 'yieldfarm',
    'metaverse_explorer', 'smart_contract_dev', 'crypto_analyst', 'blockchain_researcher',
    'defi_yield_hunter', 'nft_artist', 'web3_designer', 'crypto_trader_pro'
  ];

  const mockDomains = [
    'cryptoportfolio.io', 'defitracker.com', 'nftgallery.art', 'blockchainblog.dev',
    'web3portfolio.xyz', 'cryptoanalysis.pro', 'daocontrib.org', 'yieldstrategy.fi'
  ];

  const username = mockUsernames[seedNum % mockUsernames.length];
  const domain = mockDomains[seedNum % mockDomains.length];

  const profiles: any = {};

  // Generate different social profiles based on node type and randomness
  if (seedNum % 2 === 0) profiles.twitter = `@${username}`;
  if (seedNum % 3 === 0) profiles.discord = `${username}#${String(seedNum).padStart(4, '0')}`;
  if (seedNum % 4 === 0) profiles.telegram = `@${username}`;
  if (seedNum % 5 === 0 && nodeType === 'contract') profiles.github = `github.com/${username}`;
  if (seedNum % 6 === 0) profiles.website = `https://${domain}`;
  if (seedNum % 7 === 0) profiles.linkedin = `linkedin.com/in/${username}`;
  if (seedNum % 8 === 0) profiles.medium = `@${username}`;
  if (seedNum % 9 === 0) profiles.reddit = `u/${username}`;

  return Object.keys(profiles).length > 0 ? profiles : undefined;
}

// Calculate social score based on social presence
function calculateSocialScore(socialProfiles: any): number {
  if (!socialProfiles) return 0;

  const weights = {
    twitter: 25,
    github: 20,
    linkedin: 15,
    website: 15,
    discord: 10,
    telegram: 10,
    medium: 3,
    reddit: 2
  };

  let score = 0;
  Object.keys(socialProfiles).forEach(platform => {
    score += weights[platform as keyof typeof weights] || 0;
  });

  return Math.min(score, 100);
}

// Generate mock data for testing and development
export function generateMockData(nodeCount: number = 15): GraphData {
  const nodes: any[] = [];
  const links: any[] = [];

  // Tạo layout dạng vòng tròn để khởi tạo vị trí
  const radius = 200;
  const centerX = 0;
  const centerY = 0;

  // Generate mock nodes
  for (let i = 0; i < nodeCount; i++) {
    const txCount = Math.floor(Math.random() * 1000);
    const balance = (Math.random() * 100).toFixed(4);

    // Determine node type
    const nodeType = i % 3 === 0 ? 'exchange' : i % 4 === 0 ? 'contract' : i % 5 === 0 ? 'defi' : 'wallet';
    const tags = nodeType === 'exchange' ? ['Exchange'] :
                 nodeType === 'contract' ? ['Contract'] :
                 nodeType === 'defi' ? ['DeFi'] : [];

    // Tính toán vị trí theo hình tròn với một chút ngẫu nhiên
    const angle = (i / nodeCount) * Math.PI * 2;
    const jitter = Math.random() * 40 - 20; // Thêm độ ngẫu nhiên ±20px
    const x = centerX + Math.cos(angle) * (radius + jitter);
    const y = centerY + Math.sin(angle) * (radius + jitter);

    // Generate mock image URL and social profiles
    const imageUrl = generateMockAvatar(`node-${i}`, nodeType);
    const socialProfiles = generateMockSocialProfiles(`node-${i}`, nodeType);
    const socialScore = calculateSocialScore(socialProfiles);

    nodes.push({
      id: `node-${i}`,
      address: `0x${Math.random().toString(16).substr(2, 40)}`,
      label: i === 0 ? 'Main Wallet' : i % 5 === 0 ? `${nodeType.charAt(0).toUpperCase() + nodeType.slice(1)} ${i}` : undefined,
      balance: balance,
      transactionCount: txCount,
      tags: tags,
      size: calculateNodeSize(txCount),
      color: getNodeColor(i),
      // Image properties
      imageUrl: imageUrl,
      avatar: imageUrl,
      hasImage: !!imageUrl,
      imageLoaded: false,
      imageError: false,
      // Social media properties
      socialProfiles: socialProfiles,
      hasVerifiedSocials: !!socialProfiles && socialScore > 30,
      socialScore: socialScore,
      // Thêm vị trí và thuộc tính kéo thả
      x: x,
      y: y,
      targetX: x,
      targetY: y,
      vx: 0,
      vy: 0
    });
  }

  // Generate mock links - ưu tiên kết nối giữa các node gần nhau
  const linkCount = Math.min(nodeCount * 2, 40); // Prevent too many links
  for (let i = 0; i < linkCount; i++) {
    const source = Math.floor(Math.random() * nodeCount);

    // Tìm các node gần nhau để kết nối, tạo kết nối hợp lý hơn
    let potentialTargets = [];
    for (let j = 0; j < nodeCount; j++) {
      if (j !== source) {
        const distance = Math.sqrt(
          Math.pow(nodes[source].x - nodes[j].x, 2) +
          Math.pow(nodes[source].y - nodes[j].y, 2)
        );
        potentialTargets.push({ index: j, distance: distance });
      }
    }

    // Sắp xếp theo khoảng cách và chọn ngẫu nhiên trong 5 node gần nhất
    potentialTargets.sort((a, b) => a.distance - b.distance);
    const nearestTargets = potentialTargets.slice(0, 5);
    const target = nearestTargets[Math.floor(Math.random() * nearestTargets.length)].index;

    links.push({
      source: `node-${source}`,
      target: `node-${target}`,
      value: Math.floor(Math.random() * 10) + 1
    });
  }

  return { nodes, links };
}

// Helper function to calculate node size
function calculateNodeSize(txCount: number): number {
  // Min size is 5, max size is 30
  const minSize = 5;
  const maxSize = 30;

  // Log scale for better visualization
  if (txCount === 0) return minSize;
  const logSize = Math.log10(txCount + 1) * 5;

  return Math.max(minSize, Math.min(maxSize, logSize));
}

// Helper function to get node color
function getNodeColor(index: number): string {
  const colors = [
    '#3B82F6', // primary blue
    '#10B981', // secondary green
    '#8B5CF6', // accent purple
    '#F59E0B', // amber
    '#EF4444'  // red
  ];

  return colors[index % colors.length];
}

// Truncate addresses for display
export function truncateAddress(address: string): string {
  if (!address) return '';
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

// Generate a color scale for nodes based on properties
export function generateColorScale(nodes: Node[]): d3.ScaleOrdinal<string, string> {
  const categories = Array.from(new Set(nodes.map(node => node.tags?.[0] || 'Unknown')));

  // Custom color scheme
  const colors = [
    '#3B82F6', // primary blue
    '#10B981', // secondary green
    '#8B5CF6', // accent purple
    '#F59E0B', // amber
    '#EF4444', // red
    '#EC4899', // pink
    '#6366F1'  // indigo
  ];

  return d3.scaleOrdinal<string>()
    .domain(categories)
    .range(colors);
}

// Format balance value for display
export function formatBalance(balance: string | undefined): string {
  if (!balance) return '0 ETH';

  const balanceNum = parseFloat(balance);
  if (isNaN(balanceNum)) return '0 ETH';

  // Format with appropriate precision
  if (balanceNum < 0.001) {
    return `${balanceNum.toFixed(6)} ETH`;
  } else if (balanceNum < 1) {
    return `${balanceNum.toFixed(4)} ETH`;
  } else if (balanceNum < 1000) {
    return `${balanceNum.toFixed(2)} ETH`;
  } else {
    return `${(balanceNum / 1000).toFixed(2)}k ETH`;
  }
}

// Add highlights to nodes and their connections
export function highlightConnections(graphData: GraphData, nodeId: string): GraphData {
  const { nodes, links } = graphData;

  // Find all nodes directly connected to the selected node
  const connectedNodeIds = new Set<string>();
  links.forEach(link => {
    if (link.source === nodeId) {
      connectedNodeIds.add(link.target);
    } else if (link.target === nodeId) {
      connectedNodeIds.add(link.source);
    }
  });

  // Update node highlighting
  const updatedNodes = nodes.map(node => ({
    ...node,
    highlighted: node.id === nodeId || connectedNodeIds.has(node.id)
  }));

  // Update link highlighting
  const updatedLinks = links.map(link => ({
    ...link,
    highlighted: link.source === nodeId || link.target === nodeId
  }));

  return { nodes: updatedNodes, links: updatedLinks };
}