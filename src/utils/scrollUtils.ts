/**
 * Utility functions for smooth scrolling and dropdown positioning
 */

export interface ScrollToDropdownOptions {
  /** Element reference to scroll to */
  element: HTMLElement;
  /** Extra offset above the dropdown (default: 100px) */
  offset?: number;
  /** Estimated dropdown height (default: 400px) */
  dropdownHeight?: number;
  /** Scroll behavior (default: 'smooth') */
  behavior?: ScrollBehavior;
  /** Delay before scrolling (default: 100ms) */
  delay?: number;
}

/**
 * Smoothly scrolls the page to ensure a dropdown is fully visible
 */
export const scrollToDropdown = ({
  element,
  offset = 100,
  dropdownHeight = 400,
  behavior = 'smooth',
  delay = 100
}: ScrollToDropdownOptions): Promise<void> => {
  return new Promise((resolve) => {
    const performScroll = () => {
      if (!element) {
        resolve();
        return;
      }

      const rect = element.getBoundingClientRect();
      const viewportHeight = window.innerHeight;

      // Check if dropdown would be cut off at bottom of viewport
      const dropdownBottom = rect.bottom + dropdownHeight;
      const needsScroll = dropdownBottom > viewportHeight;

      if (needsScroll) {
        // Calculate scroll position to show full dropdown with padding
        const targetScrollY = window.scrollY + (dropdownBottom - viewportHeight) + offset;

        // Smooth scroll to target position
        window.scrollTo({
          top: Math.max(0, targetScrollY),
          behavior
        });
      }

      resolve();
    };

    if (delay > 0) {
      setTimeout(performScroll, delay);
    } else {
      performScroll();
    }
  });
};

/**
 * Checks if an element would be cut off by the viewport when a dropdown opens
 */
export const isDropdownCutOff = (
  element: HTMLElement,
  dropdownHeight: number = 400
): boolean => {
  if (!element) return false;

  const rect = element.getBoundingClientRect();
  const viewportHeight = window.innerHeight;
  const dropdownBottom = rect.bottom + dropdownHeight;

  return dropdownBottom > viewportHeight;
};

/**
 * Gets the optimal scroll position for a dropdown
 */
export const getOptimalScrollPosition = (
  element: HTMLElement,
  dropdownHeight: number = 400,
  offset: number = 100
): number => {
  if (!element) return 0;

  const rect = element.getBoundingClientRect();
  const viewportHeight = window.innerHeight;
  const dropdownBottom = rect.bottom + dropdownHeight;

  if (dropdownBottom > viewportHeight) {
    return window.scrollY + (dropdownBottom - viewportHeight) + offset;
  }

  return window.scrollY;
};

/**
 * Smoothly scrolls to a specific element with options
 */
export const scrollToElement = (
  element: HTMLElement,
  options: {
    offset?: number;
    behavior?: ScrollBehavior;
    block?: ScrollLogicalPosition;
    inline?: ScrollLogicalPosition;
  } = {}
): void => {
  if (!element) return;

  const {
    offset = 0,
    behavior = 'smooth',
    block = 'start',
    inline = 'nearest'
  } = options;

  if (offset !== 0) {
    // Custom scroll with offset
    const rect = element.getBoundingClientRect();
    const targetY = window.scrollY + rect.top - offset;

    window.scrollTo({
      top: Math.max(0, targetY),
      behavior
    });
  } else {
    // Use native scrollIntoView
    element.scrollIntoView({
      behavior,
      block,
      inline
    });
  }
};

/**
 * Debounced scroll handler to prevent excessive scroll events
 */
export const createDebouncedScrollHandler = (
  handler: () => void,
  delay: number = 100
): (() => void) => {
  let timeoutId: NodeJS.Timeout;

  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(handler, delay);
  };
};

/**
 * Checks if the user has scrolled to the bottom of an element
 */
export const isScrolledToBottom = (
  element: HTMLElement,
  threshold: number = 10
): boolean => {
  if (!element) return false;

  const { scrollTop, scrollHeight, clientHeight } = element;
  return scrollTop + clientHeight >= scrollHeight - threshold;
};

/**
 * Checks if the user has scrolled to the top of an element
 */
export const isScrolledToTop = (
  element: HTMLElement,
  threshold: number = 10
): boolean => {
  if (!element) return false;

  return element.scrollTop <= threshold;
};

/**
 * Gets the scroll percentage of an element (0-100)
 */
export const getScrollPercentage = (element: HTMLElement): number => {
  if (!element) return 0;

  const { scrollTop, scrollHeight, clientHeight } = element;
  const maxScroll = scrollHeight - clientHeight;

  if (maxScroll <= 0) return 0;

  return Math.round((scrollTop / maxScroll) * 100);
};

/**
 * Scrolls to position an element just below a fixed header/banner
 */
export const scrollToElementBelowFixedHeader = (
  element: HTMLElement,
  options: {
    /** Height of the fixed header (default: 80px for top-20) */
    headerHeight?: number;
    /** Additional offset below the header (default: 10px) */
    offset?: number;
    /** Scroll behavior (default: 'smooth') */
    behavior?: ScrollBehavior;
    /** Delay before scrolling (default: 300ms) */
    delay?: number;
  } = {}
): Promise<void> => {
  return new Promise((resolve) => {
    const {
      headerHeight = 80, // top-20 = 5rem = 80px
      offset = 10,
      behavior = 'smooth',
      delay = 300
    } = options;

    const performScroll = () => {
      if (!element) {
        resolve();
        return;
      }

      const rect = element.getBoundingClientRect();
      const targetY = window.scrollY + rect.top - headerHeight - offset;

      window.scrollTo({
        top: Math.max(0, targetY),
        behavior
      });

      resolve();
    };

    if (delay > 0) {
      setTimeout(performScroll, delay);
    } else {
      performScroll();
    }
  });
};

/**
 * Scrolls to make an element's top edge touch a fixed banner
 */
export const scrollToTouchFixedBanner = (
  element: HTMLElement,
  bannerHeight: number = 120, // Banner height including padding
  options: {
    behavior?: ScrollBehavior;
    delay?: number;
    extraOffset?: number;
  } = {}
): Promise<void> => {
  return new Promise((resolve) => {
    const {
      behavior = 'smooth',
      delay = 300,
      extraOffset = 5 // Small gap to avoid exact touching
    } = options;

    const performScroll = () => {
      if (!element) {
        resolve();
        return;
      }

      const rect = element.getBoundingClientRect();
      // Calculate position to make element top touch banner bottom
      const targetY = window.scrollY + rect.top - bannerHeight - extraOffset;

      window.scrollTo({
        top: Math.max(0, targetY),
        behavior
      });

      resolve();
    };

    if (delay > 0) {
      setTimeout(performScroll, delay);
    } else {
      performScroll();
    }
  });
};
