/**
 * Performance Manager for Crypto Bubble Map Application
 * Handles device detection, performance profiling, and optimization settings
 */

export interface PerformanceConfig {
  renderingQuality: 'low' | 'medium' | 'high' | 'ultra';
  maxNodes: number;
  frameRateLimit: 30 | 60 | 120;
  particleDensity: 'off' | 'low' | 'medium' | 'high';
  realTimeUpdateInterval: number; // milliseconds
  enableShadows: boolean;
  enableGlow: boolean;
  enableParticles: boolean;
  enableAnimations: boolean;
  lodEnabled: boolean;
  cullingEnabled: boolean;
  dataCacheLimit: number; // MB
  bufferSize: number;
  autoOptimize: boolean;
}

export interface DeviceCapabilities {
  gpu: 'low' | 'medium' | 'high' | 'ultra';
  memory: number; // GB
  screenResolution: { width: number; height: number };
  pixelRatio: number;
  browserScore: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

export interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  memoryUsage: number;
  gpuLoad: number;
  nodeCount: number;
  renderTime: number;
  lastUpdate: number;
}

export class PerformanceManager {
  private config: PerformanceConfig;
  private capabilities: DeviceCapabilities;
  private metrics: PerformanceMetrics;
  private fpsHistory: number[] = [];
  private frameTimeHistory: number[] = [];
  private lastFrameTime: number = 0;
  private frameCount: number = 0;
  private performanceObserver: PerformanceObserver | null = null;

  constructor() {
    this.capabilities = this.detectDeviceCapabilities();
    this.config = this.getOptimalConfig();
    this.metrics = this.initializeMetrics();
    this.setupPerformanceMonitoring();
  }

  /**
   * Detect device capabilities for automatic optimization
   */
  private detectDeviceCapabilities(): DeviceCapabilities {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      // Return default capabilities for SSR
      return {
        gpu: 'medium',
        memory: 4,
        screenResolution: { width: 1920, height: 1080 },
        pixelRatio: 1,
        browserScore: 70,
        isMobile: false,
        isTablet: false,
        isDesktop: true
      };
    }

    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

    // GPU Detection
    let gpuTier: 'low' | 'medium' | 'high' | 'ultra' = 'medium';
    if (gl && gl instanceof WebGLRenderingContext) {
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      if (debugInfo) {
        const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
        gpuTier = this.classifyGPU(renderer);
      }
    }

    // Memory Detection (approximate)
    const memory = (navigator as any).deviceMemory || this.estimateMemory();

    // Screen Detection
    const screenResolution = {
      width: window.screen.width,
      height: window.screen.height
    };
    const pixelRatio = window.devicePixelRatio || 1;

    // Device Type Detection
    const userAgent = navigator.userAgent;
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    const isTablet = /iPad|Android(?=.*\bMobile\b)(?=.*\bTablet\b)|Android(?=.*\bTablet\b)/i.test(userAgent);
    const isDesktop = !isMobile && !isTablet;

    // Browser Performance Score
    const browserScore = this.calculateBrowserScore();

    return {
      gpu: gpuTier,
      memory,
      screenResolution,
      pixelRatio,
      browserScore,
      isMobile,
      isTablet,
      isDesktop
    };
  }

  /**
   * Classify GPU performance based on renderer string
   */
  private classifyGPU(renderer: string): 'low' | 'medium' | 'high' | 'ultra' {
    const rendererLower = renderer.toLowerCase();

    // Ultra-high performance GPUs
    if (rendererLower.includes('rtx 40') || rendererLower.includes('rtx 30') ||
        rendererLower.includes('rx 7') || rendererLower.includes('rx 6')) {
      return 'ultra';
    }

    // High performance GPUs
    if (rendererLower.includes('rtx') || rendererLower.includes('gtx 16') ||
        rendererLower.includes('gtx 20') || rendererLower.includes('rx 5') ||
        rendererLower.includes('vega')) {
      return 'high';
    }

    // Medium performance GPUs
    if (rendererLower.includes('gtx') || rendererLower.includes('rx ') ||
        rendererLower.includes('radeon') || rendererLower.includes('iris')) {
      return 'medium';
    }

    // Low performance or integrated GPUs
    return 'low';
  }

  /**
   * Estimate device memory if not available
   */
  private estimateMemory(): number {
    // Rough estimation based on screen resolution and device type
    const totalPixels = window.screen.width * window.screen.height;
    if (totalPixels > 2073600) return 8; // 1920x1080+
    if (totalPixels > 921600) return 4;  // 1280x720+
    return 2; // Lower resolutions
  }

  /**
   * Calculate browser performance score
   */
  private calculateBrowserScore(): number {
    let score = 50; // Base score

    // Browser type scoring
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Chrome')) score += 20;
    else if (userAgent.includes('Firefox')) score += 15;
    else if (userAgent.includes('Safari')) score += 10;
    else if (userAgent.includes('Edge')) score += 15;

    // Hardware concurrency
    score += Math.min((navigator.hardwareConcurrency || 2) * 5, 20);

    // WebGL support
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
    if (gl) score += 15;

    return Math.min(score, 100);
  }

  /**
   * Get optimal configuration based on device capabilities
   */
  private getOptimalConfig(): PerformanceConfig {
    const { gpu, memory, isMobile, browserScore } = this.capabilities;

    // Base configuration
    let config: PerformanceConfig = {
      renderingQuality: 'medium',
      maxNodes: 100,
      frameRateLimit: 60,
      particleDensity: 'medium',
      realTimeUpdateInterval: 5000,
      enableShadows: true,
      enableGlow: true,
      enableParticles: true,
      enableAnimations: true,
      lodEnabled: true,
      cullingEnabled: true,
      dataCacheLimit: 50,
      bufferSize: 1000,
      autoOptimize: true
    };

    // Adjust based on GPU performance
    switch (gpu) {
      case 'ultra':
        config.renderingQuality = 'ultra';
        config.maxNodes = 500;
        config.frameRateLimit = 120;
        config.particleDensity = 'high';
        config.dataCacheLimit = 200;
        break;
      case 'high':
        config.renderingQuality = 'high';
        config.maxNodes = 300;
        config.frameRateLimit = 60;
        config.particleDensity = 'high';
        config.dataCacheLimit = 100;
        break;
      case 'medium':
        config.renderingQuality = 'medium';
        config.maxNodes = 150;
        config.frameRateLimit = 60;
        config.particleDensity = 'medium';
        break;
      case 'low':
        config.renderingQuality = 'low';
        config.maxNodes = 50;
        config.frameRateLimit = 30;
        config.particleDensity = 'low';
        config.enableShadows = false;
        config.enableGlow = false;
        config.dataCacheLimit = 25;
        break;
    }

    // Mobile optimizations
    if (isMobile) {
      config.maxNodes = Math.min(config.maxNodes, 100);
      config.frameRateLimit = Math.min(config.frameRateLimit, 60) as 30 | 60 | 120;
      config.particleDensity = config.particleDensity === 'high' ? 'medium' : config.particleDensity;
      config.enableShadows = false;
      config.realTimeUpdateInterval = Math.max(config.realTimeUpdateInterval, 10000);
    }

    // Memory-based adjustments
    if (memory < 4) {
      config.maxNodes = Math.min(config.maxNodes, 75);
      config.dataCacheLimit = Math.min(config.dataCacheLimit, 25);
      config.bufferSize = Math.min(config.bufferSize, 500);
    }

    // Browser performance adjustments
    if (browserScore < 60) {
      config.frameRateLimit = 30;
      config.enableParticles = false;
      config.particleDensity = 'off';
    }

    return config;
  }

  /**
   * Initialize performance metrics
   */
  private initializeMetrics(): PerformanceMetrics {
    return {
      fps: 0,
      frameTime: 0,
      memoryUsage: 0,
      gpuLoad: 0,
      nodeCount: 0,
      renderTime: 0,
      lastUpdate: Date.now()
    };
  }

  /**
   * Setup performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    // Only setup monitoring in browser environment
    if (typeof window === 'undefined' || typeof performance === 'undefined') {
      return;
    }

    // FPS monitoring
    const measureFPS = () => {
      const now = performance.now();
      if (this.lastFrameTime > 0) {
        const frameTime = now - this.lastFrameTime;
        const fps = 1000 / frameTime;

        this.fpsHistory.push(fps);
        this.frameTimeHistory.push(frameTime);

        // Keep only last 60 frames
        if (this.fpsHistory.length > 60) {
          this.fpsHistory.shift();
          this.frameTimeHistory.shift();
        }

        // Update metrics
        this.metrics.fps = this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length;
        this.metrics.frameTime = this.frameTimeHistory.reduce((a, b) => a + b, 0) / this.frameTimeHistory.length;
      }
      this.lastFrameTime = now;
      requestAnimationFrame(measureFPS);
    };
    requestAnimationFrame(measureFPS);

    // Memory monitoring
    if ('memory' in performance) {
      setInterval(() => {
        const memInfo = (performance as any).memory;
        this.metrics.memoryUsage = memInfo.usedJSHeapSize / (1024 * 1024); // MB
      }, 1000);
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): PerformanceConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfigToStorage();
  }

  /**
   * Get device capabilities
   */
  getCapabilities(): DeviceCapabilities {
    return { ...this.capabilities };
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Update performance metrics
   */
  updateMetrics(updates: Partial<PerformanceMetrics>): void {
    this.metrics = { ...this.metrics, ...updates, lastUpdate: Date.now() };
  }

  /**
   * Check if performance optimization is needed
   */
  shouldOptimize(): boolean {
    const { fps, frameTime } = this.metrics;
    const targetFPS = this.config.frameRateLimit * 0.8; // 80% of target

    return fps < targetFPS || frameTime > (1000 / targetFPS);
  }

  /**
   * Auto-optimize performance based on current metrics
   */
  autoOptimize(): void {
    if (!this.config.autoOptimize || !this.shouldOptimize()) return;

    const currentFPS = this.metrics.fps;
    const targetFPS = this.config.frameRateLimit;

    if (currentFPS < targetFPS * 0.6) {
      // Severe performance issues - aggressive optimization
      this.updateConfig({
        renderingQuality: 'low',
        maxNodes: Math.max(this.config.maxNodes * 0.5, 25),
        particleDensity: 'off',
        enableShadows: false,
        enableGlow: false,
        enableParticles: false
      });
    } else if (currentFPS < targetFPS * 0.8) {
      // Moderate performance issues - moderate optimization
      this.updateConfig({
        maxNodes: Math.max(this.config.maxNodes * 0.75, 50),
        particleDensity: this.config.particleDensity === 'high' ? 'medium' : 'low',
        enableShadows: false
      });
    }
  }

  /**
   * Save configuration to localStorage
   */
  private saveConfigToStorage(): void {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return;
    }
    try {
      localStorage.setItem('cryptoBubbleMap_performanceConfig', JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save performance config to localStorage:', error);
    }
  }

  /**
   * Load configuration from localStorage
   */
  loadConfigFromStorage(): void {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return;
    }
    try {
      const saved = localStorage.getItem('cryptoBubbleMap_performanceConfig');
      if (saved) {
        const savedConfig = JSON.parse(saved);
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      console.warn('Failed to load performance config from localStorage:', error);
    }
  }

  /**
   * Reset to optimal configuration
   */
  resetToOptimal(): void {
    this.config = this.getOptimalConfig();
    this.saveConfigToStorage();
  }

  /**
   * Get performance recommendations
   */
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    const { fps, memoryUsage } = this.metrics;
    const { gpu, memory, isMobile } = this.capabilities;

    if (fps < this.config.frameRateLimit * 0.8) {
      recommendations.push('Consider reducing the maximum number of nodes or lowering rendering quality');
    }

    if (memoryUsage > 100) {
      recommendations.push('High memory usage detected. Consider reducing data cache limits');
    }

    if (gpu === 'low' && this.config.renderingQuality !== 'low') {
      recommendations.push('Your GPU may benefit from lower rendering quality settings');
    }

    if (isMobile && this.config.frameRateLimit > 60) {
      recommendations.push('Consider limiting frame rate to 60fps on mobile devices');
    }

    if (memory < 4 && this.config.maxNodes > 100) {
      recommendations.push('Limited system memory detected. Consider reducing maximum nodes');
    }

    return recommendations;
  }
}

/**
 * GPU Optimization Utilities
 * Handles LOD, culling, and adaptive rendering
 */
export class GPUOptimizer {
  private camera: { x: number; y: number; zoom: number } = { x: 0, y: 0, zoom: 1 };
  private viewport: { width: number; height: number } = { width: 0, height: 0 };
  private performanceConfig: PerformanceConfig;

  constructor(config: PerformanceConfig) {
    this.performanceConfig = config;
  }

  /**
   * Update camera and viewport information
   */
  updateCamera(camera: { x: number; y: number; zoom: number }, viewport: { width: number; height: number }) {
    this.camera = camera;
    this.viewport = viewport;
  }

  /**
   * Update performance configuration
   */
  updateConfig(config: PerformanceConfig) {
    this.performanceConfig = config;
  }

  /**
   * Calculate Level of Detail for a node based on distance and zoom
   */
  calculateLOD(nodePosition: { x: number; y: number }, nodeSize: number): 'high' | 'medium' | 'low' | 'hidden' {
    if (!this.performanceConfig.lodEnabled) return 'high';

    // Calculate distance from camera center
    const dx = nodePosition.x - this.camera.x;
    const dy = nodePosition.y - this.camera.y;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // Calculate screen size of the node
    const screenSize = (nodeSize * this.camera.zoom);

    // LOD thresholds based on screen size
    if (screenSize < 2) return 'hidden';
    if (screenSize < 8) return 'low';
    if (screenSize < 20) return 'medium';
    return 'high';
  }

  /**
   * Check if a node should be culled (not rendered)
   */
  shouldCullNode(nodePosition: { x: number; y: number }, nodeSize: number): boolean {
    if (!this.performanceConfig.cullingEnabled) return false;

    const margin = nodeSize * 2; // Add margin for smooth transitions
    const left = this.camera.x - (this.viewport.width / (2 * this.camera.zoom)) - margin;
    const right = this.camera.x + (this.viewport.width / (2 * this.camera.zoom)) + margin;
    const top = this.camera.y - (this.viewport.height / (2 * this.camera.zoom)) - margin;
    const bottom = this.camera.y + (this.viewport.height / (2 * this.camera.zoom)) + margin;

    return (
      nodePosition.x < left ||
      nodePosition.x > right ||
      nodePosition.y < top ||
      nodePosition.y > bottom
    );
  }

  /**
   * Get optimized rendering settings for a node
   */
  getNodeRenderSettings(nodePosition: { x: number; y: number }, nodeSize: number) {
    const lod = this.calculateLOD(nodePosition, nodeSize);
    const shouldCull = this.shouldCullNode(nodePosition, nodeSize);

    if (shouldCull) {
      return {
        render: false,
        lod: 'hidden' as const,
        enableShadows: false,
        enableGlow: false,
        enableParticles: false,
        animationQuality: 'none' as const
      };
    }

    const baseSettings = {
      render: true,
      lod,
      enableShadows: this.performanceConfig.enableShadows,
      enableGlow: this.performanceConfig.enableGlow,
      enableParticles: this.performanceConfig.enableParticles,
      animationQuality: 'full' as const
    };

    // Adjust settings based on LOD
    switch (lod) {
      case 'low':
        return {
          ...baseSettings,
          enableShadows: false,
          enableGlow: false,
          enableParticles: false,
          animationQuality: 'reduced' as const
        };
      case 'medium':
        return {
          ...baseSettings,
          enableShadows: false,
          enableParticles: this.performanceConfig.enableParticles && this.performanceConfig.particleDensity !== 'low',
          animationQuality: 'reduced' as const
        };
      case 'high':
        return baseSettings;
      default:
        return { ...baseSettings, render: false };
    }
  }

  /**
   * Calculate optimal particle count based on performance settings and screen area
   */
  getOptimalParticleCount(baseCount: number): number {
    if (!this.performanceConfig.enableParticles || this.performanceConfig.particleDensity === 'off') {
      return 0;
    }

    const densityMultipliers = {
      low: 0.25,
      medium: 0.5,
      high: 1.0
    };

    const densityMultiplier = densityMultipliers[this.performanceConfig.particleDensity];
    const screenArea = this.viewport.width * this.viewport.height;
    const normalizedArea = Math.min(screenArea / (1920 * 1080), 2); // Normalize to 1080p, cap at 2x

    return Math.floor(baseCount * densityMultiplier * normalizedArea);
  }

  /**
   * Get frame rate limiter settings
   */
  getFrameRateSettings() {
    return {
      targetFPS: this.performanceConfig.frameRateLimit,
      frameTime: 1000 / this.performanceConfig.frameRateLimit,
      adaptiveSync: this.performanceConfig.autoOptimize
    };
  }

  /**
   * Calculate dynamic quality scaling based on current performance
   */
  getDynamicQualityScale(currentFPS: number): number {
    const targetFPS = this.performanceConfig.frameRateLimit;
    const fpsRatio = currentFPS / targetFPS;

    if (fpsRatio >= 0.9) return 1.0; // Full quality
    if (fpsRatio >= 0.7) return 0.8; // Slight reduction
    if (fpsRatio >= 0.5) return 0.6; // Moderate reduction
    return 0.4; // Aggressive reduction
  }

  /**
   * Get memory-optimized buffer sizes
   */
  getOptimizedBufferSizes() {
    const baseBufferSize = this.performanceConfig.bufferSize;
    const maxNodes = this.performanceConfig.maxNodes;

    return {
      vertexBuffer: Math.min(maxNodes * 4, baseBufferSize), // 4 vertices per node
      indexBuffer: Math.min(maxNodes * 6, baseBufferSize * 1.5), // 6 indices per node
      instanceBuffer: maxNodes,
      particleBuffer: this.getOptimalParticleCount(1000)
    };
  }
}

/**
 * Memory Manager for efficient data handling
 */
export class MemoryManager {
  private cache: Map<string, { data: any; timestamp: number; size: number }> = new Map();
  private totalCacheSize: number = 0;
  private maxCacheSize: number;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(maxCacheSizeMB: number = 50) {
    this.maxCacheSize = maxCacheSizeMB * 1024 * 1024; // Convert to bytes
    this.startCleanupInterval();
  }

  /**
   * Store data in cache with automatic cleanup
   */
  set(key: string, data: any, estimatedSizeKB: number = 1): void {
    const size = estimatedSizeKB * 1024;

    // Remove existing entry if it exists
    if (this.cache.has(key)) {
      this.totalCacheSize -= this.cache.get(key)!.size;
    }

    // Add new entry
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      size
    });
    this.totalCacheSize += size;

    // Cleanup if over limit
    this.cleanup();
  }

  /**
   * Retrieve data from cache
   */
  get(key: string): any | null {
    const entry = this.cache.get(key);
    if (entry) {
      // Update timestamp for LRU
      entry.timestamp = Date.now();
      return entry.data;
    }
    return null;
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    return this.cache.has(key);
  }

  /**
   * Remove specific entry from cache
   */
  delete(key: string): boolean {
    const entry = this.cache.get(key);
    if (entry) {
      this.totalCacheSize -= entry.size;
      return this.cache.delete(key);
    }
    return false;
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear();
    this.totalCacheSize = 0;
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      entries: this.cache.size,
      totalSize: this.totalCacheSize,
      maxSize: this.maxCacheSize,
      utilization: (this.totalCacheSize / this.maxCacheSize) * 100
    };
  }

  /**
   * Update maximum cache size
   */
  setMaxSize(maxSizeMB: number): void {
    this.maxCacheSize = maxSizeMB * 1024 * 1024;
    this.cleanup();
  }

  /**
   * Cleanup old entries when cache is full
   */
  private cleanup(): void {
    if (this.totalCacheSize <= this.maxCacheSize) return;

    // Sort entries by timestamp (oldest first)
    const entries = Array.from(this.cache.entries()).sort(
      (a, b) => a[1].timestamp - b[1].timestamp
    );

    // Remove oldest entries until under limit
    for (const [key, entry] of entries) {
      if (this.totalCacheSize <= this.maxCacheSize * 0.8) break; // Leave 20% headroom

      this.cache.delete(key);
      this.totalCacheSize -= entry.size;
    }
  }

  /**
   * Start automatic cleanup interval
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      const now = Date.now();
      const maxAge = 5 * 60 * 1000; // 5 minutes

      Array.from(this.cache.entries()).forEach(([key, entry]) => {
        if (now - entry.timestamp > maxAge) {
          this.delete(key);
        }
      });
    }, 60000); // Run every minute
  }

  /**
   * Stop cleanup interval
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }
}

// Global performance manager instance (lazy-loaded for SSR compatibility)
let _performanceManager: PerformanceManager | null = null;

export const performanceManager = {
  getInstance(): PerformanceManager {
    if (!_performanceManager && typeof window !== 'undefined') {
      _performanceManager = new PerformanceManager();
    }
    return _performanceManager || new PerformanceManager(); // Fallback for SSR
  },

  // Proxy methods for convenience
  getConfig() { return this.getInstance().getConfig(); },
  updateConfig(config: Partial<PerformanceConfig>) { return this.getInstance().updateConfig(config); },
  getCapabilities() { return this.getInstance().getCapabilities(); },
  getMetrics() { return this.getInstance().getMetrics(); },
  updateMetrics(updates: Partial<PerformanceMetrics>) { return this.getInstance().updateMetrics(updates); },
  shouldOptimize() { return this.getInstance().shouldOptimize(); },
  autoOptimize() { return this.getInstance().autoOptimize(); },
  loadConfigFromStorage() { return this.getInstance().loadConfigFromStorage(); },
  resetToOptimal() { return this.getInstance().resetToOptimal(); },
  getRecommendations() { return this.getInstance().getRecommendations(); }
};
