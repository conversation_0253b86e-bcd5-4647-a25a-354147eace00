// Image cache and loading utilities for bubble visualization

interface CachedImage {
  image: HTMLImageElement;
  loaded: boolean;
  error: boolean;
  loading: boolean;
}

class ImageCache {
  private cache: Map<string, CachedImage> = new Map();
  private loadingPromises: Map<string, Promise<HTMLImageElement>> = new Map();

  /**
   * Load an image and cache it
   */
  async loadImage(url: string): Promise<HTMLImageElement | null> {
    // Check if already cached
    const cached = this.cache.get(url);
    if (cached) {
      if (cached.loaded) return cached.image;
      if (cached.error) return null;
      if (cached.loading) {
        // Wait for existing loading promise
        const existingPromise = this.loadingPromises.get(url);
        if (existingPromise) {
          try {
            return await existingPromise;
          } catch {
            return null;
          }
        }
      }
    }

    // Create loading promise
    const loadingPromise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous'; // Enable CORS for external images
      
      // Set cache entry as loading
      this.cache.set(url, {
        image: img,
        loaded: false,
        error: false,
        loading: true
      });

      img.onload = () => {
        this.cache.set(url, {
          image: img,
          loaded: true,
          error: false,
          loading: false
        });
        this.loadingPromises.delete(url);
        resolve(img);
      };

      img.onerror = () => {
        this.cache.set(url, {
          image: img,
          loaded: false,
          error: true,
          loading: false
        });
        this.loadingPromises.delete(url);
        reject(new Error(`Failed to load image: ${url}`));
      };

      img.src = url;
    });

    this.loadingPromises.set(url, loadingPromise);

    try {
      return await loadingPromise;
    } catch {
      return null;
    }
  }

  /**
   * Get cached image if available
   */
  getCachedImage(url: string): HTMLImageElement | null {
    const cached = this.cache.get(url);
    return cached && cached.loaded ? cached.image : null;
  }

  /**
   * Check if image is loaded
   */
  isImageLoaded(url: string): boolean {
    const cached = this.cache.get(url);
    return cached ? cached.loaded : false;
  }

  /**
   * Check if image failed to load
   */
  isImageError(url: string): boolean {
    const cached = this.cache.get(url);
    return cached ? cached.error : false;
  }

  /**
   * Check if image is currently loading
   */
  isImageLoading(url: string): boolean {
    const cached = this.cache.get(url);
    return cached ? cached.loading : false;
  }

  /**
   * Preload multiple images
   */
  async preloadImages(urls: string[]): Promise<void> {
    const promises = urls.map(url => this.loadImage(url).catch(() => null));
    await Promise.all(promises);
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
    this.loadingPromises.clear();
  }

  /**
   * Get cache size
   */
  getCacheSize(): number {
    return this.cache.size;
  }

  /**
   * Remove specific image from cache
   */
  removeFromCache(url: string): void {
    this.cache.delete(url);
    this.loadingPromises.delete(url);
  }
}

// Singleton instance
export const imageCache = new ImageCache();

/**
 * Create a circular clipping path for images
 */
export function createCircularClip(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  radius: number
): void {
  ctx.save();
  ctx.beginPath();
  ctx.arc(x, y, radius, 0, Math.PI * 2);
  ctx.clip();
}

/**
 * Draw image in a circular frame with fallback
 */
export function drawCircularImage(
  ctx: CanvasRenderingContext2D,
  image: HTMLImageElement,
  x: number,
  y: number,
  radius: number,
  fallbackColor?: string
): void {
  try {
    // Create circular clipping path
    createCircularClip(ctx, x, y, radius);
    
    // Calculate image dimensions to fit in circle
    const size = radius * 2;
    const imgX = x - radius;
    const imgY = y - radius;
    
    // Draw image
    ctx.drawImage(image, imgX, imgY, size, size);
    
    // Restore context
    ctx.restore();
    
    // Draw border
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.stroke();
  } catch (error) {
    console.warn('Error drawing circular image:', error);
    ctx.restore();
    
    // Draw fallback circle
    if (fallbackColor) {
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, Math.PI * 2);
      ctx.fillStyle = fallbackColor;
      ctx.fill();
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.stroke();
    }
  }
}

/**
 * Generate placeholder image data URL
 */
export function generatePlaceholderImage(
  size: number = 100,
  backgroundColor: string = '#3B82F6',
  textColor: string = '#FFFFFF',
  text: string = '?'
): string {
  const canvas = document.createElement('canvas');
  canvas.width = size;
  canvas.height = size;
  const ctx = canvas.getContext('2d');
  
  if (!ctx) return '';
  
  // Draw background
  ctx.fillStyle = backgroundColor;
  ctx.fillRect(0, 0, size, size);
  
  // Draw text
  ctx.fillStyle = textColor;
  ctx.font = `${size * 0.4}px Arial`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(text, size / 2, size / 2);
  
  return canvas.toDataURL();
}

export default imageCache;
