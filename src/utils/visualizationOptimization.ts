// Professional visualization optimization utilities for crypto bubble maps

export interface PerformanceMetrics {
  fps: number;
  nodeCount: number;
  renderTime: number;
  memoryUsage: number;
}

export interface OptimizationConfig {
  maxNodes: number;
  lodThreshold: number;
  animationThreshold: number;
  renderDistance: number;
  targetFPS: number;
}

export class VisualizationOptimizer {
  private frameCount = 0;
  private lastFPSCheck = 0;
  private currentFPS = 60;
  private renderTimes: number[] = [];
  private config: OptimizationConfig;

  constructor(config: OptimizationConfig) {
    this.config = config;
  }

  // Monitor performance metrics
  updatePerformanceMetrics(renderStartTime: number): PerformanceMetrics {
    const renderTime = performance.now() - renderStartTime;
    this.renderTimes.push(renderTime);
    
    // Keep only last 60 render times for FPS calculation
    if (this.renderTimes.length > 60) {
      this.renderTimes.shift();
    }

    // Calculate FPS
    this.frameCount++;
    const now = performance.now();
    if (now - this.lastFPSCheck >= 1000) {
      this.currentFPS = this.frameCount;
      this.frameCount = 0;
      this.lastFPSCheck = now;
    }

    return {
      fps: this.currentFPS,
      nodeCount: 0, // Will be set by caller
      renderTime: renderTime,
      memoryUsage: this.getMemoryUsage()
    };
  }

  // Get memory usage (if available)
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  }

  // Determine if performance mode should be enabled
  shouldEnablePerformanceMode(nodeCount: number): boolean {
    return (
      nodeCount > this.config.animationThreshold ||
      this.currentFPS < this.config.targetFPS ||
      this.getAverageRenderTime() > 16 // 60fps = 16ms per frame
    );
  }

  // Get average render time
  private getAverageRenderTime(): number {
    if (this.renderTimes.length === 0) return 0;
    return this.renderTimes.reduce((a, b) => a + b, 0) / this.renderTimes.length;
  }

  // Level of Detail (LOD) optimization
  shouldRenderNode(nodeSize: number, zoomLevel: number, distance?: number): boolean {
    const effectiveSize = nodeSize * zoomLevel;
    
    // Skip very small nodes
    if (effectiveSize < 1) return false;
    
    // Skip distant nodes if distance is provided
    if (distance && distance > this.config.renderDistance) return false;
    
    return true;
  }

  // Optimize node rendering based on performance
  getOptimizedRenderSettings(nodeCount: number, zoomLevel: number) {
    const performanceMode = this.shouldEnablePerformanceMode(nodeCount);
    
    return {
      performanceMode,
      enableAnimations: !performanceMode,
      enableGlow: !performanceMode && zoomLevel > 0.5,
      enableParticles: !performanceMode && nodeCount < 100,
      enableLabels: zoomLevel > 0.3,
      enableShadows: !performanceMode && zoomLevel > 0.7,
      maxAnimatedNodes: performanceMode ? 50 : 200,
      simplifiedRendering: performanceMode
    };
  }

  // Batch processing for large datasets
  processBatch<T>(items: T[], batchSize: number, processor: (batch: T[]) => void): Promise<void> {
    return new Promise((resolve) => {
      let index = 0;
      
      const processBatch = () => {
        const batch = items.slice(index, index + batchSize);
        if (batch.length === 0) {
          resolve();
          return;
        }
        
        processor(batch);
        index += batchSize;
        
        // Use requestAnimationFrame to avoid blocking the UI
        requestAnimationFrame(processBatch);
      };
      
      processBatch();
    });
  }

  // Spatial indexing for efficient collision detection
  createSpatialIndex<T extends { x: number; y: number; size?: number }>(
    items: T[], 
    cellSize: number = 100
  ): Map<string, T[]> {
    const index = new Map<string, T[]>();
    
    items.forEach(item => {
      const cellX = Math.floor(item.x / cellSize);
      const cellY = Math.floor(item.y / cellSize);
      const key = `${cellX},${cellY}`;
      
      if (!index.has(key)) {
        index.set(key, []);
      }
      index.get(key)!.push(item);
    });
    
    return index;
  }

  // Get nearby items using spatial index
  getNearbyItems<T extends { x: number; y: number; size?: number }>(
    spatialIndex: Map<string, T[]>,
    x: number,
    y: number,
    radius: number,
    cellSize: number = 100
  ): T[] {
    const nearby: T[] = [];
    const cellRadius = Math.ceil(radius / cellSize);
    const centerCellX = Math.floor(x / cellSize);
    const centerCellY = Math.floor(y / cellSize);
    
    for (let dx = -cellRadius; dx <= cellRadius; dx++) {
      for (let dy = -cellRadius; dy <= cellRadius; dy++) {
        const key = `${centerCellX + dx},${centerCellY + dy}`;
        const cellItems = spatialIndex.get(key);
        if (cellItems) {
          nearby.push(...cellItems);
        }
      }
    }
    
    return nearby;
  }

  // Optimize animation frame rate based on performance
  getOptimalFrameRate(): number {
    if (this.currentFPS >= 55) return 60;
    if (this.currentFPS >= 25) return 30;
    return 15; // Fallback for very poor performance
  }

  // Memory cleanup utilities
  cleanup(): void {
    this.renderTimes = [];
    this.frameCount = 0;
  }
}

// Color interpolation for smooth transitions
export function interpolateColor(color1: string, color2: string, factor: number): string {
  // Simple RGB interpolation
  const hex1 = color1.replace('#', '');
  const hex2 = color2.replace('#', '');
  
  const r1 = parseInt(hex1.substr(0, 2), 16);
  const g1 = parseInt(hex1.substr(2, 2), 16);
  const b1 = parseInt(hex1.substr(4, 2), 16);
  
  const r2 = parseInt(hex2.substr(0, 2), 16);
  const g2 = parseInt(hex2.substr(2, 2), 16);
  const b2 = parseInt(hex2.substr(4, 2), 16);
  
  const r = Math.round(r1 + (r2 - r1) * factor);
  const g = Math.round(g1 + (g2 - g1) * factor);
  const b = Math.round(b1 + (b2 - b1) * factor);
  
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

// Easing functions for smooth animations
export const easingFunctions = {
  easeInOut: (t: number): number => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  easeOut: (t: number): number => t * (2 - t),
  easeIn: (t: number): number => t * t,
  bounce: (t: number): number => {
    if (t < 1 / 2.75) return 7.5625 * t * t;
    if (t < 2 / 2.75) return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
    if (t < 2.5 / 2.75) return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
    return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
  }
};

// Default optimization configuration
export const DEFAULT_OPTIMIZATION_CONFIG: OptimizationConfig = {
  maxNodes: 1000,
  lodThreshold: 500,
  animationThreshold: 100,
  renderDistance: 2000,
  targetFPS: 30
};
