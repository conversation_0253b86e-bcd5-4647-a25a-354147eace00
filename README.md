# Crypto Bubble Map

A visualization tool for displaying relationships between cryptocurrency wallet addresses on the blockchain, similar to [Bubblemaps.io](https://bubblemaps.io/).

![Crypto Bubble Map](https://via.placeholder.com/800x400?text=Crypto+Bubble+Map)

## Features

- Interactive bubble map visualization of crypto wallet relationships
- Search for specific wallet addresses to view their transaction networks
- Visualize connections between wallets with customizable depth
- View detailed information about wallets including balance and transaction count
- Responsive design that works on both desktop and mobile devices

## Technology Stack

- **Frontend**: React, Next.js, TypeScript, Tailwind CSS
- **Visualization**: D3.js, React Force Graph, Three.js
- **Backend Integration**: Neo4j graph database (pre-existing)
- **API Communication**: Axios

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- NPM or Yarn
- A running Neo4j database with wallet transaction data

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/HoiAnHub/crypto-bubble-map.git
   cd crypto-bubble-map
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env.local` file in the root directory with your Neo4j connection details:
   ```
   NEXT_PUBLIC_API_URL=http://localhost:3001/api
   NEXT_PUBLIC_NEO4J_URI=neo4j://localhost:7687
   NEXT_PUBLIC_NEO4J_USER=neo4j
   NEXT_PUBLIC_NEO4J_PASSWORD=your_password
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Backend Integration

This application is designed to work with a Neo4j database that contains wallet and transaction data. The backend should provide the following API endpoints:

- `GET /api/wallets/network?address={address}&depth={depth}` - Get the wallet network for a specific address
- `GET /api/wallets/{address}` - Get details about a specific wallet
- `GET /api/wallets/search?q={query}` - Search for wallets by address or label
- `GET /api/wallets/{address}/transactions?limit={limit}` - Get transaction history for a wallet

The data model in Neo4j should include:

- Wallet nodes with properties like address, balance, label, txCount, etc.
- TRANSFERS relationships between wallets with properties like value, timestamp, etc.

## Development

### Project Structure

```
crypto-bubble-map/
├── public/                 # Static files
├── src/
│   ├── components/         # React components
│   ├── pages/              # Next.js pages
│   ├── services/           # API services
│   ├── styles/             # Global styles
│   └── utils/              # Utility functions
├── .env.local              # Environment variables (create this)
├── next.config.js          # Next.js configuration
├── package.json            # Project dependencies
├── tailwind.config.js      # Tailwind CSS configuration
└── tsconfig.json           # TypeScript configuration
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Inspired by [Bubblemaps.io](https://bubblemaps.io/)
- Built for visualizing blockchain wallet relationships